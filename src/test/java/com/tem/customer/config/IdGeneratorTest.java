package com.tem.customer.config;

import com.baomidou.mybatisplus.core.incrementer.IdentifierGenerator;
import com.tem.customer.repository.entity.PartnerNote;
import com.tem.customer.repository.mapper.PartnerNoteMapper;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.transaction.annotation.Transactional;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ID 生成器测试
 * 验证 MyBatis Plus 的自定义 ID 生成器是否正常工作
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
@Slf4j
@SpringBootTest
@Transactional
public class IdGeneratorTest {

    @Autowired
    private IdentifierGenerator identifierGenerator;
    
    @Autowired
    private PartnerNoteMapper partnerNoteMapper;

    /**
     * 测试 IdentifierGenerator Bean 是否正确注入
     */
    @Test
    public void testIdentifierGeneratorBean() {
        assertNotNull(identifierGenerator, "IdentifierGenerator 应该被正确注入");
        
        // 测试生成 ID
        Number id = identifierGenerator.nextId(null);
        assertNotNull(id, "生成的 ID 不应为 null");
        assertTrue(id.longValue() > 0, "生成的 ID 应该大于 0");
        
        log.info("通过 IdentifierGenerator 生成的 ID: {}", id);
    }

    /**
     * 测试实体插入时 ID 是否自动生成
     */
    @Test
    public void testEntityInsertWithAutoId() {
        PartnerNote note = new PartnerNote();
        note.setPartnerId(1L);
        note.setTitle("测试自动 ID 生成");
        note.setContent("这是一个测试，验证 ID 是否能够自动生成");
        note.setSortOrder(1);
        
        // 插入前 ID 应该为 null
        assertNull(note.getId(), "插入前 ID 应该为 null");
        
        // 执行插入
        int result = partnerNoteMapper.insert(note);
        assertEquals(1, result, "插入应该成功");
        
        // 插入后 ID 应该被自动生成并回写
        assertNotNull(note.getId(), "插入后 ID 应该被自动生成");
        assertTrue(note.getId() > 0, "生成的 ID 应该大于 0");
        
        log.info("插入成功，自动生成的 ID: {}", note.getId());
        
        // 验证 ID 的长度（雪花算法生成的 ID 通常是 18-19 位）
        String idStr = String.valueOf(note.getId());
        assertTrue(idStr.length() >= 17 && idStr.length() <= 19, 
                "雪花算法生成的 ID 长度应该在 17-19 位之间，实际长度: " + idStr.length());
    }

    /**
     * 测试批量插入时 ID 的唯一性
     */
    @Test
    public void testBatchInsertIdUniqueness() {
        Long[] ids = new Long[10];
        
        for (int i = 0; i < 10; i++) {
            PartnerNote note = new PartnerNote();
            note.setPartnerId(1L);
            note.setTitle("批量测试 " + (i + 1));
            note.setContent("批量测试内容 " + (i + 1));
            note.setSortOrder(i + 1);
            
            partnerNoteMapper.insert(note);
            ids[i] = note.getId();
            
            assertNotNull(ids[i], "第 " + (i + 1) + " 条记录的 ID 不应为 null");
        }
        
        // 验证 ID 的唯一性
        for (int i = 0; i < ids.length; i++) {
            for (int j = i + 1; j < ids.length; j++) {
                assertNotEquals(ids[i], ids[j], 
                    String.format("ID 应该是唯一的，但是第 %d 和第 %d 条记录的 ID 相同: %d", 
                        i + 1, j + 1, ids[i]));
            }
        }
        
        log.info("批量插入测试成功，所有 ID 都是唯一的");
    }
}
