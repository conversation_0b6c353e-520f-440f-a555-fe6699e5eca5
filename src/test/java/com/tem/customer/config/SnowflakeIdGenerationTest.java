package com.tem.customer.config;

import com.iplatform.common.idgen.ShortIdWorker;
import com.tem.customer.repository.entity.OperationLog;
import com.tem.customer.repository.entity.PartnerNote;
import com.tem.customer.repository.entity.PartnerWechatGroup;
import com.tem.customer.repository.entity.WechatUserBinding;
import com.tem.customer.repository.mapper.OperationLogMapper;
import com.tem.customer.repository.mapper.PartnerNoteMapper;
import com.tem.customer.repository.mapper.PartnerWechatGroupMapper;
import com.tem.customer.repository.mapper.WechatUserBindingMapper;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.LocalDateTime;

/**
 * 雪花算法ID生成测试类
 * 用于验证数据库主键改造后的ID生成是否正常工作
 *
 * <AUTHOR>
 * @since 2025-07-11
 */
@Slf4j
@SpringBootTest
//@ActiveProfiles("test")
//@Transactional
public class SnowflakeIdGenerationTest {

    @Autowired
    private ShortIdWorker shortIdWorker;

    @Autowired
    private OperationLogMapper operationLogMapper;

    @Autowired
    private PartnerNoteMapper partnerNoteMapper;

    @Autowired
    private PartnerWechatGroupMapper partnerWechatGroupMapper;

    @Autowired
    private WechatUserBindingMapper wechatUserBindingMapper;

    /**
     * 测试雪花算法ID生成器是否正常工作
     */
    @Test
    public void testSnowflakeIdGenerator() {
        log.info("开始测试雪花算法ID生成器");

        // 生成10个ID，验证格式和唯一性
        for (int i = 0; i < 10; i++) {
            Long id = shortIdWorker.nextId();
            log.info("生成的雪花算法ID[{}]: {}", i + 1, id);
            
            // 验证ID格式：应该是18-19位的长整型
            assert id != null;
            assert id > 0;
            assert String.valueOf(id).length() >= 17;
            assert String.valueOf(id).length() <= 18;
        }

        log.info("雪花算法ID生成器测试完成");
    }

    /**
     * 测试操作日志表的ID自动生成
     */
    @Test
    public void testOperationLogIdGeneration() {
        log.info("开始测试操作日志表ID自动生成");

        OperationLog operationLog = new OperationLog();
        operationLog.setBusinessType("TEST");
        operationLog.setBusinessId(12345L);
        operationLog.setOperationType("CREATE");
        operationLog.setOperationDesc("测试雪花算法ID生成");
        operationLog.setOperatorId(1L);
        operationLog.setOperatorName("测试用户");
        operationLog.setOperatorUsername("test_user");
        operationLog.setPartnerId(1L);
        operationLog.setIpAddress("127.0.0.1");
        operationLog.setUserAgent("Test Agent");
        operationLog.setRequestUri("/test");
        operationLog.setRequestMethod("POST");
        operationLog.setExecutionTime(100);
        operationLog.setCreateTime(LocalDateTime.now());
        operationLog.setCreateBy("test");

        // 插入前ID应该为null
        assert operationLog.getId() == null;

        int result = operationLogMapper.insert(operationLog);
        assert result == 1;

        // 插入后ID应该被自动生成
        assert operationLog.getId() != null;
        assert operationLog.getId() > 0;
        assert String.valueOf(operationLog.getId()).length() >= 17;

        log.info("操作日志插入成功，生成的ID: {}", operationLog.getId());
    }

    /**
     * 测试合作伙伴备注表的ID自动生成
     */
    @Test
    public void testPartnerNoteIdGeneration() {
        log.info("开始测试合作伙伴备注表ID自动生成");

        PartnerNote partnerNote = new PartnerNote();
        partnerNote.setPartnerId(1L);
        partnerNote.setTitle("测试备注");
        partnerNote.setContent("这是一个测试备注，用于验证雪花算法ID生成");
        partnerNote.setSortOrder(1);
        partnerNote.setCreateTime(LocalDateTime.now());
        partnerNote.setUpdateTime(LocalDateTime.now());
        partnerNote.setCreateBy("test");
        partnerNote.setUpdateBy("test");
        partnerNote.setDeleted(0);
        partnerNote.setVersion(1);

        // 插入前ID应该为null
        assert partnerNote.getId() == null;

        int result = partnerNoteMapper.insert(partnerNote);
        assert result == 1;

        // 插入后ID应该被自动生成
        assert partnerNote.getId() != null;
        assert partnerNote.getId() > 0;
        assert String.valueOf(partnerNote.getId()).length() >= 18;

        log.info("合作伙伴备注插入成功，生成的ID: {}", partnerNote.getId());
    }

    /**
     * 测试合作伙伴微信群表的ID自动生成
     */
    @Test
    public void testPartnerWechatGroupIdGeneration() {
        log.info("开始测试合作伙伴微信群表ID自动生成");

        PartnerWechatGroup wechatGroup = new PartnerWechatGroup();
        wechatGroup.setPartnerId(1L);
        wechatGroup.setChatId("test_chat_id_" + System.currentTimeMillis());
        wechatGroup.setGroupType("CUSTOMER_SERVICE");
        wechatGroup.setStatus(1);
        wechatGroup.setSortOrder(1);
        wechatGroup.setRemark("测试微信群备注");
        wechatGroup.setCreateTime(LocalDateTime.now());
        wechatGroup.setUpdateTime(LocalDateTime.now());
        wechatGroup.setCreateBy("test");
        wechatGroup.setUpdateBy("test");
        wechatGroup.setDeleted(0);
        wechatGroup.setVersion(1);

        // 插入前ID应该为null
        assert wechatGroup.getId() == null;

        int result = partnerWechatGroupMapper.insert(wechatGroup);
        assert result == 1;

        // 插入后ID应该被自动生成
        assert wechatGroup.getId() != null;
        assert wechatGroup.getId() > 0;
        assert String.valueOf(wechatGroup.getId()).length() >= 18;

        log.info("合作伙伴微信群插入成功，生成的ID: {}", wechatGroup.getId());
    }

    /**
     * 测试微信用户绑定表的ID自动生成
     */
    @Test
    public void testWechatUserBindingIdGeneration() {
        log.info("开始测试微信用户绑定表ID自动生成");

        WechatUserBinding userBinding = new WechatUserBinding();
        userBinding.setPartnerId(1L);
        userBinding.setUnionId("test_union_id_" + System.currentTimeMillis());
        userBinding.setSourceType("WECHAT_WORK");
        userBinding.setSourceAppId("test_app_id");
        userBinding.setUserId(1L);
        userBinding.setStatus(1);
        userBinding.setRemark("测试绑定关系");
        userBinding.setCreateTime(LocalDateTime.now());
        userBinding.setUpdateTime(LocalDateTime.now());
        userBinding.setCreateBy("test");
        userBinding.setUpdateBy("test");
        userBinding.setDeleted(0);
        userBinding.setVersion(1);

        // 插入前ID应该为null
        assert userBinding.getId() == null;

        int result = wechatUserBindingMapper.insert(userBinding);
        assert result == 1;

        // 插入后ID应该被自动生成
        assert userBinding.getId() != null;
        assert userBinding.getId() > 0;
        assert String.valueOf(userBinding.getId()).length() >= 18;

        log.info("微信用户绑定插入成功，生成的ID: {}", userBinding.getId());
    }

    /**
     * 测试批量插入时ID的唯一性
     */
    @Test
    public void testBatchInsertIdUniqueness() {
        log.info("开始测试批量插入时ID的唯一性");

        // 批量插入多条操作日志，验证ID的唯一性
        for (int i = 0; i < 5; i++) {
            OperationLog operationLog = new OperationLog();
            operationLog.setBusinessType("BATCH_TEST");
            operationLog.setBusinessId((long) (i + 1));
            operationLog.setOperationType("CREATE");
            operationLog.setOperationDesc("批量测试雪花算法ID生成 - " + (i + 1));
            operationLog.setOperatorId(1L);
            operationLog.setOperatorName("测试用户");
            operationLog.setOperatorUsername("test_user");
            operationLog.setPartnerId(1L);
            operationLog.setIpAddress("127.0.0.1");
            operationLog.setUserAgent("Test Agent");
            operationLog.setRequestUri("/test/batch");
            operationLog.setRequestMethod("POST");
            operationLog.setExecutionTime(100);
            operationLog.setCreateTime(LocalDateTime.now());
            operationLog.setCreateBy("test");

            int result = operationLogMapper.insert(operationLog);
            assert result == 1;
            assert operationLog.getId() != null;

            log.info("批量插入第{}条记录，生成的ID: {}", i + 1, operationLog.getId());
        }

        log.info("批量插入ID唯一性测试完成");
    }
}
