package com.tem.customer.mapper;


import com.iplatform.common.utils.LogUtils;
import com.tem.customer.repository.entity.PartnerNote;
import com.tem.customer.repository.mapper.PartnerNoteMapper;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.LocalDateTime;


@Slf4j
@SpringBootTest
class PartnerNoteMapperTest {


    @Autowired
    private PartnerNoteMapper partnerNoteMapper;

    @Test
    public void t1() {

        PartnerNote partnerNote = new PartnerNote();
        partnerNote.setPartnerId(1L);
        partnerNote.setTitle("测试备注");
        partnerNote.setContent("这是一个测试备注，用于验证雪花算法ID生成");
        partnerNote.setSortOrder(1);
        partnerNote.setCreateTime(LocalDateTime.now());
        partnerNote.setUpdateTime(LocalDateTime.now());
        partnerNote.setCreateBy("test");
        partnerNote.setUpdateBy("test");
        partnerNote.setDeleted(0);
        partnerNote.setVersion(1);
        int insert = partnerNoteMapper.insert(partnerNote);
        LogUtils.info(log, "partnerNode:{}", partnerNote);
    }
}