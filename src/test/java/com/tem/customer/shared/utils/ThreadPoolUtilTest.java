package com.tem.customer.shared.utils;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.MDC;

import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * ThreadPoolUtil 单元测试
 * 测试线程池工具类的线程池管理、TraceId传递等功能
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("线程池工具类测试")
class ThreadPoolUtilTest {

    @Mock
    private ExecutorService mockExecutorService;

    @Mock
    private ThreadPoolExecutor mockThreadPoolExecutor;

    @Mock
    private Future<String> mockFuture;

    private ThreadPoolUtil threadPoolUtil;

    @BeforeEach
    void setUp() {
        threadPoolUtil = new ThreadPoolUtil(mockExecutorService);
        threadPoolUtil.init(); // 初始化静态实例
    }

    @Nested
    @DisplayName("基本功能测试")
    class BasicFunctionalityTests {

        @Test
        @DisplayName("获取线程池执行器")
        void testGetExecutor() {
            ExecutorService executor = ThreadPoolUtil.getExecutor();
            assertNotNull(executor);
            assertEquals(mockExecutorService, executor);
        }

        @Test
        @DisplayName("初始化和销毁")
        void testInitAndDestroy() {
            // 测试初始化
            ThreadPoolUtil newUtil = new ThreadPoolUtil(mockExecutorService);
            assertDoesNotThrow(newUtil::init);

            // 测试销毁
            assertDoesNotThrow(newUtil::destroy);
        }
    }

    @Nested
    @DisplayName("任务提交测试")
    class TaskSubmissionTests {

        @Test
        @DisplayName("提交Runnable任务")
        void testSubmitRunnable() {
            Runnable task = () -> System.out.println("Test task");
            when(mockExecutorService.submit(any(Runnable.class))).thenReturn(mockFuture);

            Future<?> result = ThreadPoolUtil.submit(task);

            assertNotNull(result);
            verify(mockExecutorService).submit(any(Runnable.class));
        }

        @Test
        @DisplayName("提交Callable任务")
        void testSubmitCallable() {
            Callable<String> task = () -> "Test result";
            when(mockExecutorService.submit(any(Callable.class))).thenReturn(mockFuture);

            Future<String> result = ThreadPoolUtil.submit(task);

            assertNotNull(result);
            verify(mockExecutorService).submit(any(Callable.class));
        }

        @Test
        @DisplayName("执行Runnable任务")
        void testExecuteRunnable() {
            Runnable task = () -> System.out.println("Test task");

            ThreadPoolUtil.execute(task);

            verify(mockExecutorService).execute(any(Runnable.class));
        }

        @Test
        @DisplayName("提交原始Runnable任务（不包装TraceId）")
        void testSubmitRawRunnable() {
            Runnable task = () -> System.out.println("Raw task");
            when(mockExecutorService.submit(task)).thenReturn(mockFuture);

            Future<?> result = ThreadPoolUtil.submitRaw(task);

            assertNotNull(result);
            verify(mockExecutorService).submit(task);
        }

        @Test
        @DisplayName("提交原始Callable任务（不包装TraceId）")
        void testSubmitRawCallable() {
            Callable<String> task = () -> "Raw result";
            when(mockExecutorService.submit(task)).thenReturn(mockFuture);

            Future<String> result = ThreadPoolUtil.submitRaw(task);

            assertNotNull(result);
            verify(mockExecutorService).submit(task);
        }

        @Test
        @DisplayName("执行原始Runnable任务（不包装TraceId）")
        void testExecuteRawRunnable() {
            Runnable task = () -> System.out.println("Raw task");

            ThreadPoolUtil.executeRaw(task);

            verify(mockExecutorService).execute(task);
        }
    }

    @Nested
    @DisplayName("指定TraceId任务提交测试")
    class TraceIdTaskSubmissionTests {

        @Test
        @DisplayName("使用指定TraceId提交Runnable任务")
        void testSubmitWithTraceIdRunnable() {
            Runnable task = () -> System.out.println("TraceId task");
            String traceId = "test-trace-id";
            when(mockExecutorService.submit(any(Runnable.class))).thenReturn(mockFuture);

            Future<?> result = ThreadPoolUtil.submitWithTraceId(task, traceId);

            assertNotNull(result);
            verify(mockExecutorService).submit(any(Runnable.class));
        }

        @Test
        @DisplayName("使用指定TraceId提交Callable任务")
        void testSubmitWithTraceIdCallable() {
            Callable<String> task = () -> "TraceId result";
            String traceId = "test-trace-id";
            when(mockExecutorService.submit(any(Callable.class))).thenReturn(mockFuture);

            Future<String> result = ThreadPoolUtil.submitWithTraceId(task, traceId);

            assertNotNull(result);
            verify(mockExecutorService).submit(any(Callable.class));
        }

        @Test
        @DisplayName("使用指定TraceId执行Runnable任务")
        void testExecuteWithTraceId() {
            Runnable task = () -> System.out.println("TraceId task");
            String traceId = "test-trace-id";

            ThreadPoolUtil.executeWithTraceId(task, traceId);

            verify(mockExecutorService).execute(any(Runnable.class));
        }
    }

    @Nested
    @DisplayName("线程池状态测试")
    class ThreadPoolStatusTests {

        @Test
        @DisplayName("获取ThreadPoolExecutor状态")
        void testGetThreadPoolStatusWithThreadPoolExecutor() {
            // 使用真实的ThreadPoolExecutor进行测试
            ThreadPoolExecutor realExecutor = new ThreadPoolExecutor(
                2, 4, 60L, TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(10)
            );

            ThreadPoolUtil realUtil = new ThreadPoolUtil(realExecutor);
            realUtil.init();

            ThreadPoolUtil.ThreadPoolStatus status = ThreadPoolUtil.getThreadPoolStatus();

            assertNotNull(status);
            assertEquals("DEFAULT", status.getPoolName());
            assertEquals(2, status.getCorePoolSize());
            assertEquals(4, status.getMaximumPoolSize());
            assertFalse(status.isShutdown());
            assertFalse(status.isTerminated());

            realExecutor.shutdown();
        }

        @Test
        @DisplayName("获取非ThreadPoolExecutor状态")
        void testGetThreadPoolStatusWithNonThreadPoolExecutor() {
            ThreadPoolUtil.ThreadPoolStatus status = ThreadPoolUtil.getThreadPoolStatus();
            // 由于mockExecutorService不是ThreadPoolExecutor，应该返回null
            assertNull(status);
        }

        @Test
        @DisplayName("线程池状态计算方法")
        void testThreadPoolStatusCalculations() {
            ThreadPoolUtil.ThreadPoolStatus status = ThreadPoolUtil.ThreadPoolStatus.builder()
                .poolName("TEST")
                .corePoolSize(2)
                .maximumPoolSize(10)
                .activeCount(5)
                .queueSize(3)
                .build();

            // 测试线程池使用率
            assertEquals(0.5, status.getPoolUsageRate(), 0.01);

            // 测试队列使用率
            double expectedQueueUsage = 3.0 / (3 + 10);
            assertEquals(expectedQueueUsage, status.getQueueUsageRate(), 0.01);
        }

        @Test
        @DisplayName("线程池状态边界情况")
        void testThreadPoolStatusEdgeCases() {
            // 测试最大线程数为0的情况
            ThreadPoolUtil.ThreadPoolStatus status1 = ThreadPoolUtil.ThreadPoolStatus.builder()
                .maximumPoolSize(0)
                .activeCount(5)
                .build();
            assertEquals(0.0, status1.getPoolUsageRate());

            // 测试队列大小为0的情况
            ThreadPoolUtil.ThreadPoolStatus status2 = ThreadPoolUtil.ThreadPoolStatus.builder()
                .queueSize(0)
                .maximumPoolSize(10)
                .build();
            assertEquals(0.0, status2.getQueueUsageRate());
        }
    }

    @Nested
    @DisplayName("自定义线程工厂测试")
    class CustomThreadFactoryTests {

        @Test
        @DisplayName("默认线程工厂")
        void testDefaultThreadFactory() {
            ThreadPoolUtil.BenefitsThreadFactory factory = 
                new ThreadPoolUtil.BenefitsThreadFactory("test-pool");

            Runnable task = () -> {};
            Thread thread = factory.newThread(task);

            assertNotNull(thread);
            assertTrue(thread.getName().startsWith("test-pool-"));
            assertEquals(Thread.NORM_PRIORITY, thread.getPriority());
            assertFalse(thread.isDaemon());
        }

        @Test
        @DisplayName("自定义参数线程工厂")
        void testCustomThreadFactory() {
            ThreadPoolUtil.BenefitsThreadFactory factory = 
                new ThreadPoolUtil.BenefitsThreadFactory("custom-pool", Thread.MAX_PRIORITY, true);

            Runnable task = () -> {};
            Thread thread = factory.newThread(task);

            assertNotNull(thread);
            assertTrue(thread.getName().startsWith("custom-pool-"));
            assertEquals(Thread.MAX_PRIORITY, thread.getPriority());
            assertTrue(thread.isDaemon());
        }

        @Test
        @DisplayName("线程工厂线程编号递增")
        void testThreadFactoryNumberIncrement() {
            ThreadPoolUtil.BenefitsThreadFactory factory = 
                new ThreadPoolUtil.BenefitsThreadFactory("numbered-pool");

            Runnable task = () -> {};
            Thread thread1 = factory.newThread(task);
            Thread thread2 = factory.newThread(task);

            assertTrue(thread1.getName().contains("-1"));
            assertTrue(thread2.getName().contains("-2"));
        }
    }

    @Nested
    @DisplayName("TraceId传递集成测试")
    class TraceIdIntegrationTests {

        @Test
        @DisplayName("TraceId在异步任务中传递")
        void testTraceIdPropagation() throws Exception {
            // 使用真实的线程池进行集成测试
            ExecutorService realExecutor = Executors.newFixedThreadPool(2);
            ThreadPoolUtil realUtil = new ThreadPoolUtil(realExecutor);
            realUtil.init();

            String originalTraceId = "original-trace-id";
            AtomicReference<String> capturedTraceId = new AtomicReference<>();
            CountDownLatch latch = new CountDownLatch(1);

            // 设置原始TraceId
            MDC.put("traceId", originalTraceId);

            try {
                // 提交任务
                ThreadPoolUtil.submit(() -> {
                    capturedTraceId.set(MDC.get("traceId"));
                    latch.countDown();
                });

                // 等待任务完成
                assertTrue(latch.await(5, TimeUnit.SECONDS));

                // 验证TraceId传递
                assertNotNull(capturedTraceId.get());
                // 注意：由于TraceIdContext会生成新的TraceId，这里验证不为空即可
            } finally {
                MDC.clear();
                realExecutor.shutdown();
                realExecutor.awaitTermination(5, TimeUnit.SECONDS);
            }
        }

        @Test
        @DisplayName("指定TraceId的任务执行")
        void testSpecificTraceIdExecution() throws Exception {
            ExecutorService realExecutor = Executors.newFixedThreadPool(1);
            ThreadPoolUtil realUtil = new ThreadPoolUtil(realExecutor);
            realUtil.init();

            String specificTraceId = "specific-trace-id";
            AtomicReference<String> capturedTraceId = new AtomicReference<>();
            CountDownLatch latch = new CountDownLatch(1);

            try {
                ThreadPoolUtil.submitWithTraceId(() -> {
                    capturedTraceId.set(MDC.get("traceId"));
                    latch.countDown();
                }, specificTraceId);

                assertTrue(latch.await(5, TimeUnit.SECONDS));
                assertEquals(specificTraceId, capturedTraceId.get());
            } finally {
                realExecutor.shutdown();
                realExecutor.awaitTermination(5, TimeUnit.SECONDS);
            }
        }
    }

    @Test
    @DisplayName("TTL包装器提取ThreadPoolExecutor测试")
    void testExtractThreadPoolExecutorFromTTLWrapper() {
        // 这个测试主要验证反射逻辑不会抛出异常
        // 由于没有实际的TTL依赖，主要测试异常处理
        ThreadPoolUtil.ThreadPoolStatus status = ThreadPoolUtil.getThreadPoolStatus();
        // 对于非ThreadPoolExecutor的mock对象，应该返回null
        assertNull(status);
    }
}
