package com.tem.customer.shared.utils;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.RepeatedTest;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ShortUUID 单元测试
 * 测试22位短UUID字符串生成类的各种功能
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
@DisplayName("短UUID工具类测试")
class ShortUUIDTest {

    @Test
    @DisplayName("生成标准22位UUID")
    void testGetUuid() {
        String uuid = ShortUUID.getUuid();
        
        assertNotNull(uuid);
        assertEquals(22, uuid.length());
        assertTrue(uuid.matches("^[A-Za-z0-9\\-_]+$"));
    }

    @Test
    @DisplayName("生成带时间戳的TraceId")
    void testGetTimestampTraceId() {
        String traceId = ShortUUID.getTimestampTraceId();
        
        assertNotNull(traceId);
        assertEquals(22, traceId.length());
        assertTrue(traceId.matches("^[A-Za-z0-9\\-_]+$"));
    }

    @Test
    @DisplayName("生成不含特殊字符的UUID")
    void testGetUuidWithoutSpecialChars() {
        String uuid = ShortUUID.getUuidWithoutSpecialChars();
        
        assertNotNull(uuid);
        assertEquals(22, uuid.length());
        assertTrue(uuid.matches("^[A-Za-z0-9XY]+$"));
        assertFalse(uuid.contains("-"));
        assertFalse(uuid.contains("_"));
    }

    @Test
    @DisplayName("生成不含特殊字符的带时间戳TraceId")
    void testGetTimestampTraceIdWithoutSpecialChars() {
        String traceId = ShortUUID.getTimestampTraceIdWithoutSpecialChars();
        
        assertNotNull(traceId);
        assertEquals(22, traceId.length());
        assertTrue(traceId.matches("^[A-Za-z0-9XY]+$"));
        assertFalse(traceId.contains("-"));
        assertFalse(traceId.contains("_"));
    }

    @RepeatedTest(100)
    @DisplayName("UUID唯一性测试")
    void testUuidUniqueness() {
        Set<String> uuids = new HashSet<>();
        
        for (int i = 0; i < 1000; i++) {
            String uuid = ShortUUID.getUuid();
            assertFalse(uuids.contains(uuid), "UUID应该是唯一的: " + uuid);
            uuids.add(uuid);
        }
    }

    @Test
    @DisplayName("带时间戳TraceId的时间顺序性")
    void testTimestampTraceIdOrdering() throws InterruptedException {
        String traceId1 = ShortUUID.getTimestampTraceId();
        Thread.sleep(1); // 确保时间戳不同
        String traceId2 = ShortUUID.getTimestampTraceId();
        
        assertNotEquals(traceId1, traceId2);
        // 由于包含时间戳，第二个TraceId应该在字典序上大于第一个（在大多数情况下）
        // 这里主要验证它们不相等即可
    }

    @Test
    @DisplayName("并发生成UUID唯一性测试")
    void testConcurrentUuidGeneration() throws InterruptedException {
        int threadCount = 10;
        int uuidsPerThread = 100;
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        CountDownLatch latch = new CountDownLatch(threadCount);
        Set<String> allUuids = ConcurrentHashMap.newKeySet();

        for (int i = 0; i < threadCount; i++) {
            executor.submit(() -> {
                try {
                    for (int j = 0; j < uuidsPerThread; j++) {
                        String uuid = ShortUUID.getUuid();
                        assertTrue(allUuids.add(uuid), "并发生成的UUID应该是唯一的: " + uuid);
                    }
                } finally {
                    latch.countDown();
                }
            });
        }

        latch.await();
        executor.shutdown();
        
        assertEquals(threadCount * uuidsPerThread, allUuids.size());
    }

    @Test
    @DisplayName("特殊字符替换测试")
    void testSpecialCharacterReplacement() {
        // 生成多个UUID，检查特殊字符替换
        for (int i = 0; i < 100; i++) {
            String normalUuid = ShortUUID.getUuid();
            String cleanUuid = ShortUUID.getUuidWithoutSpecialChars();
            
            // 验证清理后的UUID不包含特殊字符
            assertFalse(cleanUuid.contains("-"));
            assertFalse(cleanUuid.contains("_"));
            
            // 验证长度保持不变
            assertEquals(normalUuid.length(), cleanUuid.length());
        }
    }

    @Test
    @DisplayName("TraceId格式验证测试")
    void testTraceIdValidation() {
        // 有效的TraceId
        assertTrue(ShortUUID.isValidTraceId("ABCDEFGHIJKLMNOPQRSTuv"));
        assertTrue(ShortUUID.isValidTraceId("abcdefghijklmnopqrstuv"));
        assertTrue(ShortUUID.isValidTraceId("0123456789"));
        assertTrue(ShortUUID.isValidTraceId("ABC-DEF_GHI"));
        assertTrue(ShortUUID.isValidTraceId("A"));
        assertTrue(ShortUUID.isValidTraceId("1234567890123456789012")); // 22位
        
        // 生成的UUID应该都是有效的
        assertTrue(ShortUUID.isValidTraceId(ShortUUID.getUuid()));
        assertTrue(ShortUUID.isValidTraceId(ShortUUID.getTimestampTraceId()));
        assertTrue(ShortUUID.isValidTraceId(ShortUUID.getUuidWithoutSpecialChars()));
        assertTrue(ShortUUID.isValidTraceId(ShortUUID.getTimestampTraceIdWithoutSpecialChars()));
    }

    @ParameterizedTest
    @ValueSource(strings = {
        "", // 空字符串
        "   ", // 空白字符串
        "12345678901234567890123", // 超过22位
        "ABC@DEF", // 包含非法字符@
        "ABC#DEF", // 包含非法字符#
        "ABC$DEF", // 包含非法字符$
        "ABC%DEF", // 包含非法字符%
        "ABC&DEF", // 包含非法字符&
        "ABC*DEF", // 包含非法字符*
        "ABC+DEF", // 包含非法字符+
        "ABC=DEF", // 包含非法字符=
        "ABC/DEF", // 包含非法字符/
        "ABC\\DEF", // 包含非法字符\
        "ABC|DEF", // 包含非法字符|
        "ABC<DEF", // 包含非法字符<
        "ABC>DEF", // 包含非法字符>
        "ABC?DEF", // 包含非法字符?
        "ABC.DEF", // 包含非法字符.
        "ABC,DEF", // 包含非法字符,
        "ABC;DEF", // 包含非法字符;
        "ABC:DEF", // 包含非法字符:
        "ABC\"DEF", // 包含非法字符"
        "ABC'DEF", // 包含非法字符'
        "ABC[DEF", // 包含非法字符[
        "ABC]DEF", // 包含非法字符]
        "ABC{DEF", // 包含非法字符{
        "ABC}DEF", // 包含非法字符}
        "ABC(DEF", // 包含非法字符(
        "ABC)DEF", // 包含非法字符)
        "ABC DEF", // 包含空格
        "ABC\tDEF", // 包含制表符
        "ABC\nDEF", // 包含换行符
        "中文字符", // 包含中文字符
        "🙂😀😃" // 包含emoji
    })
    @DisplayName("无效TraceId验证测试")
    void testInvalidTraceIdValidation(String invalidTraceId) {
        assertFalse(ShortUUID.isValidTraceId(invalidTraceId), 
            "应该识别为无效TraceId: " + invalidTraceId);
    }

    @Test
    @DisplayName("null TraceId验证测试")
    void testNullTraceIdValidation() {
        assertFalse(ShortUUID.isValidTraceId(null));
    }

    @Test
    @DisplayName("边界长度TraceId验证测试")
    void testBoundaryLengthTraceIdValidation() {
        // 1位有效
        assertTrue(ShortUUID.isValidTraceId("A"));
        
        // 22位有效
        assertTrue(ShortUUID.isValidTraceId("1234567890123456789012"));
        
        // 23位无效
        assertFalse(ShortUUID.isValidTraceId("12345678901234567890123"));
    }

    @Test
    @DisplayName("UUID格式一致性测试")
    void testUuidFormatConsistency() {
        // 测试多次生成的UUID都符合预期格式
        for (int i = 0; i < 50; i++) {
            String uuid = ShortUUID.getUuid();
            assertEquals(22, uuid.length(), "UUID长度应该是22位");
            assertTrue(uuid.matches("^[A-Za-z0-9\\-_]+$"), "UUID应该只包含字母、数字、横线和下划线");
            
            String timestampUuid = ShortUUID.getTimestampTraceId();
            assertEquals(22, timestampUuid.length(), "带时间戳的UUID长度应该是22位");
            assertTrue(timestampUuid.matches("^[A-Za-z0-9\\-_]+$"), "带时间戳的UUID应该只包含字母、数字、横线和下划线");
            
            String cleanUuid = ShortUUID.getUuidWithoutSpecialChars();
            assertEquals(22, cleanUuid.length(), "清理后的UUID长度应该是22位");
            assertTrue(cleanUuid.matches("^[A-Za-z0-9XY]+$"), "清理后的UUID应该只包含字母、数字和XY");
            
            String cleanTimestampUuid = ShortUUID.getTimestampTraceIdWithoutSpecialChars();
            assertEquals(22, cleanTimestampUuid.length(), "清理后的带时间戳UUID长度应该是22位");
            assertTrue(cleanTimestampUuid.matches("^[A-Za-z0-9XY]+$"), "清理后的带时间戳UUID应该只包含字母、数字和XY");
        }
    }

    @Test
    @DisplayName("性能测试 - 大量UUID生成")
    void testPerformance() {
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < 10000; i++) {
            ShortUUID.getUuid();
        }
        
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        
        // 10000个UUID生成应该在合理时间内完成（这里设置为5秒，实际应该远小于这个值）
        assertTrue(duration < 5000, "生成10000个UUID耗时: " + duration + "ms");
    }
}
