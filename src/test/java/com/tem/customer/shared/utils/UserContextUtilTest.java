package com.tem.customer.shared.utils;

import com.tem.platform.api.dto.UserDto;
import com.tem.platform.security.authorize.ContextUtil;
import com.tem.platform.security.authorize.User;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * UserContextUtil 单元测试
 * 测试用户上下文工具类的用户信息获取、企业ID获取等功能
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("用户上下文工具类测试")
class UserContextUtilTest {

    @Mock
    private HttpServletRequest mockRequest;

    @Mock
    private HttpServletResponse mockResponse;

    @Mock
    private HttpSession mockSession;

    @Mock
    private ServletRequestAttributes mockRequestAttributes;

    @Mock
    private User mockUser;

    @Mock
    private UserDto mockUserDto;

    @BeforeEach
    void setUp() {
        // 清理ThreadLocal
        UserContextUtil.clearResponse();
    }

    @Nested
    @DisplayName("Legacy用户获取测试")
    class LegacyUserTests {

        @Test
        @DisplayName("通过ContextUtil获取当前用户")
        void testGetCurrentUserLegacyFromContextUtil() {
            try (MockedStatic<ContextUtil> contextUtilMock = mockStatic(ContextUtil.class)) {
                // 设置Mock行为
                contextUtilMock.when(ContextUtil::getCurrentUser).thenReturn(mockUser);

                // 执行测试
                User result = UserContextUtil.getCurrentUserLegacy();

                // 验证结果
                assertNotNull(result);
                assertEquals(mockUser, result);
                contextUtilMock.verify(ContextUtil::getCurrentUser);
            }
        }

        @Test
        @DisplayName("ContextUtil返回null时回退到SaToken")
        void testGetCurrentUserLegacyFallbackToSaToken() {
            try (MockedStatic<ContextUtil> contextUtilMock = mockStatic(ContextUtil.class);
                 MockedStatic<SaTokenUserContextUtil> saTokenMock = mockStatic(SaTokenUserContextUtil.class)) {
                
                // 设置Mock行为
                contextUtilMock.when(ContextUtil::getCurrentUser).thenReturn(null);
                saTokenMock.when(SaTokenUserContextUtil::getCurrentUser).thenReturn(mockUserDto);
                
                when(mockUserDto.getId()).thenReturn(123L);
                when(mockUserDto.getUsername()).thenReturn("testuser");

                // 执行测试
                User result = UserContextUtil.getCurrentUserLegacy();

                // 验证结果
                assertNotNull(result);
                contextUtilMock.verify(ContextUtil::getCurrentUser);
                saTokenMock.verify(SaTokenUserContextUtil::getCurrentUser);
            }
        }

        @Test
        @DisplayName("ContextUtil抛出异常时返回null")
        void testGetCurrentUserLegacyExceptionHandling() {
            try (MockedStatic<ContextUtil> contextUtilMock = mockStatic(ContextUtil.class)) {
                // 设置Mock行为 - 抛出异常
                contextUtilMock.when(ContextUtil::getCurrentUser).thenThrow(new RuntimeException("Test exception"));

                // 执行测试
                User result = UserContextUtil.getCurrentUserLegacy();

                // 验证结果
                assertNull(result);
            }
        }

        @Test
        @DisplayName("所有方式都返回null")
        void testGetCurrentUserLegacyAllNull() {
            try (MockedStatic<ContextUtil> contextUtilMock = mockStatic(ContextUtil.class);
                 MockedStatic<SaTokenUserContextUtil> saTokenMock = mockStatic(SaTokenUserContextUtil.class)) {
                
                // 设置Mock行为
                contextUtilMock.when(ContextUtil::getCurrentUser).thenReturn(null);
                saTokenMock.when(SaTokenUserContextUtil::getCurrentUser).thenReturn(null);

                // 执行测试
                User result = UserContextUtil.getCurrentUserLegacy();

                // 验证结果
                assertNull(result);
            }
        }
    }

    @Nested
    @DisplayName("TMC ID获取测试")
    class TmcIdTests {

        @Test
        @DisplayName("从ContextUtil获取TMC ID")
        void testGetTmcIdFromContextUtil() {
            try (MockedStatic<ContextUtil> contextUtilMock = mockStatic(ContextUtil.class)) {
                // 设置Mock行为
                when(mockUser.getPartnerId()).thenReturn(12345L);
                contextUtilMock.when(ContextUtil::getCurrentUser).thenReturn(mockUser);

                // 执行测试
                Long tmcId = UserContextUtil.getTmcId();

                // 验证结果
                assertEquals(12345L, tmcId);
            }
        }

        @Test
        @DisplayName("ContextUtil返回null时回退到SaToken获取TMC ID")
        void testGetTmcIdFallbackToSaToken() {
            try (MockedStatic<ContextUtil> contextUtilMock = mockStatic(ContextUtil.class);
                 MockedStatic<SaTokenUserContextUtil> saTokenMock = mockStatic(SaTokenUserContextUtil.class)) {
                
                // 设置Mock行为
                when(mockUser.getPartnerId()).thenReturn(null);
                contextUtilMock.when(ContextUtil::getCurrentUser).thenReturn(mockUser);
                saTokenMock.when(SaTokenUserContextUtil::getCurrentUserPartnerId).thenReturn(67890L);

                // 执行测试
                Long tmcId = UserContextUtil.getTmcId();

                // 验证结果
                assertEquals(67890L, tmcId);
                saTokenMock.verify(SaTokenUserContextUtil::getCurrentUserPartnerId);
            }
        }
    }

    @Nested
    @DisplayName("请求上下文测试")
    class RequestContextTests {

        @Test
        @DisplayName("获取当前请求")
        void testGetRequest() {
            try (MockedStatic<RequestContextHolder> requestHolderMock = mockStatic(RequestContextHolder.class)) {
                // 设置Mock行为
                requestHolderMock.when(RequestContextHolder::currentRequestAttributes).thenReturn(mockRequestAttributes);
                when(mockRequestAttributes.getRequest()).thenReturn(mockRequest);

                // 执行测试
                HttpServletRequest result = UserContextUtil.getRequest();

                // 验证结果
                assertEquals(mockRequest, result);
            }
        }

        @Test
        @DisplayName("获取当前请求 - RequestAttributes为null")
        void testGetRequestWhenAttributesNull() {
            try (MockedStatic<RequestContextHolder> requestHolderMock = mockStatic(RequestContextHolder.class)) {
                // 设置Mock行为
                requestHolderMock.when(RequestContextHolder::currentRequestAttributes).thenThrow(new IllegalStateException("No request"));

                // 执行测试
                HttpServletRequest result = UserContextUtil.getRequest();

                // 验证结果
                assertNull(result);
            }
        }

        @Test
        @DisplayName("获取当前响应")
        void testGetResponse() {
            // 设置响应对象
            UserContextUtil.setResponse(mockResponse);

            // 执行测试
            HttpServletResponse result = UserContextUtil.getResponse();

            // 验证结果
            assertEquals(mockResponse, result);
        }

        @Test
        @DisplayName("获取当前会话")
        void testGetSession() {
            try (MockedStatic<RequestContextHolder> requestHolderMock = mockStatic(RequestContextHolder.class)) {
                // 设置Mock行为
                requestHolderMock.when(RequestContextHolder::currentRequestAttributes).thenReturn(mockRequestAttributes);
                when(mockRequestAttributes.getRequest()).thenReturn(mockRequest);
                when(mockRequest.getSession()).thenReturn(mockSession);

                // 执行测试
                HttpSession result = UserContextUtil.getSession();

                // 验证结果
                assertEquals(mockSession, result);
            }
        }

        @Test
        @DisplayName("获取当前会话 - 请求为null")
        void testGetSessionWhenRequestNull() {
            try (MockedStatic<RequestContextHolder> requestHolderMock = mockStatic(RequestContextHolder.class)) {
                // 设置Mock行为
                requestHolderMock.when(RequestContextHolder::currentRequestAttributes).thenThrow(new IllegalStateException("No request"));

                // 执行测试
                HttpSession result = UserContextUtil.getSession();

                // 验证结果
                assertNull(result);
            }
        }
    }

    @Nested
    @DisplayName("请求参数获取测试")
    class RequestParameterTests {

        @Test
        @DisplayName("获取请求参数")
        void testGetStringValue() {
            try (MockedStatic<RequestContextHolder> requestHolderMock = mockStatic(RequestContextHolder.class)) {
                // 设置Mock行为
                requestHolderMock.when(RequestContextHolder::currentRequestAttributes).thenReturn(mockRequestAttributes);
                when(mockRequestAttributes.getRequest()).thenReturn(mockRequest);
                when(mockRequest.getParameter("testParam")).thenReturn("testValue");

                // 执行测试
                String result = UserContextUtil.getStringValue("testParam");

                // 验证结果
                assertEquals("testValue", result);
            }
        }

        @Test
        @DisplayName("获取请求参数 - 请求为null")
        void testGetStringValueWhenRequestNull() {
            try (MockedStatic<RequestContextHolder> requestHolderMock = mockStatic(RequestContextHolder.class)) {
                // 设置Mock行为
                requestHolderMock.when(RequestContextHolder::currentRequestAttributes).thenThrow(new IllegalStateException("No request"));

                // 执行测试
                String result = UserContextUtil.getStringValue("testParam");

                // 验证结果
                assertNull(result);
            }
        }

        @Test
        @DisplayName("获取Long类型请求参数")
        void testGetLongValue() {
            try (MockedStatic<RequestContextHolder> requestHolderMock = mockStatic(RequestContextHolder.class)) {
                // 设置Mock行为
                requestHolderMock.when(RequestContextHolder::currentRequestAttributes).thenReturn(mockRequestAttributes);
                when(mockRequestAttributes.getRequest()).thenReturn(mockRequest);
                when(mockRequest.getParameter("longParam")).thenReturn("12345");

                // 执行测试
                Long result = UserContextUtil.getLongValue("longParam");

                // 验证结果
                assertEquals(12345L, result);
            }
        }

        @Test
        @DisplayName("获取Long类型请求参数 - 无效数字")
        void testGetLongValueInvalidNumber() {
            try (MockedStatic<RequestContextHolder> requestHolderMock = mockStatic(RequestContextHolder.class)) {
                // 设置Mock行为
                requestHolderMock.when(RequestContextHolder::currentRequestAttributes).thenReturn(mockRequestAttributes);
                when(mockRequestAttributes.getRequest()).thenReturn(mockRequest);
                when(mockRequest.getParameter("longParam")).thenReturn("invalid");

                // 执行测试
                Long result = UserContextUtil.getLongValue("longParam");

                // 验证结果
                assertNull(result);
            }
        }

        @Test
        @DisplayName("获取Integer类型请求参数")
        void testGetIntegerValue() {
            try (MockedStatic<RequestContextHolder> requestHolderMock = mockStatic(RequestContextHolder.class)) {
                // 设置Mock行为
                requestHolderMock.when(RequestContextHolder::currentRequestAttributes).thenReturn(mockRequestAttributes);
                when(mockRequestAttributes.getRequest()).thenReturn(mockRequest);
                when(mockRequest.getParameter("intParam")).thenReturn("123");

                // 执行测试
                Integer result = UserContextUtil.getIntegerValue("intParam");

                // 验证结果
                assertEquals(123, result);
            }
        }
    }

    @Nested
    @DisplayName("Session数据操作测试")
    class SessionDataTests {

        @Test
        @DisplayName("获取Session对象")
        void testGetSession() {
            try (MockedStatic<RequestContextHolder> requestHolderMock = mockStatic(RequestContextHolder.class)) {
                // 设置Mock行为
                requestHolderMock.when(RequestContextHolder::currentRequestAttributes).thenReturn(mockRequestAttributes);
                when(mockRequestAttributes.getRequest()).thenReturn(mockRequest);
                when(mockRequest.getSession()).thenReturn(mockSession);

                // 执行测试
                HttpSession result = UserContextUtil.getSession();

                // 验证结果
                assertEquals(mockSession, result);
            }
        }

        @Test
        @DisplayName("获取Session对象 - 请求为null")
        void testGetSessionWhenRequestNull() {
            try (MockedStatic<RequestContextHolder> requestHolderMock = mockStatic(RequestContextHolder.class)) {
                // 设置Mock行为
                requestHolderMock.when(RequestContextHolder::currentRequestAttributes).thenThrow(new IllegalStateException("No request"));

                // 执行测试
                HttpSession result = UserContextUtil.getSession();

                // 验证结果
                assertNull(result);
            }
        }

        @Test
        @DisplayName("获取PartnerId")
        void testGetPartnerId() {
            try (MockedStatic<RequestContextHolder> requestHolderMock = mockStatic(RequestContextHolder.class)) {
                // 设置Mock行为
                requestHolderMock.when(RequestContextHolder::currentRequestAttributes).thenReturn(mockRequestAttributes);
                when(mockRequestAttributes.getRequest()).thenReturn(mockRequest);
                when(mockRequest.getParameter("partnerId")).thenReturn("12345");

                // 执行测试
                Long result = UserContextUtil.getPartnerId();

                // 验证结果
                assertEquals(12345L, result);
            }
        }
    }

    @Nested
    @DisplayName("响应对象管理测试")
    class ResponseManagementTests {

        @Test
        @DisplayName("设置和获取响应对象")
        void testSetAndGetResponse() {
            // 设置响应对象
            UserContextUtil.setResponse(mockResponse);

            // 获取响应对象
            HttpServletResponse result = UserContextUtil.getResponse();

            // 验证结果
            assertEquals(mockResponse, result);
        }

        @Test
        @DisplayName("清理响应对象")
        void testClearResponse() {
            // 设置响应对象
            UserContextUtil.setResponse(mockResponse);

            // 清理响应对象
            UserContextUtil.clearResponse();

            // 验证清理结果
            HttpServletResponse result = UserContextUtil.getResponse();
            assertNull(result);
        }

        @Test
        @DisplayName("获取响应对象 - 未设置时返回null")
        void testGetResponseWhenNotSet() {
            HttpServletResponse result = UserContextUtil.getResponse();
            assertNull(result);
        }
    }

    @Test
    @DisplayName("异常处理测试")
    void testExceptionHandling() {
        try (MockedStatic<RequestContextHolder> requestHolderMock = mockStatic(RequestContextHolder.class)) {
            // 设置Mock行为 - 抛出异常
            requestHolderMock.when(RequestContextHolder::currentRequestAttributes)
                .thenThrow(new RuntimeException("Test exception"));

            // 执行测试 - 应该不抛出异常
            assertDoesNotThrow(() -> {
                HttpServletRequest request = UserContextUtil.getRequest();
                assertNull(request);
            });

            assertDoesNotThrow(() -> {
                String param = UserContextUtil.getStringValue("test");
                assertNull(param);
            });

            assertDoesNotThrow(() -> {
                HttpSession session = UserContextUtil.getSession();
                assertNull(session);
            });
        }
    }
}
