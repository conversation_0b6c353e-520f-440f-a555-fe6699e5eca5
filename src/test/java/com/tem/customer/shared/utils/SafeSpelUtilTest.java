package com.tem.customer.shared.utils;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

import java.lang.reflect.Method;

import static org.junit.jupiter.api.Assertions.*;

/**
 * SafeSpelUtil 单元测试
 * 测试安全的SpEL表达式工具类的各种功能
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
@DisplayName("安全SpEL表达式工具类测试")
class SafeSpelUtilTest {

    /**
     * 测试用的简单类
     */
    static class TestObject {
        private String name;
        private Integer age;
        private Long id;

        public TestObject(String name, Integer age, Long id) {
            this.name = name;
            this.age = age;
            this.id = id;
        }

        public String getName() { return name; }
        public Integer getAge() { return age; }
        public Long getId() { return id; }
    }

    @Nested
    @DisplayName("安全表达式解析测试")
    class SafeExpressionParsingTests {

        @Test
        @DisplayName("解析简单的result表达式")
        void testParseSimpleResultExpression() throws Exception {
            Method method = TestObject.class.getMethod("getName");
            TestObject result = new TestObject("张三", 25, 123L);

            Object value = SafeSpelUtil.safeParseExpression("#result.name", method, null, result);
            assertEquals("张三", value);
        }

        @Test
        @DisplayName("解析数字表达式")
        void testParseNumberExpression() throws Exception {
            Method method = TestObject.class.getMethod("getAge");
            TestObject result = new TestObject("李四", 30, 456L);

            Object value = SafeSpelUtil.safeParseExpression("#result.age", method, null, result);
            assertEquals(30, value);
        }

        @Test
        @DisplayName("解析Long类型表达式")
        void testParseLongExpression() throws Exception {
            Method method = TestObject.class.getMethod("getId");
            TestObject result = new TestObject("王五", 35, 789L);

            Long value = SafeSpelUtil.parseLongExpression("#result.id", method, null, result);
            assertEquals(789L, value);
        }

        @Test
        @DisplayName("解析String类型表达式")
        void testParseStringExpression() throws Exception {
            Method method = TestObject.class.getMethod("getName");
            TestObject result = new TestObject("赵六", 40, 101112L);

            String value = SafeSpelUtil.parseStringExpression("#result.name", method, null, result);
            assertEquals("赵六", value);
        }

        @Test
        @DisplayName("解析null结果")
        void testParseNullResult() throws Exception {
            Method method = TestObject.class.getMethod("getName");

            Object value = SafeSpelUtil.safeParseExpression("#result", method, null, null);
            assertNull(value);
        }

        @Test
        @DisplayName("解析空表达式")
        void testParseEmptyExpression() throws Exception {
            Method method = TestObject.class.getMethod("getName");
            TestObject result = new TestObject("测试", 25, 123L);

            assertNull(SafeSpelUtil.safeParseExpression("", method, null, result));
            assertNull(SafeSpelUtil.safeParseExpression("   ", method, null, result));
            assertNull(SafeSpelUtil.safeParseExpression(null, method, null, result));
        }

        @Test
        @DisplayName("解析包含result但result为null的Long表达式")
        void testParseLongExpressionWithNullResult() throws Exception {
            Method method = TestObject.class.getMethod("getId");

            Long value = SafeSpelUtil.parseLongExpression("#result.id", method, null, null);
            assertNull(value);
        }

        @Test
        @DisplayName("解析字符串转Long")
        void testParseStringToLong() throws Exception {
            Method method = TestObject.class.getMethod("getName");
            TestObject result = new TestObject("12345", 25, 123L);

            Long value = SafeSpelUtil.parseLongExpression("#result.name", method, null, result);
            assertEquals(12345L, value);
        }

        @Test
        @DisplayName("解析无效字符串转Long")
        void testParseInvalidStringToLong() throws Exception {
            Method method = TestObject.class.getMethod("getName");
            TestObject result = new TestObject("不是数字", 25, 123L);

            Long value = SafeSpelUtil.parseLongExpression("#result.name", method, null, result);
            assertNull(value);
        }
    }

    @Nested
    @DisplayName("危险表达式检测测试")
    class DangerousExpressionDetectionTests {

        @ParameterizedTest
        @ValueSource(strings = {
            "T(java.lang.Runtime).getRuntime().exec('ls')",
            "T(java.lang.System).exit(0)",
            "@systemService.dangerousMethod()",
            "new java.io.File('/etc/passwd')",
            "#result.getClass().forName('java.lang.Runtime')",
            "#result.class.classLoader",
            "getClass().getDeclaredMethod('exec')",
            "invoke()",
            "constructor",
            "field",
            "method",
            "reflect"
        })
        @DisplayName("检测危险表达式")
        void testDetectDangerousExpressions(String dangerousExpression) throws Exception {
            Method method = TestObject.class.getMethod("getName");
            TestObject result = new TestObject("测试", 25, 123L);

            Object value = SafeSpelUtil.safeParseExpression(dangerousExpression, method, null, result);
            assertNull(value, "危险表达式应该返回null: " + dangerousExpression);
        }

        @ParameterizedTest
        @ValueSource(strings = {
            "java.lang.Runtime",
            "java.lang.ProcessBuilder",
            "java.lang.System",
            "java.lang.Class",
            "java.lang.ClassLoader",
            "java.io.File",
            "java.nio.file.Files",
            "java.nio.file.Paths"
        })
        @DisplayName("检测危险类名")
        void testDetectDangerousClassNames(String dangerousClass) throws Exception {
            Method method = TestObject.class.getMethod("getName");
            TestObject result = new TestObject("测试", 25, 123L);

            String expression = "T(" + dangerousClass + ")";
            Object value = SafeSpelUtil.safeParseExpression(expression, method, null, result);
            assertNull(value, "包含危险类的表达式应该返回null: " + expression);
        }

        @ParameterizedTest
        @ValueSource(strings = {
            "getClass",
            "forName",
            "newInstance",
            "getClassLoader",
            "getMethod",
            "getDeclaredMethod",
            "invoke",
            "exec",
            "getRuntime",
            "exit",
            "halt",
            "load",
            "loadLibrary"
        })
        @DisplayName("检测危险方法名")
        void testDetectDangerousMethodNames(String dangerousMethod) throws Exception {
            Method method = TestObject.class.getMethod("getName");
            TestObject result = new TestObject("测试", 25, 123L);

            String expression = "#result." + dangerousMethod + "()";
            Object value = SafeSpelUtil.safeParseExpression(expression, method, null, result);
            assertNull(value, "包含危险方法的表达式应该返回null: " + expression);
        }
    }

    @Nested
    @DisplayName("安全表达式测试")
    class SafeExpressionTests {

        @ParameterizedTest
        @ValueSource(strings = {
            "#result.name",
            "#result.age",
            "#result.id",
            "#result.name + ' - ' + #result.age",
            "#result.age > 18",
            "#result.id != null",
            "'常量字符串'",
            "123",
            "true",
            "false"
        })
        @DisplayName("安全表达式应该正常解析")
        void testSafeExpressions(String safeExpression) throws Exception {
            Method method = TestObject.class.getMethod("getName");
            TestObject result = new TestObject("测试用户", 25, 123L);

            Object value = SafeSpelUtil.safeParseExpression(safeExpression, method, null, result);
            // 安全表达式应该能够正常解析，不返回null（除非表达式本身计算结果为null）
            if (!safeExpression.contains("!= null")) {
                assertNotNull(value, "安全表达式应该能够正常解析: " + safeExpression);
            }
        }
    }

    @Nested
    @DisplayName("方法参数处理测试")
    class MethodParameterTests {

        public void testMethodWithParameters(String name, Integer age, Long id) {
            // 测试方法，用于获取参数信息
        }

        @Test
        @DisplayName("处理方法参数")
        void testMethodParameters() throws Exception {
            Method method = this.getClass().getMethod("testMethodWithParameters", String.class, Integer.class, Long.class);
            Object[] args = {"参数名", 30, 456L};
            TestObject result = new TestObject("结果", 25, 123L);

            // 测试访问方法参数
            Object nameValue = SafeSpelUtil.safeParseExpression("#name", method, args, result);
            assertEquals("参数名", nameValue);

            Object ageValue = SafeSpelUtil.safeParseExpression("#age", method, args, result);
            assertEquals(30, ageValue);

            Object idValue = SafeSpelUtil.safeParseExpression("#id", method, args, result);
            assertEquals(456L, idValue);
        }

        @Test
        @DisplayName("处理null方法参数")
        void testNullMethodParameters() throws Exception {
            Method method = this.getClass().getMethod("testMethodWithParameters", String.class, Integer.class, Long.class);
            Object[] args = {null, null, null};
            TestObject result = new TestObject("结果", 25, 123L);

            Object nameValue = SafeSpelUtil.safeParseExpression("#name", method, args, result);
            assertNull(nameValue);
        }
    }

    @Nested
    @DisplayName("类型转换测试")
    class TypeConversionTests {

        @Test
        @DisplayName("Number转Long")
        void testNumberToLong() throws Exception {
            Method method = TestObject.class.getMethod("getAge");
            TestObject result = new TestObject("测试", 42, 123L);

            Long value = SafeSpelUtil.parseLongExpression("#result.age", method, null, result);
            assertEquals(42L, value);
        }

        @Test
        @DisplayName("String转Long - 有效数字")
        void testValidStringToLong() throws Exception {
            Method method = TestObject.class.getMethod("getName");
            TestObject result = new TestObject("999", 25, 123L);

            Long value = SafeSpelUtil.parseLongExpression("#result.name", method, null, result);
            assertEquals(999L, value);
        }

        @Test
        @DisplayName("String转Long - 无效数字")
        void testInvalidStringToLong() throws Exception {
            Method method = TestObject.class.getMethod("getName");
            TestObject result = new TestObject("abc123", 25, 123L);

            Long value = SafeSpelUtil.parseLongExpression("#result.name", method, null, result);
            assertNull(value);
        }

        @Test
        @DisplayName("任意类型转String")
        void testAnyTypeToString() throws Exception {
            Method method = TestObject.class.getMethod("getAge");
            TestObject result = new TestObject("测试", 35, 123L);

            String value = SafeSpelUtil.parseStringExpression("#result.age", method, null, result);
            assertEquals("35", value);
        }

        @Test
        @DisplayName("null值转String")
        void testNullToString() throws Exception {
            Method method = TestObject.class.getMethod("getName");

            String value = SafeSpelUtil.parseStringExpression("#result", method, null, null);
            assertNull(value);
        }
    }

    @Test
    @DisplayName("表达式解析异常处理")
    void testExpressionParsingException() throws Exception {
        Method method = TestObject.class.getMethod("getName");
        TestObject result = new TestObject("测试", 25, 123L);

        // 无效的SpEL表达式语法
        Object value = SafeSpelUtil.safeParseExpression("#result.nonExistentProperty", method, null, result);
        assertNull(value);
    }

    @Test
    @DisplayName("空方法和参数处理")
    void testNullMethodAndArgs() {
        TestObject result = new TestObject("测试", 25, 123L);

        Object value = SafeSpelUtil.safeParseExpression("#result.name", null, null, result);
        assertEquals("测试", value);
    }
}
