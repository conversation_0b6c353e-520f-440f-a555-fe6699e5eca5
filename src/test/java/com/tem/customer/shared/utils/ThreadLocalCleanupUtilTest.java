package com.tem.customer.shared.utils;

import com.tem.platform.security.authorize.ContextHolder;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * ThreadLocalCleanupUtil 单元测试
 * 测试ThreadLocal清理工具类的各种功能
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("ThreadLocal清理工具类测试")
class ThreadLocalCleanupUtilTest {

    @BeforeEach
    void setUp() {
        // 每个测试前清理ThreadLocal状态
        try {
            ThreadLocalCleanupUtil.performCompleteCleanup();
        } catch (Exception e) {
            // 忽略清理异常
        }
    }

    @Nested
    @DisplayName("完整清理功能测试")
    class CompleteCleanupTests {

        @Test
        @DisplayName("执行完整的ThreadLocal清理")
        void testPerformCompleteCleanup() {
            // 测试清理方法不抛出异常
            assertDoesNotThrow(() -> ThreadLocalCleanupUtil.performCompleteCleanup());
        }

        @Test
        @DisplayName("清理过程中的异常处理")
        void testCleanupExceptionHandling() {
            // 即使在清理过程中发生异常，也不应该抛出
            assertDoesNotThrow(() -> ThreadLocalCleanupUtil.performCompleteCleanup());
        }

        @Test
        @DisplayName("多次清理操作")
        void testMultipleCleanupOperations() {
            // 多次调用清理方法应该是安全的
            assertDoesNotThrow(() -> {
                ThreadLocalCleanupUtil.performCompleteCleanup();
                ThreadLocalCleanupUtil.performCompleteCleanup();
                ThreadLocalCleanupUtil.performCompleteCleanup();
            });
        }
    }

    @Nested
    @DisplayName("ContextHolder清理测试")
    class ContextHolderCleanupTests {

        @Test
        @DisplayName("清理ContextHolder")
        void testCleanupContextHolder() {
            try (MockedStatic<ContextHolder> contextHolderMock = mockStatic(ContextHolder.class)) {
                // 执行清理
                ThreadLocalCleanupUtil.performCompleteCleanup();

                // 验证ContextHolder.clear()被调用
                contextHolderMock.verify(ContextHolder::clear);
            }
        }

        @Test
        @DisplayName("ContextHolder清理异常处理")
        void testContextHolderCleanupException() {
            try (MockedStatic<ContextHolder> contextHolderMock = mockStatic(ContextHolder.class)) {
                // 设置ContextHolder.clear()抛出异常
                contextHolderMock.when(ContextHolder::clear).thenThrow(new RuntimeException("清理异常"));

                // 清理操作应该不抛出异常
                assertDoesNotThrow(() -> ThreadLocalCleanupUtil.performCompleteCleanup());
            }
        }
    }

    @Nested
    @DisplayName("UserContextUtil清理测试")
    class UserContextUtilCleanupTests {

        @Test
        @DisplayName("清理UserContextUtil")
        void testCleanupUserContextUtil() {
            // 由于UserContextUtil.clearResponse()是静态方法，我们主要测试不抛出异常
            assertDoesNotThrow(() -> ThreadLocalCleanupUtil.performCompleteCleanup());
        }
    }

    @Nested
    @DisplayName("反射清理测试")
    class ReflectiveCleanupTests {

        @Test
        @DisplayName("反射方式清理ThreadLocal")
        void testReflectiveCleanup() {
            // 反射清理应该不抛出异常
            assertDoesNotThrow(() -> ThreadLocalCleanupUtil.performCompleteCleanup());
        }

        @Test
        @DisplayName("反射清理异常处理")
        void testReflectiveCleanupExceptionHandling() {
            // 即使反射操作失败，也不应该抛出异常
            assertDoesNotThrow(() -> ThreadLocalCleanupUtil.performCompleteCleanup());
        }
    }

    @Nested
    @DisplayName("用户上下文检查测试")
    class UserContextCheckTests {

        @Test
        @DisplayName("检查残留用户上下文 - 无残留")
        void testHasRemainingUserContextNoRemainder() {
            try (MockedStatic<ContextHolder> contextHolderMock = mockStatic(ContextHolder.class)) {
                // 设置ContextHolder.getContext()返回null
                contextHolderMock.when(ContextHolder::getContext).thenReturn(null);

                boolean hasRemainder = ThreadLocalCleanupUtil.hasRemainingUserContext();
                assertFalse(hasRemainder);
            }
        }

        @Test
        @DisplayName("检查残留用户上下文 - 有残留")
        void testHasRemainingUserContextWithRemainder() {
            try (MockedStatic<ContextHolder> contextHolderMock = mockStatic(ContextHolder.class)) {
                // 设置ContextHolder.getContext()返回非null对象
                Object mockContext = new Object();
                contextHolderMock.when(ContextHolder::getContext).thenReturn(mockContext);

                boolean hasRemainder = ThreadLocalCleanupUtil.hasRemainingUserContext();
                assertTrue(hasRemainder);
            }
        }

        @Test
        @DisplayName("检查残留用户上下文 - 异常处理")
        void testHasRemainingUserContextException() {
            try (MockedStatic<ContextHolder> contextHolderMock = mockStatic(ContextHolder.class)) {
                // 设置ContextHolder.getContext()抛出异常
                contextHolderMock.when(ContextHolder::getContext).thenThrow(new RuntimeException("获取上下文异常"));

                boolean hasRemainder = ThreadLocalCleanupUtil.hasRemainingUserContext();
                assertFalse(hasRemainder); // 异常情况下应该返回false
            }
        }
    }

    @Nested
    @DisplayName("并发清理测试")
    class ConcurrentCleanupTests {

        @Test
        @DisplayName("并发执行清理操作")
        void testConcurrentCleanup() throws InterruptedException {
            int threadCount = 10;
            Thread[] threads = new Thread[threadCount];
            boolean[] results = new boolean[threadCount];

            // 创建多个线程同时执行清理操作
            for (int i = 0; i < threadCount; i++) {
                final int index = i;
                threads[i] = new Thread(() -> {
                    try {
                        ThreadLocalCleanupUtil.performCompleteCleanup();
                        results[index] = true;
                    } catch (Exception e) {
                        results[index] = false;
                    }
                });
            }

            // 启动所有线程
            for (Thread thread : threads) {
                thread.start();
            }

            // 等待所有线程完成
            for (Thread thread : threads) {
                thread.join();
            }

            // 验证所有线程都成功执行
            for (boolean result : results) {
                assertTrue(result, "所有线程都应该成功执行清理操作");
            }
        }

        @Test
        @DisplayName("并发检查残留上下文")
        void testConcurrentContextCheck() throws InterruptedException {
            int threadCount = 5;
            Thread[] threads = new Thread[threadCount];
            boolean[] results = new boolean[threadCount];

            // 创建多个线程同时检查残留上下文
            for (int i = 0; i < threadCount; i++) {
                final int index = i;
                threads[i] = new Thread(() -> {
                    try {
                        ThreadLocalCleanupUtil.hasRemainingUserContext();
                        results[index] = true;
                    } catch (Exception e) {
                        results[index] = false;
                    }
                });
            }

            // 启动所有线程
            for (Thread thread : threads) {
                thread.start();
            }

            // 等待所有线程完成
            for (Thread thread : threads) {
                thread.join();
            }

            // 验证所有线程都成功执行
            for (boolean result : results) {
                assertTrue(result, "所有线程都应该成功执行检查操作");
            }
        }
    }

    @Nested
    @DisplayName("线程信息记录测试")
    class ThreadInfoLoggingTests {

        @Test
        @DisplayName("记录线程信息")
        void testThreadInfoLogging() {
            // 获取当前线程信息
            long threadId = Thread.currentThread().getId();
            String threadName = Thread.currentThread().getName();

            // 执行清理操作，验证不抛出异常
            assertDoesNotThrow(() -> ThreadLocalCleanupUtil.performCompleteCleanup());

            // 验证线程ID和名称是有效的
            assertTrue(threadId > 0);
            assertNotNull(threadName);
            assertFalse(threadName.isEmpty());
        }
    }

    @Test
    @DisplayName("清理操作的幂等性")
    void testCleanupIdempotency() {
        // 多次执行清理操作应该是安全的，不会产生副作用
        assertDoesNotThrow(() -> {
            ThreadLocalCleanupUtil.performCompleteCleanup();
            ThreadLocalCleanupUtil.performCompleteCleanup();
            ThreadLocalCleanupUtil.performCompleteCleanup();
        });

        // 检查残留上下文也应该是幂等的
        assertDoesNotThrow(() -> {
            ThreadLocalCleanupUtil.hasRemainingUserContext();
            ThreadLocalCleanupUtil.hasRemainingUserContext();
            ThreadLocalCleanupUtil.hasRemainingUserContext();
        });
    }

    @Test
    @DisplayName("清理后的状态验证")
    void testStateAfterCleanup() {
        // 执行清理
        ThreadLocalCleanupUtil.performCompleteCleanup();

        // 验证清理后的状态
        try (MockedStatic<ContextHolder> contextHolderMock = mockStatic(ContextHolder.class)) {
            contextHolderMock.when(ContextHolder::getContext).thenReturn(null);
            
            boolean hasRemainder = ThreadLocalCleanupUtil.hasRemainingUserContext();
            assertFalse(hasRemainder, "清理后不应该有残留的用户上下文");
        }
    }

    @Test
    @DisplayName("异常情况下的清理完整性")
    void testCleanupCompletenessUnderException() {
        try (MockedStatic<ContextHolder> contextHolderMock = mockStatic(ContextHolder.class)) {
            // 设置第一个清理步骤抛出异常
            contextHolderMock.when(ContextHolder::clear).thenThrow(new RuntimeException("清理异常"));

            // 即使某个步骤失败，整个清理过程也应该继续执行
            assertDoesNotThrow(() -> ThreadLocalCleanupUtil.performCompleteCleanup());
        }
    }
}
