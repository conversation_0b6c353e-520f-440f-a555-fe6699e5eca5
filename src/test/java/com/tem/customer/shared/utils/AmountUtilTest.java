package com.tem.customer.shared.utils;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.junit.jupiter.params.provider.NullAndEmptySource;

import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.*;

/**
 * AmountUtil 单元测试
 * 测试金额工具类的分元转换、精度控制、安全计算等功能
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
@DisplayName("金额工具类测试")
class AmountUtilTest {

    @Test
    @DisplayName("测试工具类不能被实例化")
    void testUtilityClassCannotBeInstantiated() {
        Exception exception = assertThrows(Exception.class, () -> {
            // 使用反射尝试创建实例
            var constructor = AmountUtil.class.getDeclaredConstructor();
            constructor.setAccessible(true);
            constructor.newInstance();
        });

        // 检查是否是UnsupportedOperationException或其包装异常
        Throwable cause = exception.getCause();
        assertTrue(exception instanceof UnsupportedOperationException ||
                  (cause != null && cause instanceof UnsupportedOperationException),
                  "工具类应该不能被实例化");
    }

    @Test
    @DisplayName("分转元 - Long类型输入")
    void testFenToYuanWithLong() {
        // 正常情况
        assertEquals(new BigDecimal("1.00"), AmountUtil.fenToYuan(100L));
        assertEquals(new BigDecimal("0.01"), AmountUtil.fenToYuan(1L));
        assertEquals(new BigDecimal("12.34"), AmountUtil.fenToYuan(1234L));
        assertEquals(new BigDecimal("0.00"), AmountUtil.fenToYuan(0L));
        
        // 负数情况
        assertEquals(new BigDecimal("-1.00"), AmountUtil.fenToYuan(-100L));
        assertEquals(new BigDecimal("-12.34"), AmountUtil.fenToYuan(-1234L));
        
        // null情况
        assertEquals(BigDecimal.ZERO, AmountUtil.fenToYuan((Long) null));
        
        // 大数值情况
        assertEquals(new BigDecimal("999999.99"), AmountUtil.fenToYuan(99999999L));
    }

    @Test
    @DisplayName("分转元 - BigDecimal类型输入")
    void testFenToYuanWithBigDecimal() {
        // 正常情况
        assertEquals(new BigDecimal("1.00"), AmountUtil.fenToYuan(new BigDecimal("100")));
        assertEquals(new BigDecimal("0.01"), AmountUtil.fenToYuan(new BigDecimal("1")));
        assertEquals(new BigDecimal("12.34"), AmountUtil.fenToYuan(new BigDecimal("1234")));
        assertEquals(new BigDecimal("0.00"), AmountUtil.fenToYuan(new BigDecimal("0")));
        
        // 小数情况（需要四舍五入）
        assertEquals(new BigDecimal("1.23"), AmountUtil.fenToYuan(new BigDecimal("123.4")));
        assertEquals(new BigDecimal("1.24"), AmountUtil.fenToYuan(new BigDecimal("123.5")));
        
        // null情况
        assertEquals(BigDecimal.ZERO, AmountUtil.fenToYuan((BigDecimal) null));
    }

    @Test
    @DisplayName("元转分 - String类型输入")
    void testYuanToFenWithString() {
        // 正常情况
        assertEquals(100L, AmountUtil.yuanToFen("1.00"));
        assertEquals(1L, AmountUtil.yuanToFen("0.01"));
        assertEquals(1234L, AmountUtil.yuanToFen("12.34"));
        assertEquals(0L, AmountUtil.yuanToFen("0"));
        assertEquals(0L, AmountUtil.yuanToFen("0.00"));
        
        // 整数情况
        assertEquals(100L, AmountUtil.yuanToFen("1"));
        assertEquals(500L, AmountUtil.yuanToFen("5"));
        
        // 负数情况
        assertEquals(-100L, AmountUtil.yuanToFen("-1.00"));
        assertEquals(-1234L, AmountUtil.yuanToFen("-12.34"));
        
        // 需要四舍五入的情况
        assertEquals(123L, AmountUtil.yuanToFen("1.234"));
        assertEquals(124L, AmountUtil.yuanToFen("1.235"));
        
        // null和空字符串情况
        assertEquals(0L, AmountUtil.yuanToFen((String) null));
        assertEquals(0L, AmountUtil.yuanToFen(""));
        assertEquals(0L, AmountUtil.yuanToFen("   "));
    }

    @ParameterizedTest
    @ValueSource(strings = {"abc", "1.2.3", "1,234", "1.23.45", "not_a_number"})
    @DisplayName("元转分 - 无效字符串格式应抛出异常")
    void testYuanToFenWithInvalidString(String invalidAmount) {
        assertThrows(IllegalArgumentException.class, () -> AmountUtil.yuanToFen(invalidAmount));
    }

    @Test
    @DisplayName("元转分 - BigDecimal类型输入")
    void testYuanToFenWithBigDecimal() {
        // 正常情况
        assertEquals(100L, AmountUtil.yuanToFen(new BigDecimal("1.00")));
        assertEquals(1L, AmountUtil.yuanToFen(new BigDecimal("0.01")));
        assertEquals(1234L, AmountUtil.yuanToFen(new BigDecimal("12.34")));
        assertEquals(0L, AmountUtil.yuanToFen(new BigDecimal("0")));
        
        // 需要四舍五入的情况
        assertEquals(123L, AmountUtil.yuanToFen(new BigDecimal("1.234")));
        assertEquals(124L, AmountUtil.yuanToFen(new BigDecimal("1.235")));
        
        // null情况
        assertEquals(0L, AmountUtil.yuanToFen((BigDecimal) null));
    }

    @Test
    @DisplayName("元转分 - Long类型输入")
    void testYuanToFenWithLong() {
        // 正常情况
        assertEquals(100L, AmountUtil.yuanToFen(1L));
        assertEquals(500L, AmountUtil.yuanToFen(5L));
        assertEquals(0L, AmountUtil.yuanToFen(0L));
        
        // 负数情况
        assertEquals(-100L, AmountUtil.yuanToFen(-1L));
        
        // null情况
        assertEquals(0L, AmountUtil.yuanToFen((Long) null));
    }

    @Test
    @DisplayName("元转分BigDecimal - BigDecimal类型输入")
    void testYuanToFenDecimal() {
        // 正常情况
        assertEquals(0, new BigDecimal("100").compareTo(AmountUtil.yuanToFenDecimal(new BigDecimal("1.00"))));
        assertEquals(0, new BigDecimal("1").compareTo(AmountUtil.yuanToFenDecimal(new BigDecimal("0.01"))));
        assertEquals(0, new BigDecimal("1234").compareTo(AmountUtil.yuanToFenDecimal(new BigDecimal("12.34"))));
        assertEquals(0, new BigDecimal("0").compareTo(AmountUtil.yuanToFenDecimal(new BigDecimal("0"))));
        
        // 小数情况（不进行四舍五入）
        assertEquals(0, new BigDecimal("123.4").compareTo(AmountUtil.yuanToFenDecimal(new BigDecimal("1.234"))));
        
        // null情况
        assertEquals(BigDecimal.ZERO, AmountUtil.yuanToFenDecimal(null));
    }

    @Test
    @DisplayName("检查金额是否为正数")
    void testIsPositive() {
        // 正数情况
        assertTrue(AmountUtil.isPositive(new BigDecimal("1.00")));
        assertTrue(AmountUtil.isPositive(new BigDecimal("0.01")));
        assertTrue(AmountUtil.isPositive(new BigDecimal("999999.99")));
        
        // 零和负数情况
        assertFalse(AmountUtil.isPositive(new BigDecimal("0")));
        assertFalse(AmountUtil.isPositive(new BigDecimal("0.00")));
        assertFalse(AmountUtil.isPositive(new BigDecimal("-1.00")));
        assertFalse(AmountUtil.isPositive(new BigDecimal("-0.01")));
        
        // null情况
        assertFalse(AmountUtil.isPositive(null));
    }

    @Test
    @DisplayName("检查金额是否为零或负数")
    void testIsZeroOrNegative() {
        // 零和负数情况
        assertTrue(AmountUtil.isZeroOrNegative(new BigDecimal("0")));
        assertTrue(AmountUtil.isZeroOrNegative(new BigDecimal("0.00")));
        assertTrue(AmountUtil.isZeroOrNegative(new BigDecimal("-1.00")));
        assertTrue(AmountUtil.isZeroOrNegative(new BigDecimal("-0.01")));
        
        // 正数情况
        assertFalse(AmountUtil.isZeroOrNegative(new BigDecimal("1.00")));
        assertFalse(AmountUtil.isZeroOrNegative(new BigDecimal("0.01")));
        
        // null情况
        assertTrue(AmountUtil.isZeroOrNegative(null));
    }

    @Test
    @DisplayName("安全的金额相加")
    void testAdd() {
        // 正常情况
        assertEquals(new BigDecimal("3.00"), AmountUtil.add(new BigDecimal("1.00"), new BigDecimal("2.00")));
        assertEquals(new BigDecimal("0.03"), AmountUtil.add(new BigDecimal("0.01"), new BigDecimal("0.02")));
        
        // 一个为null的情况
        assertEquals(new BigDecimal("1.00"), AmountUtil.add(new BigDecimal("1.00"), null));
        assertEquals(new BigDecimal("2.00"), AmountUtil.add(null, new BigDecimal("2.00")));
        
        // 两个都为null的情况
        assertEquals(BigDecimal.ZERO, AmountUtil.add(null, null));
        
        // 正负数相加
        assertEquals(new BigDecimal("0.00"), AmountUtil.add(new BigDecimal("1.00"), new BigDecimal("-1.00")));
        assertEquals(new BigDecimal("1.00"), AmountUtil.add(new BigDecimal("2.00"), new BigDecimal("-1.00")));
    }

    @Test
    @DisplayName("安全的金额相减")
    void testSubtract() {
        // 正常情况
        assertEquals(new BigDecimal("1.00"), AmountUtil.subtract(new BigDecimal("3.00"), new BigDecimal("2.00")));
        assertEquals(new BigDecimal("0.01"), AmountUtil.subtract(new BigDecimal("0.03"), new BigDecimal("0.02")));
        
        // 一个为null的情况
        assertEquals(new BigDecimal("1.00"), AmountUtil.subtract(new BigDecimal("1.00"), null));
        assertEquals(new BigDecimal("-2.00"), AmountUtil.subtract(null, new BigDecimal("2.00")));
        
        // 两个都为null的情况
        assertEquals(BigDecimal.ZERO, AmountUtil.subtract(null, null));
        
        // 结果为负数的情况
        assertEquals(new BigDecimal("-1.00"), AmountUtil.subtract(new BigDecimal("1.00"), new BigDecimal("2.00")));
    }

    @Test
    @DisplayName("分元转换的往返测试")
    void testRoundTripConversion() {
        // 测试分->元->分的往返转换
        Long originalFen = 1234L;
        BigDecimal yuan = AmountUtil.fenToYuan(originalFen);
        Long convertedFen = AmountUtil.yuanToFen(yuan);
        assertEquals(originalFen, convertedFen);
        
        // 测试元->分->元的往返转换
        BigDecimal originalYuan = new BigDecimal("12.34");
        Long fen = AmountUtil.yuanToFen(originalYuan);
        BigDecimal convertedYuan = AmountUtil.fenToYuan(fen);
        assertEquals(0, originalYuan.compareTo(convertedYuan));
    }

    @Test
    @DisplayName("精度和四舍五入测试")
    void testPrecisionAndRounding() {
        // 测试四舍五入
        assertEquals(123L, AmountUtil.yuanToFen("1.234")); // 舍
        assertEquals(124L, AmountUtil.yuanToFen("1.235")); // 入
        assertEquals(124L, AmountUtil.yuanToFen("1.236")); // 入
        
        // 测试分转元的精度
        BigDecimal result = AmountUtil.fenToYuan(1L);
        assertEquals(2, result.scale()); // 应该保留2位小数
        assertEquals("0.01", result.toString());
    }
}
