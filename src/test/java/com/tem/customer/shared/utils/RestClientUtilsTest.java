package com.tem.customer.shared.utils;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.MediaType;
import org.springframework.web.client.RestClient;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * RestClientUtils 单元测试
 * 测试HTTP客户端工具类的各种HTTP请求方法
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("HTTP客户端工具类测试")
class RestClientUtilsTest {

    @Mock
    private RestClient restClient;

    @Mock
    private RestClient.RequestHeadersUriSpec<?> requestHeadersUriSpec;

    @Mock
    private RestClient.RequestHeadersSpec<?> requestHeadersSpec;

    @Mock
    private RestClient.RequestBodyUriSpec requestBodyUriSpec;

    @Mock
    private RestClient.RequestBodySpec requestBodySpec;

    @Mock
    private RestClient.ResponseSpec responseSpec;

    private RestClientUtils restClientUtils;

    /**
     * 测试用的响应对象
     */
    static class TestResponse {
        private String message;
        private Integer code;

        public TestResponse() {}

        public TestResponse(String message, Integer code) {
            this.message = message;
            this.code = code;
        }

        // Getters and Setters
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        public Integer getCode() { return code; }
        public void setCode(Integer code) { this.code = code; }
    }

    @BeforeEach
    void setUp() {
        restClientUtils = new RestClientUtils(restClient);
    }

    @Nested
    @DisplayName("GET请求测试")
    class GetRequestTests {

        @Test
        @DisplayName("简单GET请求")
        void testSimpleGetRequest() {
            // 准备测试数据
            String uri = "/api/test";
            TestResponse expectedResponse = new TestResponse("success", 200);

            // 设置Mock行为
            when(restClient.get()).thenReturn(requestHeadersUriSpec);
            when(requestHeadersUriSpec.uri(uri)).thenReturn(requestHeadersSpec);
            when(requestHeadersSpec.retrieve()).thenReturn(responseSpec);
            when(responseSpec.onStatus(any(), any())).thenReturn(responseSpec);
            when(responseSpec.body(TestResponse.class)).thenReturn(expectedResponse);

            // 执行测试
            TestResponse actualResponse = restClientUtils.get(uri, TestResponse.class);

            // 验证结果
            assertNotNull(actualResponse);
            assertEquals(expectedResponse.getMessage(), actualResponse.getMessage());
            assertEquals(expectedResponse.getCode(), actualResponse.getCode());

            // 验证Mock调用
            verify(restClient).get();
            verify(requestHeadersUriSpec).uri(uri);
            verify(requestHeadersSpec).retrieve();
            verify(responseSpec).body(TestResponse.class);
        }

        @Test
        @DisplayName("带URI变量的GET请求")
        void testGetRequestWithUriVariables() {
            // 准备测试数据
            String uri = "/api/test/{id}";
            Object[] uriVariables = {123};
            TestResponse expectedResponse = new TestResponse("success", 200);

            // 设置Mock行为
            when(restClient.get()).thenReturn(requestHeadersUriSpec);
            when(requestHeadersUriSpec.uri(uri, uriVariables)).thenReturn(requestHeadersSpec);
            when(requestHeadersSpec.retrieve()).thenReturn(responseSpec);
            when(responseSpec.onStatus(any(), any())).thenReturn(responseSpec);
            when(responseSpec.body(TestResponse.class)).thenReturn(expectedResponse);

            // 执行测试
            TestResponse actualResponse = restClientUtils.get(uri, uriVariables, TestResponse.class);

            // 验证结果
            assertNotNull(actualResponse);
            assertEquals(expectedResponse.getMessage(), actualResponse.getMessage());

            // 验证Mock调用
            verify(requestHeadersUriSpec).uri(uri, uriVariables);
        }

        @Test
        @DisplayName("带请求头的GET请求")
        void testGetRequestWithHeaders() {
            // 准备测试数据
            String uri = "/api/test";
            Map<String, String> headers = Map.of("Authorization", "Bearer token123");
            TestResponse expectedResponse = new TestResponse("success", 200);

            // 设置Mock行为
            when(restClient.get()).thenReturn(requestHeadersUriSpec);
            when(requestHeadersUriSpec.uri(uri)).thenReturn(requestHeadersSpec);
            when(requestHeadersSpec.headers(any())).thenReturn(requestHeadersSpec);
            when(requestHeadersSpec.retrieve()).thenReturn(responseSpec);
            when(responseSpec.onStatus(any(), any())).thenReturn(responseSpec);
            when(responseSpec.body(TestResponse.class)).thenReturn(expectedResponse);

            // 执行测试
            TestResponse actualResponse = restClientUtils.get(uri, null, headers, TestResponse.class);

            // 验证结果
            assertNotNull(actualResponse);
            assertEquals(expectedResponse.getMessage(), actualResponse.getMessage());

            // 验证Mock调用
            verify(requestHeadersSpec).headers(any());
        }

        @Test
        @DisplayName("GET请求异常处理")
        void testGetRequestException() {
            // 准备测试数据
            String uri = "/api/test";

            // 设置Mock行为 - 抛出异常
            when(restClient.get()).thenReturn(requestHeadersUriSpec);
            when(requestHeadersUriSpec.uri(uri)).thenThrow(new RuntimeException("Network error"));

            // 执行测试并验证异常
            RuntimeException exception = assertThrows(RuntimeException.class, () -> 
                restClientUtils.get(uri, TestResponse.class));
            
            assertTrue(exception.getMessage().contains("GET请求失败"));
        }
    }

    @Nested
    @DisplayName("POST请求测试")
    class PostRequestTests {

        @Test
        @DisplayName("简单POST JSON请求")
        void testSimplePostJsonRequest() {
            // 准备测试数据
            String uri = "/api/test";
            TestResponse requestBody = new TestResponse("request", 100);
            TestResponse expectedResponse = new TestResponse("success", 200);

            // 设置Mock行为
            when(restClient.post()).thenReturn(requestBodyUriSpec);
            when(requestBodyUriSpec.uri(uri)).thenReturn(requestBodySpec);
            when(requestBodySpec.contentType(MediaType.APPLICATION_JSON)).thenReturn(requestBodySpec);
            when(requestBodySpec.body(anyString())).thenReturn(responseSpec);
            when(responseSpec.retrieve()).thenReturn(responseSpec);
            when(responseSpec.onStatus(any(), any())).thenReturn(responseSpec);
            when(responseSpec.body(TestResponse.class)).thenReturn(expectedResponse);

            // 执行测试
            TestResponse actualResponse = restClientUtils.postJson(uri, requestBody, TestResponse.class);

            // 验证结果
            assertNotNull(actualResponse);
            assertEquals(expectedResponse.getMessage(), actualResponse.getMessage());

            // 验证Mock调用
            verify(restClient).post();
            verify(requestBodyUriSpec).uri(uri);
            verify(requestBodySpec).contentType(MediaType.APPLICATION_JSON);
            verify(requestBodySpec).body(anyString());
        }

        @Test
        @DisplayName("POST请求使用字符串请求体")
        void testPostJsonRequestWithStringBody() {
            // 准备测试数据
            String uri = "/api/test";
            String requestBody = "{\"message\":\"test\",\"code\":100}";
            TestResponse expectedResponse = new TestResponse("success", 200);

            // 设置Mock行为
            when(restClient.post()).thenReturn(requestBodyUriSpec);
            when(requestBodyUriSpec.uri(uri)).thenReturn(requestBodySpec);
            when(requestBodySpec.contentType(MediaType.APPLICATION_JSON)).thenReturn(requestBodySpec);
            when(requestBodySpec.body(requestBody)).thenReturn(responseSpec);
            when(responseSpec.retrieve()).thenReturn(responseSpec);
            when(responseSpec.onStatus(any(), any())).thenReturn(responseSpec);
            when(responseSpec.body(TestResponse.class)).thenReturn(expectedResponse);

            // 执行测试
            TestResponse actualResponse = restClientUtils.postJson(uri, requestBody, TestResponse.class);

            // 验证结果
            assertNotNull(actualResponse);
            assertEquals(expectedResponse.getMessage(), actualResponse.getMessage());

            // 验证Mock调用
            verify(requestBodySpec).body(requestBody);
        }

        @Test
        @DisplayName("带URI变量和请求头的POST请求")
        void testPostJsonRequestWithUriVariablesAndHeaders() {
            // 准备测试数据
            String uri = "/api/test/{id}";
            Object[] uriVariables = {123};
            TestResponse requestBody = new TestResponse("request", 100);
            Map<String, String> headers = Map.of("Authorization", "Bearer token123");
            TestResponse expectedResponse = new TestResponse("success", 200);

            // 设置Mock行为
            when(restClient.post()).thenReturn(requestBodyUriSpec);
            when(requestBodyUriSpec.uri(uri, uriVariables)).thenReturn(requestBodySpec);
            when(requestBodySpec.contentType(MediaType.APPLICATION_JSON)).thenReturn(requestBodySpec);
            when(requestBodySpec.headers(any())).thenReturn(requestBodySpec);
            when(requestBodySpec.body(anyString())).thenReturn(responseSpec);
            when(responseSpec.retrieve()).thenReturn(responseSpec);
            when(responseSpec.onStatus(any(), any())).thenReturn(responseSpec);
            when(responseSpec.body(TestResponse.class)).thenReturn(expectedResponse);

            // 执行测试
            TestResponse actualResponse = restClientUtils.postJson(uri, uriVariables, requestBody, headers, TestResponse.class);

            // 验证结果
            assertNotNull(actualResponse);
            assertEquals(expectedResponse.getMessage(), actualResponse.getMessage());

            // 验证Mock调用
            verify(requestBodyUriSpec).uri(uri, uriVariables);
            verify(requestBodySpec).headers(any());
        }

        @Test
        @DisplayName("POST请求异常处理")
        void testPostRequestException() {
            // 准备测试数据
            String uri = "/api/test";
            TestResponse requestBody = new TestResponse("request", 100);

            // 设置Mock行为 - 抛出异常
            when(restClient.post()).thenReturn(requestBodyUriSpec);
            when(requestBodyUriSpec.uri(uri)).thenThrow(new RuntimeException("Network error"));

            // 执行测试并验证异常
            RuntimeException exception = assertThrows(RuntimeException.class, () -> 
                restClientUtils.postJson(uri, requestBody, TestResponse.class));
            
            assertTrue(exception.getMessage().contains("POST请求失败"));
        }
    }

    @Nested
    @DisplayName("PUT请求测试")
    class PutRequestTests {

        @Test
        @DisplayName("简单PUT JSON请求")
        void testSimplePutJsonRequest() {
            // 准备测试数据
            String uri = "/api/test";
            TestResponse requestBody = new TestResponse("update", 100);
            TestResponse expectedResponse = new TestResponse("updated", 200);

            // 设置Mock行为
            when(restClient.put()).thenReturn(requestBodyUriSpec);
            when(requestBodyUriSpec.uri(uri)).thenReturn(requestBodySpec);
            when(requestBodySpec.contentType(MediaType.APPLICATION_JSON)).thenReturn(requestBodySpec);
            when(requestBodySpec.body(anyString())).thenReturn(responseSpec);
            when(responseSpec.retrieve()).thenReturn(responseSpec);
            when(responseSpec.onStatus(any(), any())).thenReturn(responseSpec);
            when(responseSpec.body(TestResponse.class)).thenReturn(expectedResponse);

            // 执行测试
            TestResponse actualResponse = restClientUtils.putJson(uri, requestBody, TestResponse.class);

            // 验证结果
            assertNotNull(actualResponse);
            assertEquals(expectedResponse.getMessage(), actualResponse.getMessage());

            // 验证Mock调用
            verify(restClient).put();
            verify(requestBodyUriSpec).uri(uri);
            verify(requestBodySpec).contentType(MediaType.APPLICATION_JSON);
        }

        @Test
        @DisplayName("PUT请求异常处理")
        void testPutRequestException() {
            // 准备测试数据
            String uri = "/api/test";
            TestResponse requestBody = new TestResponse("update", 100);

            // 设置Mock行为 - 抛出异常
            when(restClient.put()).thenReturn(requestBodyUriSpec);
            when(requestBodyUriSpec.uri(uri)).thenThrow(new RuntimeException("Network error"));

            // 执行测试并验证异常
            RuntimeException exception = assertThrows(RuntimeException.class, () -> 
                restClientUtils.putJson(uri, requestBody, TestResponse.class));
            
            assertTrue(exception.getMessage().contains("PUT请求失败"));
        }
    }

    @Nested
    @DisplayName("DELETE请求测试")
    class DeleteRequestTests {

        @Test
        @DisplayName("简单DELETE请求")
        void testSimpleDeleteRequest() {
            // 准备测试数据
            String uri = "/api/test";
            TestResponse expectedResponse = new TestResponse("deleted", 200);

            // 设置Mock行为
            when(restClient.delete()).thenReturn(requestHeadersUriSpec);
            when(requestHeadersUriSpec.uri(uri)).thenReturn(requestHeadersSpec);
            when(requestHeadersSpec.retrieve()).thenReturn(responseSpec);
            when(responseSpec.onStatus(any(), any())).thenReturn(responseSpec);
            when(responseSpec.body(TestResponse.class)).thenReturn(expectedResponse);

            // 执行测试
            TestResponse actualResponse = restClientUtils.delete(uri, TestResponse.class);

            // 验证结果
            assertNotNull(actualResponse);
            assertEquals(expectedResponse.getMessage(), actualResponse.getMessage());

            // 验证Mock调用
            verify(restClient).delete();
            verify(requestHeadersUriSpec).uri(uri);
        }

        @Test
        @DisplayName("带URI变量和请求头的DELETE请求")
        void testDeleteRequestWithUriVariablesAndHeaders() {
            // 准备测试数据
            String uri = "/api/test/{id}";
            Object[] uriVariables = {123};
            Map<String, String> headers = Map.of("Authorization", "Bearer token123");
            TestResponse expectedResponse = new TestResponse("deleted", 200);

            // 设置Mock行为
            when(restClient.delete()).thenReturn(requestHeadersUriSpec);
            when(requestHeadersUriSpec.uri(uri, uriVariables)).thenReturn(requestHeadersSpec);
            when(requestHeadersSpec.headers(any())).thenReturn(requestHeadersSpec);
            when(requestHeadersSpec.retrieve()).thenReturn(responseSpec);
            when(responseSpec.onStatus(any(), any())).thenReturn(responseSpec);
            when(responseSpec.body(TestResponse.class)).thenReturn(expectedResponse);

            // 执行测试
            TestResponse actualResponse = restClientUtils.delete(uri, uriVariables, headers, TestResponse.class);

            // 验证结果
            assertNotNull(actualResponse);
            assertEquals(expectedResponse.getMessage(), actualResponse.getMessage());

            // 验证Mock调用
            verify(requestHeadersUriSpec).uri(uri, uriVariables);
            verify(requestHeadersSpec).headers(any());
        }

        @Test
        @DisplayName("DELETE请求异常处理")
        void testDeleteRequestException() {
            // 准备测试数据
            String uri = "/api/test";

            // 设置Mock行为 - 抛出异常
            when(restClient.delete()).thenReturn(requestHeadersUriSpec);
            when(requestHeadersUriSpec.uri(uri)).thenThrow(new RuntimeException("Network error"));

            // 执行测试并验证异常
            RuntimeException exception = assertThrows(RuntimeException.class, () -> 
                restClientUtils.delete(uri, TestResponse.class));
            
            assertTrue(exception.getMessage().contains("DELETE请求失败"));
        }
    }
}
