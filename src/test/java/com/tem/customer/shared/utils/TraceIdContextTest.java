package com.tem.customer.shared.utils;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.slf4j.MDC;

import java.util.Map;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicReference;

import static org.junit.jupiter.api.Assertions.*;

/**
 * TraceIdContext 单元测试
 * 测试TraceId上下文工具类的各种功能
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
@DisplayName("TraceId上下文工具类测试")
class TraceIdContextTest {

    @BeforeEach
    void setUp() {
        // 每个测试前清理MDC
        MDC.clear();
    }

    @AfterEach
    void tearDown() {
        // 每个测试后清理MDC
        MDC.clear();
    }

    @Nested
    @DisplayName("TraceId基本操作测试")
    class BasicTraceIdOperationsTests {

        @Test
        @DisplayName("获取当前TraceId - 未设置时返回null")
        void testGetCurrentTraceIdWhenNotSet() {
            String traceId = TraceIdContext.getCurrentTraceId();
            assertNull(traceId);
        }

        @Test
        @DisplayName("设置和获取TraceId")
        void testSetAndGetTraceId() {
            String expectedTraceId = "test-trace-id-123";
            TraceIdContext.setTraceId(expectedTraceId);

            String actualTraceId = TraceIdContext.getCurrentTraceId();
            assertEquals(expectedTraceId, actualTraceId);
        }

        @Test
        @DisplayName("设置空白TraceId")
        void testSetBlankTraceId() {
            TraceIdContext.setTraceId("");
            assertNull(TraceIdContext.getCurrentTraceId());

            TraceIdContext.setTraceId("   ");
            assertNull(TraceIdContext.getCurrentTraceId());

            TraceIdContext.setTraceId(null);
            assertNull(TraceIdContext.getCurrentTraceId());
        }

        @Test
        @DisplayName("设置带空格的TraceId会被trim")
        void testSetTraceIdWithSpaces() {
            String traceIdWithSpaces = "  test-trace-id  ";
            TraceIdContext.setTraceId(traceIdWithSpaces);

            String actualTraceId = TraceIdContext.getCurrentTraceId();
            assertEquals("test-trace-id", actualTraceId);
        }

        @Test
        @DisplayName("清理TraceId")
        void testClearTraceId() {
            TraceIdContext.setTraceId("test-trace-id");
            assertNotNull(TraceIdContext.getCurrentTraceId());

            TraceIdContext.clearTraceId();
            assertNull(TraceIdContext.getCurrentTraceId());
        }

        @Test
        @DisplayName("清理不存在的TraceId")
        void testClearNonExistentTraceId() {
            // 清理不存在的TraceId应该不抛出异常
            assertDoesNotThrow(() -> TraceIdContext.clearTraceId());
        }
    }

    @Nested
    @DisplayName("TraceId生成测试")
    class TraceIdGenerationTests {

        @Test
        @DisplayName("生成并设置新的TraceId")
        void testGenerateAndSetTraceId() {
            String traceId = TraceIdContext.generateAndSetTraceId();

            assertNotNull(traceId);
            assertFalse(traceId.isEmpty());
            assertEquals(traceId, TraceIdContext.getCurrentTraceId());
            assertEquals(22, traceId.length()); // ShortUUID生成22位
        }

        @Test
        @DisplayName("生成并设置带时间戳的TraceId")
        void testGenerateAndSetTimestampTraceId() {
            String traceId = TraceIdContext.generateAndSetTimestampTraceId();

            assertNotNull(traceId);
            assertFalse(traceId.isEmpty());
            assertEquals(traceId, TraceIdContext.getCurrentTraceId());
            assertEquals(22, traceId.length());
        }

        @Test
        @DisplayName("生成并设置不含特殊字符的TraceId")
        void testGenerateAndSetCleanTraceId() {
            String traceId = TraceIdContext.generateAndSetCleanTraceId();

            assertNotNull(traceId);
            assertFalse(traceId.isEmpty());
            assertEquals(traceId, TraceIdContext.getCurrentTraceId());
            assertEquals(22, traceId.length());
            assertFalse(traceId.contains("-"));
            assertFalse(traceId.contains("_"));
        }

        @Test
        @DisplayName("生成并设置不含特殊字符的带时间戳TraceId")
        void testGenerateAndSetCleanTimestampTraceId() {
            String traceId = TraceIdContext.generateAndSetCleanTimestampTraceId();

            assertNotNull(traceId);
            assertFalse(traceId.isEmpty());
            assertEquals(traceId, TraceIdContext.getCurrentTraceId());
            assertEquals(22, traceId.length());
            assertFalse(traceId.contains("-"));
            assertFalse(traceId.contains("_"));
        }

        @Test
        @DisplayName("多次生成的TraceId应该不同")
        void testMultipleGeneratedTraceIdsAreDifferent() {
            String traceId1 = TraceIdContext.generateAndSetTraceId();
            String traceId2 = TraceIdContext.generateAndSetTraceId();
            String traceId3 = TraceIdContext.generateAndSetTraceId();

            assertNotEquals(traceId1, traceId2);
            assertNotEquals(traceId2, traceId3);
            assertNotEquals(traceId1, traceId3);
        }
    }

    @Nested
    @DisplayName("MDC操作测试")
    class MdcOperationsTests {

        @Test
        @DisplayName("复制MDC上下文")
        void testCopyMDC() {
            MDC.put("key1", "value1");
            MDC.put("key2", "value2");
            TraceIdContext.setTraceId("test-trace-id");

            Map<String, String> copiedMDC = TraceIdContext.copyMDC();

            assertNotNull(copiedMDC);
            assertEquals("value1", copiedMDC.get("key1"));
            assertEquals("value2", copiedMDC.get("key2"));
            assertEquals("test-trace-id", copiedMDC.get("traceId"));
        }

        @Test
        @DisplayName("复制空的MDC上下文")
        void testCopyEmptyMDC() {
            Map<String, String> copiedMDC = TraceIdContext.copyMDC();
            // 空的MDC可能返回null或空Map
            assertTrue(copiedMDC == null || copiedMDC.isEmpty());
        }

        @Test
        @DisplayName("恢复MDC上下文")
        void testRestoreMDC() {
            // 设置初始状态
            MDC.put("key1", "value1");
            TraceIdContext.setTraceId("original-trace-id");
            Map<String, String> originalMDC = TraceIdContext.copyMDC();

            // 修改MDC
            MDC.clear();
            MDC.put("key2", "value2");
            TraceIdContext.setTraceId("new-trace-id");

            // 恢复原始MDC
            TraceIdContext.restoreMDC(originalMDC);

            assertEquals("value1", MDC.get("key1"));
            assertEquals("original-trace-id", TraceIdContext.getCurrentTraceId());
            assertNull(MDC.get("key2"));
        }

        @Test
        @DisplayName("恢复null MDC上下文")
        void testRestoreNullMDC() {
            MDC.put("key1", "value1");
            TraceIdContext.setTraceId("test-trace-id");

            TraceIdContext.restoreMDC(null);

            // 恢复null应该清空MDC
            assertNull(MDC.get("key1"));
            assertNull(TraceIdContext.getCurrentTraceId());
        }
    }

    @Nested
    @DisplayName("任务包装测试")
    class TaskWrappingTests {

        @Test
        @DisplayName("包装Runnable任务")
        void testWrapRunnableWithTraceId() {
            String originalTraceId = "original-trace-id";
            TraceIdContext.setTraceId(originalTraceId);

            AtomicReference<String> capturedTraceId = new AtomicReference<>();
            Runnable originalTask = () -> capturedTraceId.set(TraceIdContext.getCurrentTraceId());

            Runnable wrappedTask = TraceIdContext.wrapWithTraceId(originalTask);
            assertNotNull(wrappedTask);

            // 清除当前TraceId
            TraceIdContext.clearTraceId();

            // 执行包装后的任务
            wrappedTask.run();

            // 验证任务中的TraceId是原始的TraceId
            assertEquals(originalTraceId, capturedTraceId.get());
        }

        @Test
        @DisplayName("包装Callable任务")
        void testWrapCallableWithTraceId() throws Exception {
            String originalTraceId = "original-trace-id";
            TraceIdContext.setTraceId(originalTraceId);

            Callable<String> originalTask = () -> TraceIdContext.getCurrentTraceId();

            Callable<String> wrappedTask = TraceIdContext.wrapWithTraceId(originalTask);
            assertNotNull(wrappedTask);

            // 清除当前TraceId
            TraceIdContext.clearTraceId();

            // 执行包装后的任务
            String result = wrappedTask.call();

            // 验证任务中的TraceId是原始的TraceId
            assertEquals(originalTraceId, result);
        }

        @Test
        @DisplayName("包装null任务")
        void testWrapNullTask() {
            Runnable wrappedRunnable = TraceIdContext.wrapWithTraceId((Runnable) null);
            assertNull(wrappedRunnable);

            Callable<String> wrappedCallable = TraceIdContext.wrapWithTraceId((Callable<String>) null);
            assertNull(wrappedCallable);
        }

        @Test
        @DisplayName("使用指定TraceId包装任务")
        void testWrapWithSpecificTraceId() {
            String specificTraceId = "specific-trace-id";
            AtomicReference<String> capturedTraceId = new AtomicReference<>();

            Runnable originalTask = () -> capturedTraceId.set(TraceIdContext.getCurrentTraceId());
            Runnable wrappedTask = TraceIdContext.wrapWithTraceId(originalTask, specificTraceId);

            wrappedTask.run();

            assertEquals(specificTraceId, capturedTraceId.get());
        }

        @Test
        @DisplayName("包装任务时自动生成TraceId")
        void testWrapWithAutoGeneratedTraceId() {
            AtomicReference<String> capturedTraceId = new AtomicReference<>();

            Runnable originalTask = () -> capturedTraceId.set(TraceIdContext.getCurrentTraceId());
            Runnable wrappedTask = TraceIdContext.wrapWithTraceId(originalTask, null);

            wrappedTask.run();

            String generatedTraceId = capturedTraceId.get();
            assertNotNull(generatedTraceId);
            assertFalse(generatedTraceId.isEmpty());
            assertEquals(22, generatedTraceId.length());
        }
    }

    @Nested
    @DisplayName("上下文执行测试")
    class ContextExecutionTests {

        @Test
        @DisplayName("在指定TraceId上下文中执行Runnable")
        void testRunWithTraceId() {
            String specificTraceId = "specific-trace-id";
            String originalTraceId = "original-trace-id";
            TraceIdContext.setTraceId(originalTraceId);

            AtomicReference<String> capturedTraceId = new AtomicReference<>();

            TraceIdContext.runWithTraceId(specificTraceId, () -> {
                capturedTraceId.set(TraceIdContext.getCurrentTraceId());
            });

            // 验证任务中的TraceId是指定的TraceId
            assertEquals(specificTraceId, capturedTraceId.get());

            // 验证原始TraceId被恢复
            assertEquals(originalTraceId, TraceIdContext.getCurrentTraceId());
        }

        @Test
        @DisplayName("在指定TraceId上下文中执行Callable")
        void testCallWithTraceId() throws Exception {
            String specificTraceId = "specific-trace-id";
            String originalTraceId = "original-trace-id";
            TraceIdContext.setTraceId(originalTraceId);

            String result = TraceIdContext.callWithTraceId(specificTraceId, () -> {
                return TraceIdContext.getCurrentTraceId() + "-result";
            });

            // 验证任务中的TraceId是指定的TraceId
            assertEquals(specificTraceId + "-result", result);

            // 验证原始TraceId被恢复
            assertEquals(originalTraceId, TraceIdContext.getCurrentTraceId());
        }

        @Test
        @DisplayName("上下文执行中的异常处理")
        void testContextExecutionExceptionHandling() {
            String originalTraceId = "original-trace-id";
            TraceIdContext.setTraceId(originalTraceId);

            // Runnable异常处理
            assertThrows(RuntimeException.class, () -> {
                TraceIdContext.runWithTraceId("test-trace-id", () -> {
                    throw new RuntimeException("Test exception");
                });
            });

            // 验证异常后原始TraceId被恢复
            assertEquals(originalTraceId, TraceIdContext.getCurrentTraceId());

            // Callable异常处理
            assertThrows(Exception.class, () -> {
                TraceIdContext.callWithTraceId("test-trace-id", () -> {
                    throw new Exception("Test exception");
                });
            });

            // 验证异常后原始TraceId被恢复
            assertEquals(originalTraceId, TraceIdContext.getCurrentTraceId());
        }
    }

    @Nested
    @DisplayName("并发测试")
    class ConcurrencyTests {

        @Test
        @DisplayName("并发执行包装任务")
        void testConcurrentWrappedTasks() throws InterruptedException {
            int threadCount = 10;
            ExecutorService executor = Executors.newFixedThreadPool(threadCount);
            CountDownLatch latch = new CountDownLatch(threadCount);
            ConcurrentHashMap<String, String> results = new ConcurrentHashMap<>();

            for (int i = 0; i < threadCount; i++) {
                final String traceId = "trace-id-" + i;
                Runnable task = () -> results.put(traceId, TraceIdContext.getCurrentTraceId());
                Runnable wrappedTask = TraceIdContext.wrapWithTraceId(task, traceId);

                executor.submit(() -> {
                    try {
                        wrappedTask.run();
                    } finally {
                        latch.countDown();
                    }
                });
            }

            latch.await(5, TimeUnit.SECONDS);
            executor.shutdown();

            // 验证每个任务都获得了正确的TraceId
            assertEquals(threadCount, results.size());
            for (int i = 0; i < threadCount; i++) {
                String expectedTraceId = "trace-id-" + i;
                assertEquals(expectedTraceId, results.get(expectedTraceId));
            }
        }
    }
}
