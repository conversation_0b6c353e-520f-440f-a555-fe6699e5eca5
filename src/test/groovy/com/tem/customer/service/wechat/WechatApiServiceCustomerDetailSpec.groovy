package com.tem.customer.service.wechat

import com.iplatform.common.global.cache.GlobalCacheUtil
import com.iplatform.common.utils.LogUtils
import com.tem.customer.infrastructure.config.WechatApiProperties
import com.tem.customer.model.dto.wechat.WechatCustomerDetailRequest
import com.tem.customer.model.dto.wechat.WechatCustomerDetailResponse
import com.tem.customer.shared.exception.BusinessException
import com.tem.customer.shared.utils.RestClientUtils
import org.mockito.Mockito
import org.spockframework.spring.SpringBean
import spock.lang.Specification
import spock.lang.Subject
import spock.lang.Unroll

/**
 * WechatApiService客户详情功能单元测试
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
class WechatApiServiceCustomerDetailSpec extends Specification {

    @Subject
    WechatApiService wechatApiService

    @SpringBean
    WechatApiProperties wechatApiProperties = Mock()

    @SpringBean
    RestClientUtils restClientUtils = Mock()

    def setup() {
        // 使用构造函数创建实例
        wechatApiService = new WechatApiServiceImpl(wechatApiProperties, restClientUtils)
        
        // Mock静态方法
        Mockito.mockStatic(GlobalCacheUtil.class)
        Mockito.mockStatic(LogUtils.class)
    }

    def cleanup() {
        // 清理静态方法Mock
        Mockito.clearAllCaches()
    }

    // ==================== 基础参数验证测试 ====================

    def "获取客户详情 - API功能未启用"() {
        given: "API功能未启用"
        wechatApiProperties.isEnabled() >> false

        and: "有效的请求参数"
        def request = new WechatCustomerDetailRequest("test_external_user_id")

        when: "调用获取客户详情方法"
        wechatApiService.getCustomerDetail(request)

        then: "抛出业务异常"
        def exception = thrown(BusinessException)
        exception.message == "企业微信API功能未启用"
    }

    def "获取客户详情 - 请求参数为null"() {
        given: "API功能启用"
        wechatApiProperties.isEnabled() >> true

        when: "调用获取客户详情方法，参数为null"
        wechatApiService.getCustomerDetail((WechatCustomerDetailRequest) null)

        then: "抛出业务异常"
        def exception = thrown(BusinessException)
        exception.message == "外部联系人ID不能为空"
    }

    def "获取客户详情 - externalUserId为空"() {
        given: "API功能启用"
        wechatApiProperties.isEnabled() >> true

        and: "externalUserId为空的请求"
        def request = new WechatCustomerDetailRequest("")

        when: "调用获取客户详情方法"
        wechatApiService.getCustomerDetail(request)

        then: "抛出业务异常"
        def exception = thrown(BusinessException)
        exception.message == "外部联系人ID不能为空"
    }

    def "获取客户详情 - externalUserId为null"() {
        given: "API功能启用"
        wechatApiProperties.isEnabled() >> true

        and: "externalUserId为null的请求"
        def request = new WechatCustomerDetailRequest()
        request.setExternalUserId(null)

        when: "调用获取客户详情方法"
        wechatApiService.getCustomerDetail(request)

        then: "抛出业务异常"
        def exception = thrown(BusinessException)
        exception.message == "外部联系人ID不能为空"
    }

    // ==================== 客户详情获取功能测试（成功场景） ====================

    def "获取客户详情 - 成功场景"() {
        given: "API功能启用"
        wechatApiProperties.isEnabled() >> true

        and: "有效的请求参数"
        def request = new WechatCustomerDetailRequest("test_external_user_id")

        and: "模拟获取Access Token成功"
        def spyService = Spy(wechatApiService)
        spyService.getAccessToken() >> "mock_access_token"
        wechatApiService = spyService

        and: "模拟HTTP调用成功"
        def mockResponse = createMockCustomerDetailResponse()
        restClientUtils.postJson(
            "/cgi-bin/externalcontact/get?access_token={token}",
            ["mock_access_token"] as Object[],
            request,
            WechatCustomerDetailResponse.class
        ) >> mockResponse

        when: "调用获取客户详情方法"
        def result = wechatApiService.getCustomerDetail(request)

        then: "验证返回结果"
        result != null
        result.isSuccess()
        result.externalContact != null
        result.externalContact.externalUserId == "test_external_user_id"
        result.externalContact.name == "测试用户"
        result.externalContact.avatar == "https://test.avatar.url"
        result.externalContact.type == 1
        result.externalContact.gender == 1
        result.externalContact.unionId == "test_union_id"
    }

    // ==================== 客户详情获取错误处理测试 ====================

    def "获取客户详情 - 企业微信API返回错误"() {
        given: "API功能启用"
        wechatApiProperties.isEnabled() >> true

        and: "有效的请求参数"
        def request = new WechatCustomerDetailRequest("test_external_user_id")

        and: "模拟获取Access Token成功"
        def spyService = Spy(wechatApiService)
        spyService.getAccessToken() >> "mock_access_token"
        wechatApiService = spyService

        and: "模拟企业微信API返回错误"
        def errorResponse = new WechatCustomerDetailResponse()
        errorResponse.setErrCode(40003)
        errorResponse.setErrMsg("invalid external_userid")
        restClientUtils.postJson(_, _, _, _) >> errorResponse

        when: "调用获取客户详情方法"
        wechatApiService.getCustomerDetail(request)

        then: "抛出业务异常"
        def exception = thrown(BusinessException)
        exception.message.contains("获取客户详情失败")
        exception.message.contains("错误码: 40003")
    }

    def "获取客户详情 - HTTP调用返回null"() {
        given: "API功能启用"
        wechatApiProperties.isEnabled() >> true

        and: "有效的请求参数"
        def request = new WechatCustomerDetailRequest("test_external_user_id")

        and: "模拟获取Access Token成功"
        def spyService = Spy(wechatApiService)
        spyService.getAccessToken() >> "mock_access_token"
        wechatApiService = spyService

        and: "模拟HTTP调用返回null"
        restClientUtils.postJson(_, _, _, _) >> null

        when: "调用获取客户详情方法"
        wechatApiService.getCustomerDetail(request)

        then: "抛出业务异常"
        def exception = thrown(BusinessException)
        exception.message == "获取客户详情失败: 企业微信API返回空响应"
    }

    def "获取客户详情 - HTTP调用异常"() {
        given: "API功能启用"
        wechatApiProperties.isEnabled() >> true

        and: "有效的请求参数"
        def request = new WechatCustomerDetailRequest("test_external_user_id")

        and: "模拟获取Access Token成功"
        def spyService = Spy(wechatApiService)
        spyService.getAccessToken() >> "mock_access_token"
        wechatApiService = spyService

        and: "模拟HTTP调用异常"
        restClientUtils.postJson(_, _, _, _) >> { throw new RuntimeException("Network timeout") }

        when: "调用获取客户详情方法"
        wechatApiService.getCustomerDetail(request)

        then: "抛出业务异常"
        def exception = thrown(BusinessException)
        exception.message.contains("获取客户详情失败")
        exception.message.contains("Network timeout")
    }

    // ==================== 重载方法测试 ====================

    def "获取客户详情重载方法 - 仅使用externalUserId"() {
        given: "API功能启用"
        wechatApiProperties.isEnabled() >> true

        and: "Mock主方法"
        def expectedRequest = new WechatCustomerDetailRequest("test_external_user_id")
        def mockResponse = createMockCustomerDetailResponse()
        
        // 使用Spy来Mock主方法调用
        def spyService = Spy(wechatApiService)
        spyService.getCustomerDetail(expectedRequest) >> mockResponse

        when: "调用重载方法"
        def result = spyService.getCustomerDetail("test_external_user_id")

        then: "验证调用主方法并返回正确结果"
        result == mockResponse
    }

    // ==================== 辅助方法 ====================

    /**
     * 创建模拟的客户详情响应
     */
    def createMockCustomerDetailResponse() {
        def response = new WechatCustomerDetailResponse()
        response.setErrCode(0)
        response.setErrMsg("ok")
        
        def externalContact = new WechatCustomerDetailResponse.ExternalContact()
        externalContact.setExternalUserId("test_external_user_id")
        externalContact.setName("测试用户")
        externalContact.setAvatar("https://test.avatar.url")
        externalContact.setType(1)
        externalContact.setGender(1)
        externalContact.setUnionId("test_union_id")
        externalContact.setRemark("测试备注")
        externalContact.setDescription("测试描述")
        externalContact.setCorpName("测试公司")
        externalContact.setPosition("测试职位")
        externalContact.setUserId("test_user_id")
        externalContact.setAddTime(System.currentTimeMillis() as Long)
        externalContact.setMarkTime(System.currentTimeMillis() as Long)
        
        response.setExternalContact(externalContact)
        return response
    }
}