package com.tem.customer.service.auth

import com.iplatform.common.utils.LogUtils
import org.redisson.api.RBucket
import org.redisson.api.RedissonClient
import org.spockframework.spring.SpringBean
import org.spockframework.spring.SpringSpy
import spock.lang.Specification
import spock.lang.Subject
import spock.lang.Unroll

import java.time.Duration

/**
 * CaptchaService单元测试
 *
 * <AUTHOR>
 * @since 1.0.0
 */
class CaptchaServiceSpec extends Specification {

    @Subject
    CaptchaService captchaService

    @SpringBean
    RedissonClient redissonClient = Mock()

    @SpringSpy
    LogUtils logUtils = Mock()

    def setup() {
        captchaService = new CaptchaService()
        captchaService.redissonClient = redissonClient
    }

    def "生成图形验证码成功"() {
        given: "验证码key"
        def captchaKey = "test_captcha_key"

        and: "模拟Redis操作"
        def bucket = Mock(RBucket)
        redissonClient.getBucket("captcha:" + captchaKey) >> bucket

        when: "调用生成验证码方法"
        def result = captchaService.generateCaptchaImage(captchaKey)

        then: "验证返回结果不为空"
        result != null
        result.startsWith("data:image/png;base64")

        and: "验证验证码被存储到Redis"
        1 * bucket.set(_, Duration.ofSeconds(300))
    }

    def "生成图形验证码异常情况"() {
        given: "验证码key"
        def captchaKey = "test_captcha_key"

        and: "模拟Redis操作抛出异常"
        redissonClient.getBucket("captcha:" + captchaKey) >> { throw new RuntimeException("Redis connection failed") }

        when: "调用生成验证码方法"
        captchaService.generateCaptchaImage(captchaKey)

        then: "抛出运行时异常"
        thrown(RuntimeException)
    }

    @Unroll
    def "验证图形验证码 - #scenario"() {
        given: "验证码key和用户输入"
        def captchaKey = key
        def userInput = input

        and: "模拟Redis操作"
        def bucket = Mock(RBucket)
        if (captchaKey != null) {
            redissonClient.getBucket("captcha:" + captchaKey) >> bucket
            bucket.get() >> storedCaptcha
        }

        when: "调用验证方法"
        def result = captchaService.validateCaptcha(captchaKey, userInput)

        then: "验证结果"
        result == expectedResult

        and: "验证Redis操作"
        if (expectedResult && captchaKey != null) {
            1 * bucket.delete()
        }

        where:
        scenario               | key              | storedCaptcha | input   | expectedResult
        "验证成功"             | "test_key"       | "ABCD"        | "abcd"  | true
        "验证成功(忽略大小写)" | "test_key"       | "ABCD"        | "ABCD"  | true
        "验证失败"             | "test_key"       | "ABCD"        | "wrong" | false
        "验证码不存在"         | "test_key"       | null          | "abcd"  | false
        "输入为空"             | "test_key"       | "ABCD"        | null    | false
        "key为空"              | null             | "ABCD"        | "abcd"  | false
    }

    def "验证图形验证码异常情况"() {
        given: "验证码key和用户输入"
        def captchaKey = "test_captcha_key"
        def userInput = "abcd"

        and: "模拟Redis操作抛出异常"
        def bucket = Mock(RBucket)
        redissonClient.getBucket("captcha:" + captchaKey) >> { throw new RuntimeException("Redis connection failed") }

        when: "调用验证方法"
        def result = captchaService.validateCaptcha(captchaKey, userInput)

        then: "返回false"
        !result
    }

    @Unroll
    def "检查图形验证码频率限制 - #scenario"() {
        given: "客户端IP"
        def clientIp = "*************"

        and: "模拟Redis操作"
        def bucket = Mock(RBucket)
        redissonClient.getBucket("captcha:rate_limit:" + clientIp) >> bucket
        bucket.get() >> currentCount

        when: "调用频率限制检查方法"
        def result = captchaService.checkCaptchaRateLimit(clientIp)

        then: "验证结果"
        result == expectedResult

        and: "验证Redis操作"
        if (expectedResult && currentCount != null) {
            1 * bucket.set(_, Duration.ofSeconds(60))
        } else if (expectedResult && currentCount == null) {
            1 * bucket.set("1", Duration.ofSeconds(60))
        }

        where:
        scenario   | currentCount | expectedResult
        "首次请求" | null         | true
        "未超限"   | "5"          | true
        "达到限制" | "10"         | false
        "超过限制" | "15"         | false
    }

    def "检查图形验证码频率限制异常情况"() {
        given: "客户端IP"
        def clientIp = "*************"

        and: "模拟Redis操作抛出异常"
        redissonClient.getBucket("captcha:rate_limit:" + clientIp) >> { throw new RuntimeException("Redis connection failed") }

        when: "调用频率限制检查方法"
        def result = captchaService.checkCaptchaRateLimit(clientIp)

        then: "异常情况下允许获取"
        result
    }

    @Unroll
    def "检查短信验证码频率限制 - #scenario"() {
        given: "手机号"
        def mobile = "13800138000"

        and: "模拟Redis操作"
        def bucket = Mock(RBucket)
        redissonClient.getBucket("sms:rate_limit:interval:" + mobile) >> bucket
        bucket.isExists() >> isExists
        bucket.remainTimeToLive() >> remainingTime

        when: "调用频率限制检查方法"
        def result = captchaService.checkSmsRateLimit(mobile)

        then: "验证结果"
        result.allowed() == expectedAllowed
        result.message() == expectedMessage

        and: "验证Redis操作"
        if (expectedAllowed) {
            1 * bucket.set("sent", Duration.ofSeconds(60))
        }

        where:
        scenario             | isExists | remainingTime | expectedAllowed | expectedMessage
        "允许发送"           | false    | 0             | true            | "允许发送"
        "间隔限制"           | true     | 30L           | false           | "发送过于频繁，请30秒后再试"
        "间隔限制(剩余时间)" | true     | 45L           | false           | "发送过于频繁，请45秒后再试"
    }

    def "检查短信验证码频率限制异常情况"() {
        given: "手机号"
        def mobile = "13800138000"

        and: "模拟Redis操作抛出异常"
        redissonClient.getBucket("sms:rate_limit:interval:" + mobile) >> { throw new RuntimeException("Redis connection failed") }

        when: "调用频率限制检查方法"
        def result = captchaService.checkSmsRateLimit(mobile)

        then: "异常情况下允许发送"
        result.allowed()
        result.message() == "允许发送"
    }

    def "SmsRateLimitResult记录类测试"() {
        given: "创建结果对象"
        def result = new CaptchaService.SmsRateLimitResult(true, "测试消息")

        expect: "验证属性"
        result.allowed()
        result.message() == "测试消息"
    }
}