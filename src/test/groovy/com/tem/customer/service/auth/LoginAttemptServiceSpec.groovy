package com.tem.customer.service.auth


import org.redisson.api.RBucket
import org.redisson.api.RedissonClient
import org.spockframework.spring.SpringBean
import spock.lang.Specification
import spock.lang.Subject
import spock.lang.Unroll

import java.time.Duration
/**
 * LoginAttemptService单元测试
 *
 * <AUTHOR>
 * @since 1.0.0
 */
class LoginAttemptServiceSpec extends Specification {

    @Subject
    LoginAttemptService loginAttemptService

    @SpringBean
    RedissonClient redissonClient = Mock()

    def setup() {
        loginAttemptService = new LoginAttemptService(redissonClient)
    }

    def "记录登录失败 - 未达到最大失败次数"() {
        given: "手机号和当前失败次数"
        def mobile = "***********"
        def currentAttempts = 5

        and: "模拟Redis操作"
        def failedAttemptsBucket = Mock(RBucket)
        redissonClient.getBucket("login:failed_attempts:" + mobile) >> failedAttemptsBucket
        failedAttemptsBucket.get() >> currentAttempts.toString()

        when: "调用记录登录失败方法"
        loginAttemptService.recordLoginFailure(mobile)

        then: "验证Redis操作"
        1 * failedAttemptsBucket.set("6", Duration.ofHours(1))
    }

    def "记录登录失败 - 首次失败"() {
        given: "手机号"
        def mobile = "***********"

        and: "模拟Redis操作"
        def failedAttemptsBucket = Mock(RBucket)
        redissonClient.getBucket("login:failed_attempts:" + mobile) >> failedAttemptsBucket
        failedAttemptsBucket.get() >> null

        when: "调用记录登录失败方法"
        loginAttemptService.recordLoginFailure(mobile)

        then: "验证Redis操作"
        1 * failedAttemptsBucket.set("1", Duration.ofHours(1))
    }

    def "记录登录失败 - 达到最大失败次数并锁定账户"() {
        given: "手机号和当前失败次数"
        def mobile = "***********"
        def currentAttempts = 9

        and: "模拟Redis操作"
        def failedAttemptsBucket = Mock(RBucket)
        def lockedBucket = Mock(RBucket)
        redissonClient.getBucket("login:failed_attempts:" + mobile) >> failedAttemptsBucket
        redissonClient.getBucket("login:account_locked:" + mobile) >> lockedBucket
        failedAttemptsBucket.get() >> currentAttempts.toString()

        when: "调用记录登录失败方法"
        loginAttemptService.recordLoginFailure(mobile)

        then: "验证账户被锁定"
        1 * lockedBucket.set("locked", Duration.ofHours(1))
        1 * failedAttemptsBucket.delete()
    }

    def "记录登录失败 - Redis异常情况"() {
        given: "手机号"
        def mobile = "***********"

        and: "模拟Redis操作抛出异常"
        redissonClient.getBucket("login:failed_attempts:" + mobile) >> { throw new RuntimeException("Redis connection failed") }

        when: "调用记录登录失败方法"
        loginAttemptService.recordLoginFailure(mobile)

        then: "记录异常日志"
        noExceptionThrown()
    }

    def "清除登录失败记录"() {
        given: "手机号"
        def mobile = "***********"

        and: "模拟Redis操作"
        def failedAttemptsBucket = Mock(RBucket)
        def lockedBucket = Mock(RBucket)
        redissonClient.getBucket("login:failed_attempts:" + mobile) >> failedAttemptsBucket
        redissonClient.getBucket("login:account_locked:" + mobile) >> lockedBucket

        when: "调用清除登录失败记录方法"
        loginAttemptService.clearLoginFailures(mobile)

        then: "验证Redis操作"
        1 * failedAttemptsBucket.delete()
        1 * lockedBucket.delete()
    }

    def "清除登录失败记录 - Redis异常情况"() {
        given: "手机号"
        def mobile = "***********"

        and: "模拟Redis操作抛出异常"
        redissonClient.getBucket("login:failed_attempts:" + mobile) >> { throw new RuntimeException("Redis connection failed") }

        when: "调用清除登录失败记录方法"
        loginAttemptService.clearLoginFailures(mobile)

        then: "记录异常日志"
        noExceptionThrown()
    }

    @Unroll
    def "检查账户锁定状态 - #scenario"() {
        given: "手机号"
        def mobile = "***********"

        and: "模拟Redis操作"
        def lockedBucket = Mock(RBucket)
        redissonClient.getBucket("login:account_locked:" + mobile) >> lockedBucket
        lockedBucket.isExists() >> isLocked

        when: "调用检查账户锁定状态方法"
        def result = loginAttemptService.isAccountLocked(mobile)

        then: "验证结果"
        result == expectedResult

        and: "验证日志"
        if (isLocked) {
            // 实际日志通过@Slf4j输出，不需要Mock验证
        }

        where:
        scenario         | isLocked | expectedResult
        "账户已锁定"     | true     | true
        "账户未锁定"     | false    | false
    }

    def "检查账户锁定状态 - Redis异常情况"() {
        given: "手机号"
        def mobile = "***********"

        and: "模拟Redis操作抛出异常"
        redissonClient.getBucket("login:account_locked:" + mobile) >> { throw new RuntimeException("Redis connection failed") }

        when: "调用检查账户锁定状态方法"
        def result = loginAttemptService.isAccountLocked(mobile)

        then: "异常情况下返回锁定状态"
        result
    }

    @Unroll
    def "获取当前失败次数 - #scenario"() {
        given: "手机号"
        def mobile = "***********"

        and: "模拟Redis操作"
        def failedAttemptsBucket = Mock(RBucket)
        redissonClient.getBucket("login:failed_attempts:" + mobile) >> failedAttemptsBucket
        failedAttemptsBucket.get() >> storedAttempts

        when: "调用获取失败次数方法"
        def result = loginAttemptService.getFailedAttempts(mobile)

        then: "验证结果"
        result == expectedResult

        where:
        scenario           | storedAttempts | expectedResult
        "有失败记录"       | "5"            | 5
        "没有失败记录"     | null           | 0
    }

    def "获取当前失败次数 - Redis异常情况"() {
        given: "手机号"
        def mobile = "***********"

        and: "模拟Redis操作抛出异常"
        redissonClient.getBucket("login:failed_attempts:" + mobile) >> { throw new RuntimeException("Redis connection failed") }

        when: "调用获取失败次数方法"
        def result = loginAttemptService.getFailedAttempts(mobile)

        then: "异常情况下返回0"
        result == 0
    }

    @Unroll
    def "获取剩余可尝试次数 - #scenario"() {
        given: "手机号"
        def mobile = "***********"

        and: "模拟Redis操作"
        def failedAttemptsBucket = Mock(RBucket)
        redissonClient.getBucket("login:failed_attempts:" + mobile) >> failedAttemptsBucket
        failedAttemptsBucket.get() >> storedAttempts

        when: "调用获取剩余次数方法"
        def result = loginAttemptService.getRemainingAttempts(mobile)

        then: "验证结果"
        result == expectedResult

        where:
        scenario                   | storedAttempts | expectedResult
        "剩余5次"                 | "5"            | 5
        "剩余10次"                | "0"            | 10
        "没有失败记录"            | null           | 10
        "超过最大次数"            | "15"           | 0
    }

    @Unroll
    def "获取账户锁定剩余时间 - #scenario"() {
        given: "手机号"
        def mobile = "***********"

        and: "模拟Redis操作"
        def lockedBucket = Mock(RBucket)
        redissonClient.getBucket("login:account_locked:" + mobile) >> lockedBucket
        lockedBucket.remainTimeToLive() >> remainTime

        when: "调用获取锁定剩余时间方法"
        def result = loginAttemptService.getLockRemainingTime(mobile)

        then: "验证结果"
        result == expectedResult

        where:
        scenario                   | remainTime | expectedResult
        "剩余时间3600秒"          | 3600000L    | 3600
        "未锁定"                  | -1L         | -1
        "剩余时间0秒"             | 0L          | -1
    }

    def "获取账户锁定剩余时间 - Redis异常情况"() {
        given: "手机号"
        def mobile = "***********"

        and: "模拟Redis操作抛出异常"
        redissonClient.getBucket("login:account_locked:" + mobile) >> { throw new RuntimeException("Redis connection failed") }

        when: "调用获取锁定剩余时间方法"
        def result = loginAttemptService.getLockRemainingTime(mobile)

        then: "异常情况下返回-1"
        result == -1
    }
}