package com.tem.customer.service.system

import com.iplatform.common.ResponseDto
import com.tem.imgserver.client.ImgClient
import com.tem.imgserver.client.UploadResult
import com.tem.platform.api.FileService
import com.tem.platform.api.dto.FileDto
import org.mockito.MockedStatic
import org.mockito.Mockito
import org.spockframework.spring.SpringBean
import org.springframework.mock.web.MockMultipartFile
import org.springframework.web.multipart.MultipartFile
import spock.lang.Specification
import spock.lang.Subject
/**
 * ImageService单元测试
 *
 * <AUTHOR>
 * @since 1.0.0
 */
class ImageServiceSpec extends Specification {

    @Subject
    ImageServiceImpl imageService

    @SpringBean
    FileService fileService = Mock()

    MockedStatic<ImgClient> imgClientMock

    def setup() {
        imageService = new ImageServiceImpl()
        imageService.fileService = fileService

        // 使用Mockito进行静态方法mock
        imgClientMock = Mockito.mockStatic(ImgClient.class)
    }

    def cleanup() {
        // 关闭Mockito静态mock，避免测试间相互影响
        if (imgClientMock != null) {
            imgClientMock.close()
        }
    }

    def "上传图片成功"() {
        given: "创建测试文件"
        def file = new MockMultipartFile("test.jpg", "test.jpg", "image/jpeg", "test content".bytes)

        and: "模拟ImgClient上传结果"
        def uploadResult = Mock(UploadResult)
        uploadResult.getFileKey() >> "test-file-key"

        and: "配置ImgClient.uploadImg2方法的mock行为"
        imgClientMock.when(ImgClient.uploadImg2(Mockito.any(InputStream.class), Mockito.eq("customer"), Mockito.any(String.class))).thenReturn(uploadResult)

        and: "模拟FileService.insert方法"
        fileService.insert(_) >> ResponseDto.success(true)

        when: "调用上传方法"
        def result = imageService.uploadImage(file)

        then: "验证返回结果"
        result == "test-file-key"

        and: "验证FileService被调用"
        1 * fileService.insert({ FileDto dto ->
            dto.bizType == "CUSTOMER" &&
            dto.fileName == "test.jpg" &&
            dto.fileType == "jpg" &&
            dto.size == 12
        })
    }

    def "上传图片失败 - 文件为空"() {
        given: "空文件"
        MultipartFile file = null

        when: "调用上传方法"
        imageService.uploadImage(file)

        then: "抛出异常"
        thrown(IllegalArgumentException)
    }

    def "上传图片失败 - 文件为空内容"() {
        given: "空内容文件"
        def file = new MockMultipartFile("empty.jpg", "empty.jpg", "image/jpeg", new byte[0])

        when: "调用上传方法"
        imageService.uploadImage(file)

        then: "抛出异常"
        thrown(IllegalArgumentException)
    }

    def "上传图片失败 - ImgClient异常"() {
        given: "创建测试文件"
        def file = new MockMultipartFile("test.jpg", "test.jpg", "image/jpeg", "test content".bytes)

        and: "配置ImgClient抛出异常"
        imgClientMock.when(ImgClient.uploadImg2(Mockito.any(InputStream.class), Mockito.eq("customer"), Mockito.any(String.class))).thenThrow(new RuntimeException("Upload failed"))

        when: "调用上传方法"
        imageService.uploadImage(file)

        then: "抛出RuntimeException，包含原始异常信息"
        def exception = thrown(RuntimeException)
        exception.message.contains("图片上传失败")
        exception.cause instanceof RuntimeException
        exception.cause.message == "Upload failed"
    }

    def "批量上传图片成功"() {
        given: "创建测试文件列表"
        def file1 = new MockMultipartFile("test1.jpg", "test1.jpg", "image/jpeg", "content1".bytes)
        def file2 = new MockMultipartFile("test2.jpg", "test2.jpg", "image/jpeg", "content2".bytes)
        def files = [file1, file2]

        and: "模拟ImgClient上传结果"
        def uploadResult1 = Mock(UploadResult)
        uploadResult1.getFileKey() >> "file-key-1"
        def uploadResult2 = Mock(UploadResult)
        uploadResult2.getFileKey() >> "file-key-2"

        and: "配置ImgClient.uploadImg2方法返回不同结果"
        imgClientMock.when(ImgClient.uploadImg2(Mockito.any(InputStream.class), Mockito.eq("customer"), Mockito.any(String.class)))
                .thenReturn(uploadResult1)
                .thenReturn(uploadResult2)

        and: "模拟FileService.insert方法"
        fileService.insert(_) >>> [ResponseDto.success(true), ResponseDto.success(true)]

        when: "调用批量上传方法"
        def result = imageService.uploadImages(files)

        then: "验证返回结果"
        result.totalCount == 2
        result.successCount == 2
        result.failCount == 0
        result.successFiles.size() == 2
        result.failedFiles.size() == 0

        and: "验证成功文件信息"
        result.successFiles[0].fileName == "test1.jpg"
        result.successFiles[0].fileKey == "file-key-1"
        result.successFiles[1].fileName == "test2.jpg"
        result.successFiles[1].fileKey == "file-key-2"
    }

    def "批量上传图片 - 部分成功"() {
        given: "创建测试文件列表"
        def file1 = new MockMultipartFile("test1.jpg", "test1.jpg", "image/jpeg", "content1".bytes)
        def file2 = new MockMultipartFile("test2.jpg", "test2.jpg", "image/jpeg", "content2".bytes)
        def files = [file1, file2]

        and: "模拟ImgClient上传结果"
        def uploadResult = Mock(UploadResult)
        uploadResult.getFileKey() >> "file-key-1"

        and: "配置ImgClient.uploadImg2方法，第一次成功，第二次失败"
        imgClientMock.when(ImgClient.uploadImg2(Mockito.any(InputStream.class), Mockito.eq("customer"), Mockito.any(String.class)))
                .thenReturn(uploadResult)
                .thenThrow(new RuntimeException("Upload failed"))

        and: "模拟FileService.insert方法"
        fileService.insert(_) >> ResponseDto.success(true)

        when: "调用批量上传方法"
        def result = imageService.uploadImages(files)

        then: "验证返回结果"
        result.totalCount == 2
        result.successCount == 1
        result.failCount == 1
        result.successFiles.size() == 1
        result.failedFiles.size() == 1

        and: "验证成功文件信息"
        result.successFiles[0].fileName == "test1.jpg"
        result.successFiles[0].fileKey == "file-key-1"

        and: "验证失败文件信息"
        result.failedFiles[0].fileName == "test2.jpg"
        result.failedFiles[0].error == "Upload failed"
    }

    def "批量上传图片 - 空文件列表"() {
        given: "空文件列表"
        List<MultipartFile> files = null

        when: "调用批量上传方法"
        imageService.uploadImages(files)

        then: "抛出异常"
        thrown(IllegalArgumentException)
    }

    def "批量上传图片 - 空文件内容"() {
        given: "包含空文件的列表"
        def file1 = new MockMultipartFile("test1.jpg", "test1.jpg", "image/jpeg", "content1".bytes)
        def file2 = new MockMultipartFile("empty.jpg", "empty.jpg", "image/jpeg", new byte[0])
        def files = [file1, file2]

        and: "模拟ImgClient上传结果"
        def uploadResult = Mock(UploadResult)
        uploadResult.getFileKey() >> "file-key-1"

        and: "配置ImgClient.uploadImg2方法"
        imgClientMock.when(ImgClient.uploadImg2(Mockito.any(InputStream.class), Mockito.eq("customer"), Mockito.any(String.class))).thenReturn(uploadResult)

        and: "模拟FileService.insert方法"
        fileService.insert(_) >> ResponseDto.success(true)

        when: "调用批量上传方法"
        def result = imageService.uploadImages(files)

        then: "验证返回结果"
        result.totalCount == 2
        result.successCount == 1
        result.failCount == 1
        result.successFiles.size() == 1
        result.failedFiles.size() == 1

        and: "验证失败文件信息"
        result.failedFiles[0].fileName == "empty.jpg"
        result.failedFiles[0].error == "文件为空"
    }

    def "批量上传图片 - 文件为null"() {
        given: "包含null文件的列表"
        def file1 = new MockMultipartFile("test1.jpg", "test1.jpg", "image/jpeg", "content1".bytes)
        def files = [file1, null]

        and: "模拟ImgClient上传结果"
        def uploadResult = Mock(UploadResult)
        uploadResult.getFileKey() >> "file-key-1"

        and: "配置ImgClient.uploadImg2方法"
        imgClientMock.when(ImgClient.uploadImg2(Mockito.any(InputStream.class), Mockito.eq("customer"), Mockito.any(String.class))).thenReturn(uploadResult)

        and: "模拟FileService.insert方法"
        fileService.insert(_) >> ResponseDto.success(true)

        when: "调用批量上传方法"
        def result = imageService.uploadImages(files)

        then: "验证返回结果"
        result.totalCount == 2
        result.successCount == 1
        result.failCount == 1
        result.successFiles.size() == 1
        result.failedFiles.size() == 1

        and: "验证失败文件信息"
        result.failedFiles[0].fileName == "unknown"
        result.failedFiles[0].error == "文件为空"
    }

    def "批量上传图片 - 文件名无扩展名"() {
        given: "创建无扩展名的测试文件"
        def file = new MockMultipartFile("test", "test", "image/jpeg", "test content".bytes)
        def files = [file]

        and: "模拟ImgClient上传结果"
        def uploadResult = Mock(UploadResult)
        uploadResult.getFileKey() >> "file-key-1"

        and: "配置ImgClient.uploadImg2方法"
        imgClientMock.when(ImgClient.uploadImg2(Mockito.any(InputStream.class), Mockito.eq("customer"), Mockito.any(String.class))).thenReturn(uploadResult)

        and: "模拟FileService.insert方法"
        fileService.insert(_) >> ResponseDto.success(true)

        when: "调用批量上传方法"
        def result = imageService.uploadImages(files)

        then: "验证返回结果"
        result.totalCount == 1
        result.successCount == 1
        result.failCount == 0
        result.successFiles.size() == 1

        and: "验证FileService被调用，文件类型为空字符串"
        1 * fileService.insert({ FileDto dto ->
            dto.fileName == "test" &&
            dto.fileType == ""
        })
    }

    def "上传图片失败 - ImgClient返回null"() {
        given: "创建测试文件"
        def file = new MockMultipartFile("test.jpg", "test.jpg", "image/jpeg", "test content".bytes)

        and: "配置ImgClient返回null"
        imgClientMock.when(ImgClient.uploadImg2(Mockito.any(InputStream.class), Mockito.eq("customer"), Mockito.any(String.class))).thenReturn(null)

        when: "调用上传方法"
        imageService.uploadImage(file)

        then: "抛出NullPointerException"
        def exception = thrown(RuntimeException)
        exception.message.contains("图片上传失败")
        exception.cause instanceof NullPointerException
    }

    def "上传图片失败 - FileService异常"() {
        given: "创建测试文件"
        def file = new MockMultipartFile("test.jpg", "test.jpg", "image/jpeg", "test content".bytes)

        and: "模拟ImgClient上传结果"
        def uploadResult = Mock(UploadResult)
        uploadResult.getFileKey() >> "test-file-key"

        and: "配置ImgClient.uploadImg2方法"
        imgClientMock.when(ImgClient.uploadImg2(Mockito.any(InputStream.class), Mockito.eq("customer"), Mockito.any(String.class))).thenReturn(uploadResult)

        and: "模拟FileService.insert抛出异常"
        fileService.insert(_) >> { throw new RuntimeException("Database error") }

        when: "调用上传方法"
        imageService.uploadImage(file)

        then: "抛出RuntimeException"
        def exception = thrown(RuntimeException)
        exception.message.contains("图片上传失败")
        exception.cause instanceof RuntimeException
        exception.cause.message == "Database error"
    }

    def "上传图片失败 - 文件流异常"() {
        given: "创建会抛出IOException的文件mock"
        def file = Mock(MultipartFile)
        file.isEmpty() >> false
        file.getOriginalFilename() >> "test.jpg"
        file.getSize() >> 100L
        file.getInputStream() >> { throw new IOException("Stream error") }

        when: "调用上传方法"
        imageService.uploadImage(file)

        then: "抛出RuntimeException"
        def exception = thrown(RuntimeException)
        exception.message.contains("图片上传失败")
        exception.cause instanceof IOException
        exception.cause.message == "Stream error"
    }
}