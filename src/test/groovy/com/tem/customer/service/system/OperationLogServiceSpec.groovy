package com.tem.customer.service.system

import cn.dev33.satoken.stp.StpUtil
import com.baomidou.mybatisplus.extension.plugins.pagination.Page
import com.tem.customer.model.dto.system.OperationLogQueryDTO
import com.tem.customer.repository.entity.OperationLog
import com.tem.customer.repository.mapper.OperationLogMapper
import com.tem.customer.shared.enums.BusinessType
import com.tem.customer.shared.enums.OperationType
import com.tem.customer.shared.utils.SaTokenUserContextUtil
import com.tem.customer.shared.utils.UserContextUtil
import com.tem.customer.shared.utils.WebUtil
import org.mockito.MockedStatic
import org.mockito.Mockito
import org.spockframework.spring.SpringBean
import spock.lang.Specification
import spock.lang.Subject

import java.time.LocalDateTime
/**
 * OperationLogService单元测试
 *
 * <AUTHOR>
 * @since 1.0.0
 */
class OperationLogServiceSpec extends Specification {

    @Subject
    OperationLogServiceImpl operationLogService

    @SpringBean
    OperationLogMapper operationLogMapper = Mock()

    private MockedStatic<StpUtil> mockedStpUtil
    private MockedStatic<SaTokenUserContextUtil> mockedSaTokenUserContextUtil
    private MockedStatic<UserContextUtil> mockedUserContextUtil
    private MockedStatic<WebUtil> mockedWebUtil

    def setup() {
        operationLogService = Spy(OperationLogServiceImpl)
        operationLogService.baseMapper = operationLogMapper
        
        // 初始化Sa-Token上下文
        cn.dev33.satoken.SaManager.setConfig(new cn.dev33.satoken.config.SaTokenConfig())
        
        // 初始化Mockito静态mock
        mockedStpUtil = Mockito.mockStatic(StpUtil.class)
        mockedSaTokenUserContextUtil = Mockito.mockStatic(SaTokenUserContextUtil.class)
        mockedUserContextUtil = Mockito.mockStatic(UserContextUtil.class)
        mockedWebUtil = Mockito.mockStatic(WebUtil.class)
    }

    def cleanup() {
        // 清理Mockito静态mock
        mockedStpUtil?.close()
        mockedSaTokenUserContextUtil?.close()
        mockedUserContextUtil?.close()
        mockedWebUtil?.close()
    }

    def "记录操作日志成功"() {
        given: "测试参数"
        def businessType = BusinessType.PARTNER_NOTE
        def businessId = 123L
        def operationType = OperationType.CREATE
        def description = "测试操作"
        def targetPartnerId = 456L
        def executionTime = 100

        and: "模拟Sa-Token和UserContextUtil、WebUtil"
        mockedStpUtil.when(StpUtil::isLogin).thenReturn(true)
        mockedStpUtil.when(StpUtil::getLoginIdAsLong).thenReturn(789L)

        mockedSaTokenUserContextUtil.when(SaTokenUserContextUtil::getCurrentUserId).thenReturn(789L)
        mockedSaTokenUserContextUtil.when(SaTokenUserContextUtil::getCurrentUserFullname).thenReturn("测试用户")
        mockedSaTokenUserContextUtil.when(SaTokenUserContextUtil::getCurrentUserPartnerId).thenReturn(456L)

        mockedWebUtil.when(WebUtil::getClientIpAddress).thenReturn("***********")
        mockedWebUtil.when(WebUtil::getUserAgent).thenReturn("Test-Agent")
        mockedWebUtil.when(WebUtil::getRequestUri).thenReturn("/test")
        mockedWebUtil.when(WebUtil::getRequestMethod).thenReturn("GET")

        and: "Mock save方法返回true"
        operationLogService.save(_) >> true

        when: "调用记录方法"
        def result = operationLogService.recordLog(businessType, businessId, operationType, description, targetPartnerId, executionTime)

        then: "验证返回结果"
        result == true
    }

    def "记录操作日志失败 - businessId为null"() {
        given: "测试参数"
        def businessType = BusinessType.PARTNER
        Long businessId = null
        def operationType = OperationType.CREATE
        def description = "测试操作"
        def targetPartnerId = 456L
        def executionTime = 100

        when: "调用记录方法"
        def result = operationLogService.recordLog(businessType, businessId, operationType, description, targetPartnerId, executionTime)

        then: "验证返回结果"
        !result

        and: "验证Mapper未被调用"
        0 * operationLogMapper.insert(_)
    }

    def "记录操作日志异常情况"() {
        given: "测试参数"
        def businessType = BusinessType.PARTNER
        def businessId = 123L
        def operationType = OperationType.CREATE
        def description = "测试操作"
        def targetPartnerId = 456L
        def executionTime = 100

        and: "模拟Sa-Token和UserContextUtil、WebUtil"
        mockedStpUtil.when(StpUtil::isLogin).thenReturn(true)
        mockedStpUtil.when(StpUtil::getLoginIdAsLong).thenReturn(789L)

        mockedSaTokenUserContextUtil.when(SaTokenUserContextUtil::getCurrentUserId).thenReturn(789L)
        mockedSaTokenUserContextUtil.when(SaTokenUserContextUtil::getCurrentUserFullname).thenReturn("测试用户")
        mockedSaTokenUserContextUtil.when(SaTokenUserContextUtil::getCurrentUserPartnerId).thenReturn(456L)

        mockedWebUtil.when(WebUtil::getClientIpAddress).thenReturn("***********")
        mockedWebUtil.when(WebUtil::getUserAgent).thenReturn("Test-Agent")
        mockedWebUtil.when(WebUtil::getRequestUri).thenReturn("/test")
        mockedWebUtil.when(WebUtil::getRequestMethod).thenReturn("GET")

        and: "Mock save方法抛出异常"
        operationLogService.save(_) >> { throw new RuntimeException("Database error") }

        when: "调用记录方法"
        def result = operationLogService.recordLog(businessType, businessId, operationType, description, targetPartnerId, executionTime)

        then: "验证返回结果"
        !result
    }

    def "异步记录操作日志"() {
        given: "测试参数"
        def businessType = BusinessType.PARTNER
        def businessId = 123L
        def operationType = OperationType.CREATE
        def description = "测试操作"
        def targetPartnerId = 456L
        def executionTime = 100

        and: "模拟UserContextUtil"
        def userSnapshot = Mock(UserContextUtil.UserContextSnapshot)
        userSnapshot.userId() >> 789L
        userSnapshot.fullname() >> "测试用户"
        userSnapshot.partnerId() >> 456L
        mockedUserContextUtil.when(UserContextUtil::getCurrentUserSnapshot).thenReturn(userSnapshot)

        and: "Mock save方法返回true"
        operationLogService.save(_) >> true

        when: "调用异步记录方法"
        operationLogService.recordLogAsync(businessType, businessId, operationType, description, targetPartnerId, executionTime)

        then: "验证方法被调用（异步方法无法直接验证结果）"
        noExceptionThrown()
    }

    def "根据业务类型和ID查询操作日志"() {
        given: "测试参数"
        def businessType = BusinessType.PARTNER
        def businessId = 123L

        and: "模拟Mapper返回结果"
        def expectedLogs = [
            new OperationLog(id: 1L, businessType: businessType.code, businessId: businessId),
            new OperationLog(id: 2L, businessType: businessType.code, businessId: businessId)
        ]
        operationLogMapper.selectByBusinessTypeAndId(businessType.code, businessId) >> expectedLogs

        when: "调用查询方法"
        def result = operationLogService.listByBusinessTypeAndId(businessType, businessId)

        then: "验证返回结果"
        result.size() == 2
        result[0].id == 1L
        result[1].id == 2L
    }

    def "根据业务类型和ID查询操作日志 - 参数为null"() {
        given: "测试参数"
        BusinessType businessType = null
        Long businessId = null

        when: "调用查询方法"
        def result = operationLogService.listByBusinessTypeAndId(businessType, businessId)

        then: "验证返回空列表"
        result.isEmpty()

        and: "验证Mapper未被调用"
        0 * operationLogMapper.selectByBusinessTypeAndId(_, _)
    }

    def "根据目标企业ID查询操作日志"() {
        given: "测试参数"
        def targetPartnerId = 456L

        and: "模拟Mapper返回结果"
        def expectedLogs = [
            new OperationLog(id: 1L, targetPartnerId: targetPartnerId),
            new OperationLog(id: 2L, targetPartnerId: targetPartnerId)
        ]
        operationLogMapper.selectByTargetPartnerId(targetPartnerId) >> expectedLogs

        when: "调用查询方法"
        def result = operationLogService.listByTargetPartnerId(targetPartnerId)

        then: "验证返回结果"
        result.size() == 2
        result[0].id == 1L
        result[1].id == 2L
    }

    def "根据目标企业ID查询操作日志 - 参数为null"() {
        given: "测试参数"
        Long targetPartnerId = null

        when: "调用查询方法"
        def result = operationLogService.listByTargetPartnerId(targetPartnerId)

        then: "验证返回空列表"
        result.isEmpty()

        and: "验证Mapper未被调用"
        0 * operationLogMapper.selectByTargetPartnerId(_)
    }

    def "根据操作人ID查询操作日志"() {
        given: "测试参数"
        def operatorId = 789L

        and: "模拟Mapper返回结果"
        def expectedLogs = [
            new OperationLog(id: 1L, operatorId: operatorId),
            new OperationLog(id: 2L, operatorId: operatorId)
        ]
        operationLogMapper.selectByOperatorId(operatorId) >> expectedLogs

        when: "调用查询方法"
        def result = operationLogService.listByOperatorId(operatorId)

        then: "验证返回结果"
        result.size() == 2
        result[0].id == 1L
        result[1].id == 2L
    }

    def "根据操作人ID查询操作日志 - 参数为null"() {
        given: "测试参数"
        Long operatorId = null

        when: "调用查询方法"
        def result = operationLogService.listByOperatorId(operatorId)

        then: "验证返回空列表"
        result.isEmpty()

        and: "验证Mapper未被调用"
        0 * operationLogMapper.selectByOperatorId(_)
    }

    def "分页查询操作日志"() {
        given: "测试参数"
        def queryDTO = new OperationLogQueryDTO()
        queryDTO.pageNum = 1
        queryDTO.pageSize = 10
        queryDTO.businessType = BusinessType.PARTNER.code
        queryDTO.operationType = OperationType.CREATE.code
        queryDTO.targetPartnerId = 456L
        queryDTO.operatorId = 789L
        queryDTO.startTime = LocalDateTime.now().minusDays(7)
        queryDTO.endTime = LocalDateTime.now()

        and: "模拟Mapper返回结果"
        def expectedPage = new Page<OperationLog>()
        expectedPage.records = [
            new OperationLog(id: 1L),
            new OperationLog(id: 2L)
        ]
        expectedPage.total = 2
        expectedPage.size = 10
        expectedPage.current = 1

        operationLogMapper.selectPageWithConditions(_, _, _, _, _, _, _) >> expectedPage

        when: "调用分页查询方法"
        def result = operationLogService.pageQuery(queryDTO)

        then: "验证返回结果"
        result.total == 2
        result.size == 10
        result.current == 1
        result.records.size() == 2
    }

    def "分页查询操作日志 - 参数为null"() {
        given: "测试参数为null"
        OperationLogQueryDTO queryDTO = null

        and: "模拟Mapper返回结果"
        def expectedPage = new Page<OperationLog>()
        expectedPage.records = []
        expectedPage.total = 0
        expectedPage.size = 10
        expectedPage.current = 1

        operationLogMapper.selectPageWithConditions(_, _, _, _, _, _, _) >> expectedPage

        when: "调用分页查询方法"
        def result = operationLogService.pageQuery(queryDTO)

        then: "验证返回结果"
        result.total == 0
        result.size == 10
        result.current == 1
        result.records.isEmpty()
    }

    def "分页查询操作日志 - 页面大小超过限制"() {
        given: "测试参数"
        def queryDTO = new OperationLogQueryDTO()
        queryDTO.pageNum = 1
        queryDTO.pageSize = 20000 // 超过MAX_QUERY_LIMIT

        and: "模拟Mapper返回结果"
        def expectedPage = new Page<OperationLog>()
        expectedPage.records = []
        expectedPage.total = 0
        expectedPage.size = 10000 // 应该被限制为MAX_QUERY_LIMIT
        expectedPage.current = 1

        operationLogMapper.selectPageWithConditions(_, _, _, _, _, _, _) >> expectedPage

        when: "调用分页查询方法"
        def result = operationLogService.pageQuery(queryDTO)

        then: "验证返回结果"
        result.size == 10000
    }

    def "统计时间范围内操作日志数量"() {
        given: "测试参数"
        def startTime = LocalDateTime.now().minusDays(7)
        def endTime = LocalDateTime.now()

        and: "模拟Mapper返回结果"
        operationLogMapper.countByTimeRange(startTime, endTime) >> 25

        when: "调用统计方法"
        def result = operationLogService.countByTimeRange(startTime, endTime)

        then: "验证返回结果"
        result == 25
    }

    def "统计时间范围内操作日志数量 - 参数为null"() {
        given: "测试参数为null"
        LocalDateTime startTime = null
        LocalDateTime endTime = null

        when: "调用统计方法"
        def result = operationLogService.countByTimeRange(startTime, endTime)

        then: "验证返回结果"
        result == 0

        and: "验证Mapper未被调用"
        0 * operationLogMapper.countByTimeRange(_, _)
    }

    def "清理历史操作日志"() {
        given: "测试参数"
        def beforeTime = LocalDateTime.now().minusMonths(3)

        and: "模拟Mapper返回结果"
        operationLogMapper.deleteByCreateTimeBefore(beforeTime) >> 100

        when: "调用清理方法"
        def result = operationLogService.cleanHistoryLogs(beforeTime)

        then: "验证返回结果"
        result == 100
    }

    def "清理历史操作日志 - 参数为null"() {
        given: "测试参数为null"
        LocalDateTime beforeTime = null

        when: "调用清理方法"
        def result = operationLogService.cleanHistoryLogs(beforeTime)

        then: "验证返回结果"
        result == 0

        and: "验证Mapper未被调用"
        0 * operationLogMapper.deleteByCreateTimeBefore(_)
    }

    def "根据业务类型统计操作日志数量"() {
        given: "测试参数"
        def businessType = BusinessType.PARTNER

        and: "模拟Mapper返回结果"
        operationLogMapper.countByBusinessType(businessType.code) >> 50

        when: "调用统计方法"
        def result = operationLogService.countByBusinessType(businessType)

        then: "验证返回结果"
        result == 50
    }

    def "根据业务类型统计操作日志数量 - 参数为null"() {
        given: "测试参数为null"
        BusinessType businessType = null

        when: "调用统计方法"
        def result = operationLogService.countByBusinessType(businessType)

        then: "验证返回结果"
        result == 0

        and: "验证Mapper未被调用"
        0 * operationLogMapper.countByBusinessType(_)
    }

    def "根据操作类型统计操作日志数量"() {
        given: "测试参数"
        def operationType = OperationType.CREATE

        and: "模拟Mapper返回结果"
        operationLogMapper.countByOperationType(operationType.code) >> 30

        when: "调用统计方法"
        def result = operationLogService.countByOperationType(operationType)

        then: "验证返回结果"
        result == 30
    }

    def "根据操作类型统计操作日志数量 - 参数为null"() {
        given: "测试参数为null"
        OperationType operationType = null

        when: "调用统计方法"
        def result = operationLogService.countByOperationType(operationType)

        then: "验证返回结果"
        result == 0

        and: "验证Mapper未被调用"
        0 * operationLogMapper.countByOperationType(_)
    }
}