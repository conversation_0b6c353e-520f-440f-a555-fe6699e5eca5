package com.tem.customer.service.qiyu

import com.iplatform.common.ResponseDto
import com.tem.customer.model.dto.qiyu.QiyuGroupInfoRequest
import com.tem.customer.model.dto.qiyu.QiyuWechatUserInfoRequest
import com.tem.customer.repository.entity.PartnerWechatGroup
import com.tem.customer.repository.entity.WechatUserBinding
import com.tem.customer.service.partner.PartnerWechatGroupService
import com.tem.customer.service.partner.WechatUserBindingService
import com.tem.platform.api.UserService
import com.tem.platform.api.dto.UserDto
import org.spockframework.spring.SpringBean
import spock.lang.Specification
import spock.lang.Subject
import spock.lang.Unroll
/**
 * QiyuCrmService单元测试
 *
 * <AUTHOR>
 * @since 1.0.0
 */
class QiyuCrmServiceSpec extends Specification {

    @Subject
    QiyuCrmService qiyuCrmService

    @SpringBean
    UserService userService = Mock()

    @SpringBean
    WechatUserBindingService wechatUserBindingService = Mock()

    @SpringBean
    PartnerWechatGroupService partnerWechatGroupService = Mock()

    def setup() {
        // 使用构造函数创建实例，然后设置userService字段
        qiyuCrmService = new QiyuCrmServiceImpl(wechatUserBindingService, partnerWechatGroupService)
        
        // 使用反射设置userService字段（因为它是Dubbo引用）
        def userServiceField = QiyuCrmServiceImpl.class.getDeclaredField("userService")
        userServiceField.setAccessible(true)
        userServiceField.set(qiyuCrmService, userService)
    }

    def "获取微信用户信息 - 参数验证失败"() {
        given: "无效的请求参数"
        def request = new QiyuWechatUserInfoRequest()
        request.setUnionid(null)
        request.setFromType(null)

        when: "调用获取微信用户信息方法"
        def result = qiyuCrmService.getWechatUserInfo(request)

        then: "验证返回参数错误响应"
        result.rlt == 1
        result.message == "Missing required parameters: unionid and fromType"
    }

    def "获取微信用户信息 - 用户未绑定"() {
        given: "有效的请求参数"
        def request = new QiyuWechatUserInfoRequest()
        request.setUnionid("test_unionid")
        request.setFromType("wx_cs")
        request.setOpenid("test_openid")
        request.setWxworkUserId("test_wxwork_userid")
        request.setWxworkUserName("test_wxwork_username")

        and: "模拟用户未绑定"
        wechatUserBindingService.getByUnionIdGlobal("test_unionid") >> null

        when: "调用获取微信用户信息方法"
        def result = qiyuCrmService.getWechatUserInfo(request)

        then: "验证返回未绑定用户响应"
        result.rlt == 0
        result.uid == "test_unionid"
        result.data.size() > 0
        result.data.find { it.key == "status" && it.value == "unbound" } != null
    }

    def "获取微信用户信息 - 用户已绑定"() {
        given: "有效的请求参数"
        def request = new QiyuWechatUserInfoRequest()
        request.setUnionid("test_unionid")
        request.setFromType("wx_cs")
        request.setOpenid("test_openid")

        and: "模拟用户绑定关系"
        def binding = new WechatUserBinding()
        binding.setUserId(123L)
        wechatUserBindingService.getByUnionIdGlobal("test_unionid") >> binding

        and: "模拟用户基本信息"
        def userDto = new UserDto()
        userDto.setFullname("测试用户")
        userDto.setEmail("<EMAIL>")
        userDto.setMobile("***********")
        def userResponse = ResponseDto.success(userDto)
        userService.getUserBaseInfo(123L) >> userResponse

        when: "调用获取微信用户信息方法"
        def result = qiyuCrmService.getWechatUserInfo(request)

        then: "验证返回绑定用户响应"
        result.rlt == 0
        result.uid == "test_unionid"
        result.name == "测试用户"
        result.email == "<EMAIL>"
        result.mobile == "***********"
        result.level == 1
        result.data.size() > 0
    }

    def "获取微信用户信息 - 获取用户信息失败"() {
        given: "有效的请求参数"
        def request = new QiyuWechatUserInfoRequest()
        request.setUnionid("test_unionid")
        request.setFromType("wx_cs")

        and: "模拟用户绑定关系"
        def binding = new WechatUserBinding()
        binding.setUserId(123L)
        wechatUserBindingService.getByUnionIdGlobal("test_unionid") >> binding

        and: "模拟获取用户信息失败"
        def userResponse = ResponseDto.error("User not found")
        userService.getUserBaseInfo(123L) >> userResponse

        when: "调用获取微信用户信息方法"
        def result = qiyuCrmService.getWechatUserInfo(request)

        then: "验证返回未绑定用户响应（回退逻辑）"
        result.rlt == 0
        result.uid == "test_unionid"
        result.data.find { it.key == "status" && it.value == "unbound" } != null
    }

    def "获取微信用户信息 - 异常情况"() {
        given: "有效的请求参数"
        def request = new QiyuWechatUserInfoRequest()
        request.setUnionid("test_unionid")
        request.setFromType("wx_cs")

        and: "模拟异常"
        wechatUserBindingService.getByUnionIdGlobal("test_unionid") >> { throw new RuntimeException("Database error") }

        when: "调用获取微信用户信息方法"
        def result = qiyuCrmService.getWechatUserInfo(request)

        then: "验证返回未绑定用户响应（异常被内部捕获，返回null，触发未绑定逻辑）"
        result.rlt == 0
        result.uid == "test_unionid"
        result.data.find { it.key == "status" && it.value == "unbound" } != null
    }

    def "获取群聊信息 - 参数验证失败"() {
        given: "无效的请求参数"
        def request = new QiyuGroupInfoRequest()
        request.setChatId(null)
        request.setFromType(null)

        when: "调用获取群聊信息方法"
        def result = qiyuCrmService.getGroupInfo(request)

        then: "验证返回参数错误响应"
        result.rlt == 1
        result.message == "Missing required parameters: chatId and fromType"
    }

    def "获取群聊信息 - 群聊未绑定"() {
        given: "有效的请求参数"
        def request = new QiyuGroupInfoRequest()
        request.setChatId("test_chat_id")
        request.setFromType("wx_cs")

        and: "模拟群聊未绑定"
        partnerWechatGroupService.getByChatId("test_chat_id") >> null

        when: "调用获取群聊信息方法"
        def result = qiyuCrmService.getGroupInfo(request)

        then: "验证返回成功响应"
        result.rlt == 0
        result.uid == "test_chat_id"
        result.level == 1
        result.data.size() > 0
        result.data.find { it.key == "chatId" && it.value == "test_chat_id" } != null
    }

    def "获取群聊信息 - 群聊已绑定"() {
        given: "有效的请求参数"
        def request = new QiyuGroupInfoRequest()
        request.setChatId("test_chat_id")
        request.setFromType("wx_cs")

        and: "模拟群聊绑定关系"
        def group = new PartnerWechatGroup()
        group.setPartnerId(456L)
        partnerWechatGroupService.getByChatId("test_chat_id") >> group

        when: "调用获取群聊信息方法"
        def result = qiyuCrmService.getGroupInfo(request)

        then: "验证返回成功响应"
        result.rlt == 0
        result.uid == "test_chat_id"
        result.level == 1
        result.data.size() > 0
        result.data.find { it.key == "chatId" && it.value == "test_chat_id" } != null
    }

    def "获取群聊信息 - 异常情况"() {
        given: "有效的请求参数"
        def request = new QiyuGroupInfoRequest()
        request.setChatId("test_chat_id")
        request.setFromType("wx_cs")

        and: "模拟异常"
        partnerWechatGroupService.getByChatId("test_chat_id") >> { throw new RuntimeException("Database error") }

        when: "调用获取群聊信息方法"
        def result = qiyuCrmService.getGroupInfo(request)

        then: "验证返回成功响应（异常被内部捕获，返回null，触发默认群聊逻辑）"
        result.rlt == 0
        result.uid == "test_chat_id"
        result.level == 1
        result.data.find { it.key == "chatId" && it.value == "test_chat_id" } != null
    }

    @Unroll
    def "根据微信标识符查找用户 - #scenario"() {
        given: "请求参数"
        def request = new QiyuWechatUserInfoRequest()
        request.setUnionid(unionid)
        request.setFromType(fromType)

        and: "模拟绑定服务"
        wechatUserBindingService.getByUnionIdGlobal(unionid) >> binding

        when: "调用查找用户方法"
        def result = qiyuCrmService.findUserByWechatIdentifier(request)

        then: "验证结果"
        result == expectedResult

        where:
        scenario               | unionid         | fromType | binding               | expectedResult
        "找到绑定用户"         | "test_unionid"  | "wx_cs"  | createBinding(123L)   | 123L
        "未找到绑定用户"       | "test_unionid"  | "wx_cs"  | null                  | null
        "Unionid为空"          | null            | "wx_cs"  | null                  | null
        "查找异常"             | "test_unionid"  | "wx_cs"  | { throw new RuntimeException("DB error") } | null
    }

    def "根据chatId查找企业ID - 找到绑定关系"() {
        given: "模拟群聊绑定关系"
        def group = new PartnerWechatGroup()
        group.setPartnerId(789L)
        partnerWechatGroupService.getByChatId("test_chat_id") >> group

        when: "调用查找企业ID方法"
        def result = qiyuCrmService.findPartnerByChatId("test_chat_id")

        then: "验证结果"
        result == 789L
    }

    def "根据chatId查找企业ID - 未找到绑定关系"() {
        given: "模拟未找到绑定关系"
        partnerWechatGroupService.getByChatId("test_chat_id") >> null

        when: "调用查找企业ID方法"
        def result = qiyuCrmService.findPartnerByChatId("test_chat_id")

        then: "验证结果"
        result == null
    }

    def "根据chatId查找企业ID - chatId为空"() {
        when: "调用查找企业ID方法，chatId为空"
        def result = qiyuCrmService.findPartnerByChatId(null)

        then: "验证结果"
        result == null
    }

    def "根据chatId查找企业ID - 异常情况"() {
        given: "模拟异常"
        partnerWechatGroupService.getByChatId("test_chat_id") >> { throw new RuntimeException("Database error") }

        when: "调用查找企业ID方法"
        def result = qiyuCrmService.findPartnerByChatId("test_chat_id")

        then: "验证结果"
        result == null
    }

    def "完整微信用户信息获取流程测试"() {
        given: "完整的请求参数"
        def request = new QiyuWechatUserInfoRequest()
        request.setUnionid("test_unionid")
        request.setFromType("wx_cs")
        request.setOpenid("test_openid")
        request.setWxworkUserId("test_wxwork_userid")
        request.setWxworkUserName("测试用户")

        and: "模拟用户绑定关系"
        def binding = new WechatUserBinding()
        binding.setUserId(123L)
        wechatUserBindingService.getByUnionIdGlobal("test_unionid") >> binding

        and: "模拟用户基本信息"
        def userDto = new UserDto()
        userDto.setFullname("测试用户")
        userDto.setEmail("<EMAIL>")
        userDto.setMobile("***********")
        def userResponse = ResponseDto.success(userDto)
        userService.getUserBaseInfo(123L) >> userResponse

        when: "调用获取微信用户信息方法"
        def result = qiyuCrmService.getWechatUserInfo(request)

        then: "验证完整的响应数据"
        result.rlt == 0
        result.uid == "test_unionid"
        result.name == "测试用户"
        result.email == "<EMAIL>"
        result.mobile == "***********"
        result.level == 1

        and: "验证数据项包含必要字段"
        def accountItem = result.data.find { it.key == "account" }
        def unionIdItem = result.data.find { it.key == "UnionID" }
        def openidItem = result.data.find { it.key == "openid" }
        def fromTypeItem = result.data.find { it.key == "fromType" }

        accountItem != null
        accountItem.value == "123"
        unionIdItem != null
        unionIdItem.value == "test_unionid"
        openidItem != null
        openidItem.value == "test_openid"
        fromTypeItem != null
        fromTypeItem.value == "wx_cs"
    }

    // 辅助方法：创建绑定对象
    def createBinding(Long userId) {
        def binding = new WechatUserBinding()
        binding.setUserId(userId)
        return binding
    }
}