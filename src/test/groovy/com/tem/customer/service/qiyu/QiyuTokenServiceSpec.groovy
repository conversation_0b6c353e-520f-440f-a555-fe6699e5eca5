package com.tem.customer.service.qiyu

import org.redisson.api.RBucket
import org.redisson.api.RedissonClient
import spock.lang.Specification
import spock.lang.Subject
import spock.lang.Unroll

import java.time.Duration
import java.time.LocalDateTime
/**
 * QiyuTokenService单元测试
 *
 * <AUTHOR>
 * @since 1.0.0
 */
class QiyuTokenServiceSpec extends Specification {

    @Subject
    QiyuTokenService qiyuTokenService

    RedissonClient redissonClient = Mock()

    def setup() {
        qiyuTokenService = new QiyuTokenServiceImpl(redissonClient)
        // 通过反射设置配置属性
        qiyuTokenService.metaClass.setProperty(qiyuTokenService, "appKey", "test_app_key")
        qiyuTokenService.metaClass.setProperty(qiyuTokenService, "appSecret", "test_app_secret")
        qiyuTokenService.metaClass.setProperty(qiyuTokenService, "tokenTimeoutMinutes", 120)
    }

    def "获取Token成功"() {
        given: "模拟Redis操作"
        def bucket = Mock(RBucket)
        redissonClient.getBucket(_ as String) >> bucket // 匹配任何key

        when: "调用获取Token方法"
        def result = qiyuTokenService.getToken("test_app_key", "test_app_secret")

        then: "验证返回结果"
        result.rlt == 0
        result.token != null
        result.expires != null

        and: "验证Token被缓存到Redis"
        1 * bucket.set(_, Duration.ofMinutes(120))
    }

    def "获取Token - AppId或AppSecret不匹配"() {
        when: "调用获取Token方法，使用错误的凭据"
        def result = qiyuTokenService.getToken("wrong_app_key", "wrong_app_secret")

        then: "验证返回错误结果"
        result.rlt == 1
        result.message == "Invalid appid or appsecret"
    }

    def "获取Token - Redis异常情况"() {
        given: "模拟Redis操作抛出异常"
        redissonClient.getBucket(_ as String) >> { throw new RuntimeException("Redis connection failed") }

        when: "调用获取Token方法"
        def result = qiyuTokenService.getToken("test_app_key", "test_app_secret")

        then: "返回系统错误"
        result.rlt == 500
        result.message == "System error"
    }

    @Unroll
    def "验证Token - #scenario"() {
        given: "模拟Redis操作"
        def bucket = Mock(RBucket)
        redissonClient.getBucket(_ as String) >> bucket
        bucket.get() >> tokenInfo

        when: "调用验证Token方法"
        def result = qiyuTokenService.validateToken(token)

        then: "验证结果"
        result == expectedResult

        where:
        scenario               | token          | tokenInfo                          | expectedResult
        "Token有效"            | "valid_token"  | new QiyuTokenServiceImpl.TokenInfo("valid_token", LocalDateTime.now().plusHours(1), "test_app_key") | true
        "Token不存在"          | "invalid_token"| null                              | false
        "Token已过期"          | "expired_token"| new QiyuTokenServiceImpl.TokenInfo("expired_token", LocalDateTime.now().minusHours(1), "test_app_key") | false
        "Token为空"            | null           | null                              | false
        "Token为空字符串"      | ""             | null                              | false
    }

    def "验证Token - 过期Token被删除"() {
        given: "模拟Redis操作"
        def bucket = Mock(RBucket)
        def expiredTokenInfo = new QiyuTokenServiceImpl.TokenInfo("expired_token", LocalDateTime.now().minusHours(1), "test_app_key")
        redissonClient.getBucket(_ as String) >> bucket
        bucket.get() >> expiredTokenInfo

        when: "调用验证Token方法"
        def result = qiyuTokenService.validateToken("expired_token")

        then: "验证结果和删除操作"
        !result
        1 * bucket.delete()
    }

    def "验证Token - Redis异常情况"() {
        given: "模拟Redis操作抛出异常"
        redissonClient.getBucket(_ as String) >> { throw new RuntimeException("Redis connection failed") }

        when: "调用验证Token方法"
        qiyuTokenService.validateToken("test_token")

        then: "异常情况下抛出异常"
        thrown(RuntimeException)
    }

    def "TokenInfo记录类测试"() {
        given: "创建TokenInfo对象"
        def tokenInfo = new QiyuTokenServiceImpl.TokenInfo("test_token", LocalDateTime.now().plusHours(1), "test_app_key")

        expect: "验证属性"
        tokenInfo.token() == "test_token"
        tokenInfo.appId() == "test_app_key"
        tokenInfo.expireTime().isAfter(LocalDateTime.now())
    }

    def "完整Token生命周期测试"() {
        given: "模拟Redis操作"
        def bucket = Mock(RBucket)
        redissonClient.getBucket(_ as String) >> bucket

        when: "获取Token"
        bucket.get() >> null
        def tokenResponse = qiyuTokenService.getToken("test_app_key", "test_app_secret")

        then: "验证Token生成"
        tokenResponse.rlt == 0
        tokenResponse.token != null
        1 * bucket.set(_, Duration.ofMinutes(120))

        when: "验证Token有效性"
        def validTokenInfo = new QiyuTokenServiceImpl.TokenInfo(tokenResponse.token, LocalDateTime.now().plusHours(1), "test_app_key")
        bucket.get() >> validTokenInfo
        def isValid = qiyuTokenService.validateToken(tokenResponse.token)

        then: "验证Token有效"
        isValid

        when: "Token过期"
        def expiredTokenInfo = new QiyuTokenServiceImpl.TokenInfo(tokenResponse.token, LocalDateTime.now().minusHours(1), "test_app_key")
        bucket.get() >> expiredTokenInfo
        def isExpired = qiyuTokenService.validateToken(tokenResponse.token)

        then: "验证Token过期并被删除"
        !isExpired
        1 * bucket.delete()
    }
}