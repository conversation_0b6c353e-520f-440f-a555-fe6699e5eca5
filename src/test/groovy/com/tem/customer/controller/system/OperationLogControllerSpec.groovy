package com.tem.customer.controller.system

import com.baomidou.mybatisplus.core.metadata.IPage
import com.tem.customer.shared.common.Result
import com.tem.customer.shared.common.ResultCode
import com.tem.customer.model.dto.system.OperationLogQueryDTO
import com.tem.customer.repository.entity.OperationLog
import com.tem.customer.model.vo.system.OperationLogVO
import com.tem.customer.service.system.OperationLogService
import com.tem.customer.shared.enums.BusinessType
import com.tem.customer.shared.enums.OperationType
import org.spockframework.spring.SpringBean
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import spock.lang.Specification

/**
 * 操作记录日志控制器测试类
 * 使用Spock框架进行单元测试
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@SpringBootTest
class OperationLogControllerSpec extends Specification {

    @SpringBean
    private OperationLogService operationLogService = Mock()

    def operationLogController

    def setup() {
        // 手动创建Controller实例
        operationLogController = new OperationLogController(operationLogService)
    }

    def "测试分页查询操作日志成功"() {
        given: "准备测试数据"
        def queryDTO = new OperationLogQueryDTO()
        queryDTO.setPageNum(1)
        queryDTO.setPageSize(10)

        def operationLog1 = new OperationLog()
        operationLog1.setId(1L)
        operationLog1.setBusinessType(BusinessType.PARTNER.getCode())
        operationLog1.setBusinessId(100L)
        operationLog1.setOperationType(OperationType.CREATE.getCode())
        operationLog1.setOperationDesc("创建企业")
        operationLog1.setOperatorId(1L)
        operationLog1.setOperatorName("测试用户")
        operationLog1.setPartnerId(1L)
        operationLog1.setTargetPartnerId(100L)

        def operationLog2 = new OperationLog()
        operationLog2.setId(2L)
        operationLog2.setBusinessType(BusinessType.PARTNER_NOTE.getCode())
        operationLog2.setBusinessId(200L)
        operationLog2.setOperationType(OperationType.UPDATE.getCode())
        operationLog2.setOperationDesc("更新备注")
        operationLog2.setOperatorId(1L)
        operationLog2.setOperatorName("测试用户")
        operationLog2.setPartnerId(1L)
        operationLog2.setTargetPartnerId(100L)

        def logList = [operationLog1, operationLog2]
        
        // 创建VO对象列表
        def vo1 = new OperationLogVO()
        vo1.setId(1L)
        vo1.setBusinessType(BusinessType.PARTNER.getCode())
        vo1.setBusinessTypeDesc(BusinessType.PARTNER.getDescription())
        vo1.setBusinessId(100L)
        vo1.setOperationType(OperationType.CREATE.getCode())
        vo1.setOperationTypeDesc(OperationType.CREATE.getDescription())
        vo1.setOperationDesc("创建企业")
        vo1.setOperatorId(1L)
        vo1.setOperatorName("测试用户")
        vo1.setPartnerId(1L)
        vo1.setTargetPartnerId(100L)
        
        def vo2 = new OperationLogVO()
        vo2.setId(2L)
        vo2.setBusinessType(BusinessType.PARTNER_NOTE.getCode())
        vo2.setBusinessTypeDesc(BusinessType.PARTNER_NOTE.getDescription())
        vo2.setBusinessId(200L)
        vo2.setOperationType(OperationType.UPDATE.getCode())
        vo2.setOperationTypeDesc(OperationType.UPDATE.getDescription())
        vo2.setOperationDesc("更新备注")
        vo2.setOperatorId(1L)
        vo2.setOperatorName("测试用户")
        vo2.setPartnerId(1L)
        vo2.setTargetPartnerId(100L)
        
        def voList = [vo1, vo2]
        
        def page = Mock(IPage)
        page.getRecords() >> logList
        page.getTotal() >> 2L
        page.getSize() >> 10L
        page.getCurrent() >> 1L
        page.getPages() >> 1L
        
        def voPage = Mock(IPage)
        voPage.getRecords() >> voList
        voPage.getTotal() >> 2L
        voPage.getSize() >> 10L
        voPage.getCurrent() >> 1L
        voPage.getPages() >> 1L
        
        page.convert(_) >> voPage

        and: "Mock服务调用"
        operationLogService.pageQuery(queryDTO) >> page

        when: "调用分页查询接口"
        def result = operationLogController.pageQuery(queryDTO)

        then: "验证返回结果"
        result.code == ResultCode.SUCCESS.getCode()
        result.data != null
        result.data.total == 2
        result.data.records.size() == 2
        result.data.records[0].id == 1L
        result.data.records[0].businessType == BusinessType.PARTNER.getCode()
        result.data.records[0].businessTypeDesc == BusinessType.PARTNER.getDescription()
        result.data.records[0].operationType == OperationType.CREATE.getCode()
        result.data.records[0].operationTypeDesc == OperationType.CREATE.getDescription()
        result.data.records[0].operationDesc == "创建企业"
        result.data.records[1].id == 2L
        result.data.records[1].businessType == BusinessType.PARTNER_NOTE.getCode()
        result.data.records[1].businessTypeDesc == BusinessType.PARTNER_NOTE.getDescription()
    }

    def "测试根据业务类型和业务ID查询操作日志成功"() {
        given: "准备测试数据"
        def operationLog1 = new OperationLog()
        operationLog1.setId(1L)
        operationLog1.setBusinessType(BusinessType.PARTNER.getCode())
        operationLog1.setBusinessId(100L)
        operationLog1.setOperationType(OperationType.CREATE.getCode())
        operationLog1.setOperationDesc("创建企业")

        def operationLog2 = new OperationLog()
        operationLog2.setId(2L)
        operationLog2.setBusinessType(BusinessType.PARTNER.getCode())
        operationLog2.setBusinessId(100L)
        operationLog2.setOperationType(OperationType.UPDATE.getCode())
        operationLog2.setOperationDesc("更新企业")

        def logList = [operationLog1, operationLog2]

        and: "Mock服务调用"
        operationLogService.listByBusinessTypeAndId(BusinessType.PARTNER, 100L) >> logList

        when: "调用根据业务类型和业务ID查询接口"
        def result = operationLogController.listByBusinessTypeAndId(BusinessType.PARTNER.getCode(), 100L)

        then: "验证返回结果"
        result.code == ResultCode.SUCCESS.getCode()
        result.data != null
        result.data.size() == 2
        result.data[0].id == 1L
        result.data[0].businessType == BusinessType.PARTNER.getCode()
        result.data[0].businessTypeDesc == BusinessType.PARTNER.getDescription()
        result.data[1].id == 2L
        result.data[1].operationType == OperationType.UPDATE.getCode()
        result.data[1].operationTypeDesc == OperationType.UPDATE.getDescription()
    }

    def "测试根据目标企业ID查询操作日志成功"() {
        given: "准备测试数据"
        def operationLog1 = new OperationLog()
        operationLog1.setId(1L)
        operationLog1.setTargetPartnerId(100L)
        operationLog1.setOperationDesc("操作1")

        def operationLog2 = new OperationLog()
        operationLog2.setId(2L)
        operationLog2.setTargetPartnerId(100L)
        operationLog2.setOperationDesc("操作2")

        def logList = [operationLog1, operationLog2]

        and: "Mock服务调用"
        operationLogService.listByTargetPartnerId(100L) >> logList

        when: "调用根据目标企业ID查询接口"
        def result = operationLogController.listByTargetPartnerId(100L)

        then: "验证返回结果"
        result.code == ResultCode.SUCCESS.getCode()
        result.data != null
        result.data.size() == 2
        result.data[0].id == 1L
        result.data[0].targetPartnerId == 100L
        result.data[1].id == 2L
        result.data[1].targetPartnerId == 100L
    }

    def "测试根据操作人ID查询操作日志成功"() {
        given: "准备测试数据"
        def operationLog1 = new OperationLog()
        operationLog1.setId(1L)
        operationLog1.setOperatorId(1L)
        operationLog1.setOperationDesc("操作1")

        def operationLog2 = new OperationLog()
        operationLog2.setId(2L)
        operationLog2.setOperatorId(1L)
        operationLog2.setOperationDesc("操作2")

        def logList = [operationLog1, operationLog2]

        and: "Mock服务调用"
        operationLogService.listByOperatorId(1L) >> logList

        when: "调用根据操作人ID查询接口"
        def result = operationLogController.listByOperatorId(1L)

        then: "验证返回结果"
        result.code == ResultCode.SUCCESS.getCode()
        result.data != null
        result.data.size() == 2
        result.data[0].id == 1L
        result.data[0].operatorId == 1L
        result.data[1].id == 2L
        result.data[1].operatorId == 1L
    }

    def "测试根据ID查询操作日志详情成功"() {
        given: "准备测试数据"
        def operationLog = new OperationLog()
        operationLog.setId(1L)
        operationLog.setBusinessType(BusinessType.PARTNER.getCode())
        operationLog.setBusinessId(100L)
        operationLog.setOperationType(OperationType.CREATE.getCode())
        operationLog.setOperationDesc("创建企业")
        operationLog.setOperatorId(1L)
        operationLog.setOperatorName("测试用户")
        operationLog.setPartnerId(1L)
        operationLog.setTargetPartnerId(100L)

        and: "Mock服务调用"
        operationLogService.getById(1L) >> operationLog

        when: "调用根据ID查询详情接口"
        def result = operationLogController.getById(1L)

        then: "验证返回结果"
        result.code == ResultCode.SUCCESS.getCode()
        result.data != null
        result.data.id == 1L
        result.data.businessType == BusinessType.PARTNER.getCode()
        result.data.businessTypeDesc == BusinessType.PARTNER.getDescription()
        result.data.businessId == 100L
        result.data.operationType == OperationType.CREATE.getCode()
        result.data.operationTypeDesc == OperationType.CREATE.getDescription()
        result.data.operationDesc == "创建企业"
        result.data.operatorId == 1L
        result.data.operatorName == "测试用户"
        result.data.partnerId == 1L
        result.data.targetPartnerId == 100L
    }

    def "测试根据ID查询操作日志详情失败 - 日志不存在"() {
        given: "Mock服务调用"
        operationLogService.getById(999L) >> null

        when: "调用根据ID查询详情接口"
        def result = operationLogController.getById(999L)

        then: "验证返回结果"
        result.code != ResultCode.SUCCESS.getCode()
        result.message == "操作日志不存在"
    }

    def "测试获取业务类型列表成功"() {
        when: "调用获取业务类型列表接口"
        def result = operationLogController.getBusinessTypes()

        then: "验证返回结果"
        result.code == ResultCode.SUCCESS.getCode()
        result.data != null
        result.data.size() == BusinessType.values().length
        
        and: "验证业务类型数据"
        def partnerType = result.data.find { it.code == BusinessType.PARTNER.getCode() }
        partnerType != null
        partnerType.description == BusinessType.PARTNER.getDescription()
        
        def noteType = result.data.find { it.code == BusinessType.PARTNER_NOTE.getCode() }
        noteType != null
        noteType.description == BusinessType.PARTNER_NOTE.getDescription()
    }

    def "测试获取操作类型列表成功"() {
        when: "调用获取操作类型列表接口"
        def result = operationLogController.getOperationTypes()

        then: "验证返回结果"
        result.code == ResultCode.SUCCESS.getCode()
        result.data != null
        result.data.size() == OperationType.values().length
        
        and: "验证操作类型数据"
        def createType = result.data.find { it.code == OperationType.CREATE.getCode() }
        createType != null
        createType.description == OperationType.CREATE.getDescription()
        
        def updateType = result.data.find { it.code == OperationType.UPDATE.getCode() }
        updateType != null
        updateType.description == OperationType.UPDATE.getDescription()
        
        def deleteType = result.data.find { it.code == OperationType.DELETE.getCode() }
        deleteType != null
        deleteType.description == OperationType.DELETE.getDescription()
    }

    def "测试转换为VO对象 - 正常数据"() {
        given: "准备测试数据"
        def operationLog = new OperationLog()
        operationLog.setId(1L)
        operationLog.setBusinessType(BusinessType.PARTNER.getCode())
        operationLog.setBusinessId(100L)
        operationLog.setOperationType(OperationType.CREATE.getCode())
        operationLog.setOperationDesc("创建企业")
        operationLog.setOperatorId(1L)
        operationLog.setOperatorName("测试用户")
        operationLog.setOperatorUsername("testuser")
        operationLog.setPartnerId(1L)
        operationLog.setTargetPartnerId(100L)
        operationLog.setIpAddress("*************")
        operationLog.setUserAgent("Mozilla/5.0")
        operationLog.setRequestUri("/api/test")
        operationLog.setRequestMethod("POST")
        operationLog.setExecutionTime(150)
        operationLog.setCreateTime(java.time.LocalDateTime.now())
        operationLog.setCreateBy("admin")

        when: "调用私有方法转换为VO"
        def method = OperationLogController.class.getDeclaredMethod("convertToVO", OperationLog.class)
        method.setAccessible(true)
        def result = method.invoke(operationLogController, operationLog)

        then: "验证转换结果"
        result != null
        result.id == 1L
        result.businessType == BusinessType.PARTNER.getCode()
        result.businessTypeDesc == BusinessType.PARTNER.getDescription()
        result.businessId == 100L
        result.operationType == OperationType.CREATE.getCode()
        result.operationTypeDesc == OperationType.CREATE.getDescription()
        result.operationDesc == "创建企业"
        result.operatorId == 1L
        result.operatorName == "测试用户"
        result.operatorUsername == "testuser"
        result.partnerId == 1L
        result.targetPartnerId == 100L
        result.ipAddress == "*************"
        result.userAgent == "Mozilla/5.0"
        result.requestUri == "/api/test"
        result.requestMethod == "POST"
        result.executionTime == 150L
        result.createTime != null
        result.createBy == "admin"
    }

    def "测试BusinessTypeVO内部类"() {
        given: "创建BusinessTypeVO实例"
        def vo = new OperationLogController.BusinessTypeVO("PARTNER", "企业")

        expect: "验证属性值"
        vo.code == "PARTNER"
        vo.description == "企业"
    }

    def "测试OperationTypeVO内部类"() {
        given: "创建OperationTypeVO实例"
        def vo = new OperationLogController.OperationTypeVO("CREATE", "创建")

        expect: "验证属性值"
        vo.code == "CREATE"
        vo.description == "创建"
    }
}