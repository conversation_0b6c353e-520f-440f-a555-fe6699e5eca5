package com.tem.customer.controller.system

import com.tem.customer.shared.common.Result
import com.tem.customer.shared.common.ResultCode
import com.tem.customer.service.system.ImageService
import org.spockframework.spring.SpringBean
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.mock.web.MockMultipartFile
import org.springframework.web.multipart.MultipartFile
import spock.lang.Specification

/**
 * 图片上传控制器测试类
 * 使用Spock框架进行单元测试
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@SpringBootTest
class ImageControllerSpec extends Specification {

    def setup() {
        // 初始化控制器实例
        imageController = new ImageController(imageService)

        // 通过反射设置@Value注入的字段
        def temImgServerHostField = ImageController.class.getDeclaredField("temImgServerHost")
        temImgServerHostField.setAccessible(true)
        temImgServerHostField.set(imageController, "http://test.img.server/")
    }

    @SpringBean
    private ImageService imageService = Mock()

    def imageController

    def "测试单张图片上传成功"() {
        given: "准备测试数据"
        def file = new MockMultipartFile(
            "file",
            "test.jpg",
            "image/jpeg",
            "test image content".bytes
        )

        and: "Mock服务调用"
        imageService.uploadImage(file) >> "test-file-key-123"

        when: "调用单张图片上传接口"
        def result = imageController.uploadImage(file)

        then: "验证返回结果"
        result.code == ResultCode.SUCCESS.getCode()
        result.message == "图片上传成功"
        result.data != null
        result.data.fileUrl == "http://test.img.server/test-file-key-123"
        result.data.fileName == "test.jpg"
        result.data.fileSize == "test image content".bytes.length
        result.data.contentType == "image/jpeg"
    }

    def "测试单张图片上传失败 - 文件为空"() {
        given: "准备空文件"
        def file = new MockMultipartFile(
            "file",
            "",
            "image/jpeg",
            "".bytes
        )

        when: "调用单张图片上传接口"
        def result = imageController.uploadImage(file)

        then: "验证返回结果"
        result.code == ResultCode.BAD_REQUEST.getCode()
        result.message == "上传文件不能为空"
    }

    def "测试单张图片上传失败 - 文件类型不支持"() {
        given: "准备非图片文件"
        def file = new MockMultipartFile(
            "file",
            "test.txt",
            "text/plain",
            "test text content".bytes
        )

        when: "调用单张图片上传接口"
        def result = imageController.uploadImage(file)

        then: "验证返回结果"
        result.code == ResultCode.FILE_TYPE_NOT_SUPPORTED.getCode()
        result.message == "只支持图片文件上传"
    }

    def "测试单张图片上传失败 - 文件过大"() {
        given: "准备大文件"
        def largeContent = new byte[11 * 1024 * 1024] // 11MB
        Arrays.fill(largeContent, (byte) 1)
        
        def file = new MockMultipartFile(
            "file",
            "large.jpg",
            "image/jpeg",
            largeContent
        )

        when: "调用单张图片上传接口"
        def result = imageController.uploadImage(file)

        then: "验证返回结果"
        result.code == ResultCode.FILE_TOO_LARGE.getCode()
        result.message == "文件大小不能超过10MB"
    }

    def "测试单张图片上传失败 - 服务层异常"() {
        given: "准备测试数据"
        def file = new MockMultipartFile(
            "file",
            "test.jpg",
            "image/jpeg",
            "test image content".bytes
        )

        and: "Mock服务调用抛出异常"
        imageService.uploadImage(file) >> { throw new IOException("上传失败") }

        when: "调用单张图片上传接口"
        def result = imageController.uploadImage(file)

        then: "验证返回结果"
        result.code == ResultCode.FILE_UPLOAD_FAILED.getCode()
        result.message.contains("图片上传失败")
    }

    def "测试批量图片上传成功"() {
        given: "准备测试数据"
        def file1 = new MockMultipartFile(
            "files",
            "test1.jpg",
            "image/jpeg",
            "test image content 1".bytes
        )
        
        def file2 = new MockMultipartFile(
            "files",
            "test2.jpg",
            "image/jpeg",
            "test image content 2".bytes
        )
        
        def files = [file1, file2]

        and: "Mock服务调用"
        def uploadResult = [
            successCount: 2,
            failCount: 0,
            totalCount: 2,
            successFiles: [
                [fileKey: "test-file-key-1", fileName: "test1.jpg"],
                [fileKey: "test-file-key-2", fileName: "test2.jpg"]
            ],
            failFiles: []
        ]
        imageService.uploadImages(files) >> uploadResult

        when: "调用批量图片上传接口"
        def result = imageController.uploadImages(files)

        then: "验证返回结果"
        result.code == ResultCode.SUCCESS.getCode()
        result.message == "批量上传成功，共上传2张图片"
        result.data != null
        result.data.successCount == 2
        result.data.failCount == 0
        result.data.totalCount == 2
        result.data.successFiles.size() == 2
        result.data.successFiles[0].fileUrl == "http://test.img.server/test-file-key-1"
        result.data.successFiles[1].fileUrl == "http://test.img.server/test-file-key-2"
    }

    def "测试批量图片上传失败 - 文件列表为空"() {
        given: "准备空文件列表"
        def files = []

        when: "调用批量图片上传接口"
        def result = imageController.uploadImages(files)

        then: "验证返回结果"
        result.code == ResultCode.BAD_REQUEST.getCode()
        result.message == "上传文件列表不能为空"
    }

    def "测试批量图片上传失败 - 文件数量超限"() {
        given: "准备超过限制的文件数量"
        def files = []
        for (i in 1..10) {
            def file = new MockMultipartFile(
                "files",
                "test${i}.jpg",
                "image/jpeg",
                "test image content ${i}".bytes
            )
            files.add(file)
        }

        when: "调用批量图片上传接口"
        def result = imageController.uploadImages(files)

        then: "验证返回结果"
        result.code == ResultCode.BAD_REQUEST.getCode()
        result.message == "一次最多只能上传9张图片"
    }

    def "测试批量图片上传失败 - 文件验证错误"() {
        given: "准备包含无效文件的列表"
        def validFile = new MockMultipartFile(
            "files",
            "valid.jpg",
            "image/jpeg",
            "valid image content".bytes
        )
        
        def invalidFile = new MockMultipartFile(
            "files",
            "invalid.txt",
            "text/plain",
            "invalid text content".bytes
        )
        
        def files = [validFile, invalidFile]

        when: "调用批量图片上传接口"
        def result = imageController.uploadImages(files)

        then: "验证返回结果"
        result.code == ResultCode.BAD_REQUEST.getCode()
        result.message.contains("文件验证失败")
        result.message.contains("不是图片格式")
    }

    def "测试批量图片上传部分成功"() {
        given: "准备测试数据"
        def files = [
            new MockMultipartFile("files", "test1.jpg", "image/jpeg", "content1".bytes),
            new MockMultipartFile("files", "test2.jpg", "image/jpeg", "content2".bytes)
        ]

        and: "Mock服务调用 - 部分成功"
        def uploadResult = [
            successCount: 1,
            failCount: 1,
            totalCount: 2,
            successFiles: [
                [fileKey: "test-file-key-1", fileName: "test1.jpg"]
            ],
            failFiles: [
                [fileName: "test2.jpg", error: "上传失败"]
            ]
        ]
        imageService.uploadImages(files) >> uploadResult

        when: "调用批量图片上传接口"
        def result = imageController.uploadImages(files)

        then: "验证返回结果"
        result.code == ResultCode.SUCCESS.getCode()
        result.message == "批量上传完成，成功1张，失败1张"
        result.data != null
        result.data.successCount == 1
        result.data.failCount == 1
        result.data.totalCount == 2
    }

    def "测试验证单个文件方法 - 有效文件"() {
        given: "准备有效文件"
        def file = new MockMultipartFile(
            "file",
            "test.jpg",
            "image/jpeg",
            "test image content".bytes
        )

        when: "调用私有方法验证文件"
        def method = ImageController.class.getDeclaredMethod("validateSingleFile", MultipartFile.class, int.class)
        method.setAccessible(true)
        def result = method.invoke(imageController, file, 1)

        then: "验证结果"
        result == null
    }

    def "测试验证单个文件方法 - 空文件"() {
        given: "准备空文件"
        def file = new MockMultipartFile(
            "file",
            "",
            "image/jpeg",
            "".bytes
        )

        when: "调用私有方法验证文件"
        def method = ImageController.class.getDeclaredMethod("validateSingleFile", MultipartFile.class, int.class)
        method.setAccessible(true)
        def result = method.invoke(imageController, file, 1)

        then: "验证结果"
        result != null
        result.contains("第1个文件为空")
    }

    def "测试验证单个文件方法 - 无效文件类型"() {
        given: "准备无效文件类型"
        def file = new MockMultipartFile(
            "file",
            "test.txt",
            "text/plain",
            "test text content".bytes
        )

        when: "调用私有方法验证文件"
        def method = ImageController.class.getDeclaredMethod("validateSingleFile", MultipartFile.class, int.class)
        method.setAccessible(true)
        def result = method.invoke(imageController, file, 1)

        then: "验证结果"
        result != null
        result.contains("不是图片格式")
    }

    def "测试验证单个文件方法 - 文件过大"() {
        given: "准备大文件"
        def largeContent = new byte[11 * 1024 * 1024] // 11MB
        Arrays.fill(largeContent, (byte) 1)
        
        def file = new MockMultipartFile(
            "file",
            "large.jpg",
            "image/jpeg",
            largeContent
        )

        when: "调用私有方法验证文件"
        def method = ImageController.class.getDeclaredMethod("validateSingleFile", MultipartFile.class, int.class)
        method.setAccessible(true)
        def result = method.invoke(imageController, file, 1)

        then: "验证结果"
        result != null
        result.contains("大小超过10MB限制")
    }

    def "测试处理上传结果方法"() {
        given: "准备上传结果数据"
        def uploadResult = [
            successCount: 2,
            failCount: 0,
            totalCount: 2,
            successFiles: [
                [fileKey: "test-key-1", fileName: "test1.jpg"],
                [fileKey: "test-key-2", fileName: "test2.jpg"],
                [fileName: "test3.jpg"] // 没有fileKey的文件
            ],
            failFiles: []
        ]

        when: "调用私有方法处理上传结果"
        def method = ImageController.class.getDeclaredMethod("processUploadResult", Map.class)
        method.setAccessible(true)
        method.invoke(imageController, uploadResult)

        then: "验证处理结果"
        uploadResult.successFiles[0].fileUrl == "http://test.img.server/test-key-1"
        uploadResult.successFiles[1].fileUrl == "http://test.img.server/test-key-2"
        uploadResult.successFiles[2].fileUrl == null // 没有fileKey的文件不会添加URL
    }
}