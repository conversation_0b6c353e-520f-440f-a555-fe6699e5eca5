package com.tem.customer.controller.partner

import com.tem.customer.controller.partner.PartnerNoteController
import com.tem.customer.shared.common.Result
import com.tem.customer.shared.common.ResultCode
import com.tem.customer.shared.exception.BusinessException
import com.tem.customer.repository.entity.PartnerNote
import com.tem.customer.model.convert.PartnerNoteConverter
import com.tem.customer.model.dto.partner.PartnerNoteDTO
import com.tem.customer.model.vo.partner.PartnerNoteVO
import com.tem.customer.service.partner.PartnerNoteService
import org.spockframework.spring.SpringBean
import org.springframework.boot.test.context.SpringBootTest
import spock.lang.Specification

/**
 * 企业备注控制器测试类
 * 使用Spock框架进行单元测试
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@SpringBootTest
class PartnerNoteControllerSpec extends Specification {

    def setup() {
        // 初始化控制器实例
        partnerNoteController = new PartnerNoteController(partnerNoteService)
    }

    @SpringBean
    private PartnerNoteService partnerNoteService = Mock()

    def partnerNoteController

    def "测试根据企业ID查询备注列表成功"() {
        given: "准备测试数据"
        def partnerNote1 = new PartnerNote()
        partnerNote1.setId(1L)
        partnerNote1.setPartnerId(100L)
        partnerNote1.setTitle("测试标题1")
        partnerNote1.setContent("测试内容1")

        def partnerNote2 = new PartnerNote()
        partnerNote2.setId(2L)
        partnerNote2.setPartnerId(100L)
        partnerNote2.setTitle("测试标题2")
        partnerNote2.setContent("测试内容2")

        def noteList = [partnerNote1, partnerNote2]

        def vo1 = new PartnerNoteVO()
        vo1.setId(1L)
        vo1.setTitle("测试标题1")
        vo1.setContent("测试内容1")

        def vo2 = new PartnerNoteVO()
        vo2.setId(2L)
        vo2.setTitle("测试标题2")
        vo2.setContent("测试内容2")

        def voList = [vo1, vo2]

        and: "Mock服务调用"
        partnerNoteService.listByPartnerId(100L) >> noteList

        when: "调用根据企业ID查询备注列表接口"
        def result = partnerNoteController.listByPartnerId(100L)

        then: "验证返回结果"
        result.code == ResultCode.SUCCESS.getCode()
        result.data != null
        result.data.size() == 2
        // 由于使用真实的转换器，验证基本字段即可
        result.data[0].id == 1L
        result.data[0].title == "测试标题1"
        result.data[1].id == 2L
        result.data[1].title == "测试标题2"
    }

    def "测试根据备注ID查询备注详情成功"() {
        given: "准备测试数据"
        def partnerNote = new PartnerNote()
        partnerNote.setId(1L)
        partnerNote.setPartnerId(100L)
        partnerNote.setTitle("测试标题")
        partnerNote.setContent("测试内容")

        def vo = new PartnerNoteVO()
        vo.setId(1L)
        vo.setTitle("测试标题")
        vo.setContent("测试内容")

        and: "Mock服务调用"
        partnerNoteService.getById(1L) >> partnerNote

        when: "调用根据备注ID查询备注详情接口"
        def result = partnerNoteController.getById(1L)

        then: "验证返回结果"
        result.code == ResultCode.SUCCESS.getCode()
        result.data != null
        result.data.id == 1L
        result.data.title == "测试标题"
        result.data.content == "测试内容"
    }

    def "测试根据备注ID查询详情失败 - 备注不存在"() {
        given: "Mock服务调用"
        partnerNoteService.getById(999L) >> null

        when: "调用根据备注ID查询备注详情接口"
        def result = partnerNoteController.getById(999L)

        then: "验证异常抛出"
        def exception = thrown(BusinessException)
        exception.message.contains("备注不存在")
    }

    def "测试添加企业备注成功"() {
        given: "准备测试数据"
        def dto = new PartnerNoteDTO()
        dto.setPartnerId(100L)
        dto.setTitle("测试标题")
        dto.setContent("测试内容")

        def entity = new PartnerNote()
        entity.setPartnerId(100L)
        entity.setTitle("测试标题")
        entity.setContent("测试内容")

        def savedEntity = new PartnerNote()
        savedEntity.setId(1L)
        savedEntity.setPartnerId(100L)
        savedEntity.setTitle("测试标题")
        savedEntity.setContent("测试内容")

        def vo = new PartnerNoteVO()
        vo.setId(1L)
        vo.setTitle("测试标题")
        vo.setContent("测试内容")

        and: "Mock服务调用"
        partnerNoteService.isExceedLimit(100L) >> false
        partnerNoteService.addPartnerNote(_ as PartnerNote) >> savedEntity

        when: "调用添加企业备注接口"
        def result = partnerNoteController.add(dto)

        then: "验证返回结果"
        result.code == ResultCode.SUCCESS.getCode()
        result.data != null
        result.data.id == 1L
        result.data.title == "测试标题"
        result.data.content == "测试内容"
        result.message == "添加备注成功"
    }

    def "测试添加企业备注失败 - 数量超限"() {
        given: "准备测试数据"
        def dto = new PartnerNoteDTO()
        dto.setPartnerId(100L)
        dto.setTitle("测试标题")
        dto.setContent("测试内容")

        and: "Mock数量限制检查"
        partnerNoteService.isExceedLimit(100L) >> true

        when: "调用添加企业备注接口"
        def result = partnerNoteController.add(dto)

        then: "验证异常抛出"
        def exception = thrown(BusinessException)
        exception.message.contains("企业备注数量已达到最大限制")
    }

    def "测试添加企业备注失败 - 保存失败"() {
        given: "准备测试数据"
        def dto = new PartnerNoteDTO()
        dto.setPartnerId(100L)
        dto.setTitle("测试标题")
        dto.setContent("测试内容")

        def entity = new PartnerNote()
        entity.setPartnerId(100L)
        entity.setTitle("测试标题")
        entity.setContent("测试内容")

        and: "Mock服务调用"
        partnerNoteService.isExceedLimit(100L) >> false
        partnerNoteService.addPartnerNote(_ as PartnerNote) >> null

        when: "调用添加企业备注接口"
        def result = partnerNoteController.add(dto)

        then: "验证异常抛出"
        def exception = thrown(BusinessException)
        exception.message.contains("添加备注失败")
    }

    def "测试调试JSON解析接口"() {
        given: "准备测试数据"
        def dto = new PartnerNoteDTO()
        dto.setPartnerId(100L)
        dto.setTitle("调试标题")
        dto.setContent("调试内容")

        when: "调用调试接口"
        def result = partnerNoteController.debugJsonParsing(dto)

        then: "验证返回结果"
        result.code == ResultCode.SUCCESS.getCode()
        result.data != null
        result.data.partnerId == 100L
        result.data.title == "调试标题"
        result.data.content == "调试内容"
        result.message == "JSON解析成功"
    }

    def "测试更新企业备注成功"() {
        given: "准备测试数据"
        def dto = new PartnerNoteDTO()
        dto.setPartnerId(100L)
        dto.setTitle("更新标题")
        dto.setContent("更新内容")

        def existingNote = new PartnerNote()
        existingNote.setId(1L)
        existingNote.setPartnerId(100L)
        existingNote.setTitle("原标题")
        existingNote.setContent("原内容")

        def entity = new PartnerNote()
        entity.setId(1L)
        entity.setPartnerId(100L)
        entity.setTitle("更新标题")
        entity.setContent("更新内容")

        def vo = new PartnerNoteVO()
        vo.setId(1L)
        vo.setTitle("更新标题")
        vo.setContent("更新内容")

        and: "Mock服务调用"
        partnerNoteService.getById(1L) >> existingNote
        partnerNoteService.updatePartnerNote(_ as PartnerNote) >> true

        when: "调用更新企业备注接口"
        def result = partnerNoteController.update(1L, dto)

        then: "验证返回结果"
        result.code == ResultCode.SUCCESS.getCode()
        result.data != null
        result.data.id == 1L
        result.data.title == "更新标题"
        result.data.content == "更新内容"
        result.message == "更新备注成功"
    }

    def "测试更新企业备注失败 - 备注不存在"() {
        given: "准备测试数据"
        def dto = new PartnerNoteDTO()
        dto.setPartnerId(100L)
        dto.setTitle("更新标题")
        dto.setContent("更新内容")

        and: "Mock服务调用"
        partnerNoteService.getById(999L) >> null

        when: "调用更新企业备注接口"
        def result = partnerNoteController.update(999L, dto)

        then: "验证异常抛出"
        def exception = thrown(BusinessException)
        exception.message.contains("备注不存在")
    }

    def "测试更新企业备注失败 - 更新失败"() {
        given: "准备测试数据"
        def dto = new PartnerNoteDTO()
        dto.setPartnerId(100L)
        dto.setTitle("更新标题")
        dto.setContent("更新内容")

        def existingNote = new PartnerNote()
        existingNote.setId(1L)
        existingNote.setPartnerId(100L)

        def entity = new PartnerNote()
        entity.setId(1L)
        entity.setPartnerId(100L)

        and: "Mock服务调用"
        partnerNoteService.getById(1L) >> existingNote
        partnerNoteService.updatePartnerNote(_ as PartnerNote) >> false

        when: "调用更新企业备注接口"
        def result = partnerNoteController.update(1L, dto)

        then: "验证异常抛出"
        def exception = thrown(BusinessException)
        exception.message.contains("更新备注失败")
    }

    def "测试删除企业备注成功"() {
        given: "准备测试数据"
        def existingNote = new PartnerNote()
        existingNote.setId(1L)
        existingNote.setPartnerId(100L)

        and: "Mock服务调用"
        partnerNoteService.getById(1L) >> existingNote
        partnerNoteService.deletePartnerNote(1L) >> true

        when: "调用删除企业备注接口"
        def result = partnerNoteController.delete(1L)

        then: "验证返回结果"
        result.code == ResultCode.SUCCESS.getCode()
        result.message == "删除备注成功"
    }

    def "测试删除企业备注失败 - 备注不存在"() {
        given: "Mock服务调用"
        partnerNoteService.getById(999L) >> null

        when: "调用删除企业备注接口"
        def result = partnerNoteController.delete(999L)

        then: "验证异常抛出"
        def exception = thrown(BusinessException)
        exception.message.contains("备注不存在")
    }

    def "测试删除企业备注失败 - 删除失败"() {
        given: "准备测试数据"
        def existingNote = new PartnerNote()
        existingNote.setId(1L)
        existingNote.setPartnerId(100L)

        and: "Mock服务调用"
        partnerNoteService.getById(1L) >> existingNote
        partnerNoteService.deletePartnerNote(1L) >> false

        when: "调用删除企业备注接口"
        def result = partnerNoteController.delete(1L)

        then: "验证异常抛出"
        def exception = thrown(BusinessException)
        exception.message.contains("删除备注失败")
    }

    def "测试调整备注排序位置成功"() {
        given: "准备测试数据"
        def existingNote = new PartnerNote()
        existingNote.setId(1L)
        existingNote.setPartnerId(100L)

        and: "Mock服务调用"
        partnerNoteService.getById(1L) >> existingNote
        partnerNoteService.updateSortPosition(1L, 3) >> true

        when: "调用调整排序位置接口"
        def result = partnerNoteController.updateSortPosition(1L, 3)

        then: "验证返回结果"
        result.code == ResultCode.SUCCESS.getCode()
        result.message == "调整排序位置成功"
    }

    def "测试调整备注排序位置失败 - 备注不存在"() {
        given: "Mock服务调用"
        partnerNoteService.getById(999L) >> null

        when: "调用调整排序位置接口"
        def result = partnerNoteController.updateSortPosition(999L, 3)

        then: "验证异常抛出"
        def exception = thrown(BusinessException)
        exception.message.contains("备注不存在")
    }

    def "测试调整备注排序位置失败 - 位置参数无效"() {
        given: "准备测试数据"
        def existingNote = new PartnerNote()
        existingNote.setId(1L)
        existingNote.setPartnerId(100L)

        and: "Mock服务调用"
        partnerNoteService.getById(1L) >> existingNote

        when: "调用调整排序位置接口 - 无效位置"
        def result = partnerNoteController.updateSortPosition(1L, 0)

        then: "验证异常抛出"
        def exception = thrown(BusinessException)
        exception.message.contains("位置参数必须大于0")
    }

    def "测试调整备注排序位置失败 - 调整失败"() {
        given: "准备测试数据"
        def existingNote = new PartnerNote()
        existingNote.setId(1L)
        existingNote.setPartnerId(100L)

        and: "Mock服务调用"
        partnerNoteService.getById(1L) >> existingNote
        partnerNoteService.updateSortPosition(1L, 3) >> false

        when: "调用调整排序位置接口"
        def result = partnerNoteController.updateSortPosition(1L, 3)

        then: "验证异常抛出"
        def exception = thrown(BusinessException)
        exception.message.contains("调整排序位置失败")
    }

    def "测试统计企业备注数量"() {
        given: "Mock服务调用"
        partnerNoteService.countByPartnerId(100L) >> 5

        when: "调用统计接口"
        def result = partnerNoteController.countByPartnerId(100L)

        then: "验证返回结果"
        result.code == ResultCode.SUCCESS.getCode()
        result.data == 5
    }

    def "测试检查企业备注数量限制 - 未超限"() {
        given: "Mock服务调用"
        partnerNoteService.isExceedLimit(100L) >> false

        when: "调用检查限制接口"
        def result = partnerNoteController.checkLimit(100L)

        then: "验证返回结果"
        result.code == ResultCode.SUCCESS.getCode()
        result.data == true // 可以继续添加
    }

    def "测试检查企业备注数量限制 - 已超限"() {
        given: "Mock服务调用"
        partnerNoteService.isExceedLimit(100L) >> true

        when: "调用检查限制接口"
        def result = partnerNoteController.checkLimit(100L)

        then: "验证返回结果"
        result.code == ResultCode.SUCCESS.getCode()
        result.data == false // 不能继续添加
    }

    def "测试检查企业备注数量限制 - 异常情况"() {
        given: "Mock服务调用抛出异常"
        partnerNoteService.isExceedLimit(100L) >> { throw new RuntimeException("服务异常") }

        when: "调用检查限制接口"
        def result = partnerNoteController.checkLimit(100L)

        then: "验证异常处理"
        result.code != ResultCode.SUCCESS.getCode()
        result.message.contains("检查数量限制失败")
    }
}