package com.tem.customer.controller.qiyu

import com.tem.customer.model.dto.qiyu.QiyuGroupInfoRequest
import com.tem.customer.model.dto.qiyu.QiyuGroupInfoResponse
import com.tem.customer.model.dto.qiyu.QiyuTokenResponse
import com.tem.customer.model.dto.qiyu.QiyuWechatUserInfoRequest
import com.tem.customer.model.dto.qiyu.QiyuWechatUserInfoResponse
import com.tem.customer.service.qiyu.QiyuCrmService
import com.tem.customer.service.qiyu.QiyuTokenService
import org.spockframework.spring.SpringBean
import org.springframework.boot.test.context.SpringBootTest
import spock.lang.Specification

/**
 * 七鱼CRM接口控制器测试类
 * 使用Spock框架进行单元测试
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@SpringBootTest
class QiyuCrmControllerSpec extends Specification {

    def setup() {
        // 初始化控制器实例
        qiyuCrmController = new QiyuCrmController(qiyuTokenService, qiyuCrmService)
    }

    @SpringBean
    private QiyuTokenService qiyuTokenService = Mock()

    @SpringBean
    private QiyuCrmService qiyuCrmService = Mock()

    def qiyuCrmController

    def "测试获取Token成功"() {
        given: "准备测试数据"
        def appid = "test-appid"
        def appsecret = "test-appsecret"
        
        def tokenResponse = new QiyuTokenResponse()
        tokenResponse.setRlt(200)
        tokenResponse.setMessage("success")
        tokenResponse.setToken("test-token-123")

        and: "Mock服务调用"
        qiyuTokenService.getToken(appid, appsecret) >> tokenResponse

        when: "调用获取Token接口"
        def response = qiyuCrmController.getToken(appid, appsecret)

        then: "验证返回结果"
        response != null
        response.statusCode.is2xxSuccessful()
        response.body != null
        response.body.rlt == 200
        response.body.message == "success"
        response.body.token == "test-token-123"
    }

    def "测试获取Token异常处理"() {
        given: "准备测试数据"
        def appid = "test-appid"
        def appsecret = "test-appsecret"

        and: "Mock服务调用抛出异常"
        qiyuTokenService.getToken(appid, appsecret) >> { throw new RuntimeException("服务异常") }

        when: "调用获取Token接口"
        def response = qiyuCrmController.getToken(appid, appsecret)

        then: "验证异常处理"
        response != null
        response.statusCode.is2xxSuccessful()
        response.body != null
        response.body.rlt == 500
        response.body.message == "System error"
    }

    def "测试微信生态获取用户信息成功"() {
        given: "准备测试数据"
        def request = new QiyuWechatUserInfoRequest()
        request.setOpenid("test-openid")
        request.setUnionid("test-unionid")
        
        def wechatUserInfoResponse = new QiyuWechatUserInfoResponse()
        wechatUserInfoResponse.setRlt(200)
        wechatUserInfoResponse.setMessage("success")
        wechatUserInfoResponse.setName("测试用户")
        wechatUserInfoResponse.setEmail("<EMAIL>")

        and: "Mock服务调用"
        qiyuCrmService.getWechatUserInfo(request) >> wechatUserInfoResponse

        when: "调用微信生态获取用户信息接口"
        def response = qiyuCrmController.getWechatUserInfo(request)

        then: "验证返回结果"
        response != null
        response.statusCode.is2xxSuccessful()
        response.body != null
        response.body.rlt == 200
        response.body.message == "success"
        response.body.name == "测试用户"
        response.body.email == "<EMAIL>"
    }

    def "测试微信生态获取用户信息异常处理"() {
        given: "准备测试数据"
        def request = new QiyuWechatUserInfoRequest()
        request.setOpenid("test-openid")

        and: "Mock服务调用抛出异常"
        qiyuCrmService.getWechatUserInfo(request) >> { throw new RuntimeException("服务异常") }

        when: "调用微信生态获取用户信息接口"
        def response = qiyuCrmController.getWechatUserInfo(request)

        then: "验证异常处理"
        response != null
        response.statusCode.is2xxSuccessful()
        response.body != null
        response.body.rlt == 500
        response.body.message == "System error"
    }

    def "测试企微客服群聊获取群信息成功"() {
        given: "准备测试数据"
        def request = new QiyuGroupInfoRequest()
        request.setChatId("test-chat-id")
        request.setFromType("wx_cs")

        def groupInfoResponse = new QiyuGroupInfoResponse()
        groupInfoResponse.setRlt(200)
        groupInfoResponse.setMessage("success")
        groupInfoResponse.setUid("test-chat-id")
        groupInfoResponse.setLevel(1)

        and: "Mock服务调用"
        qiyuCrmService.getGroupInfo(request) >> groupInfoResponse

        when: "调用企微群聊获取群信息接口"
        def response = qiyuCrmController.getGroupInfo(request)

        then: "验证返回结果"
        response != null
        response.statusCode.is2xxSuccessful()
        response.body != null
        response.body.rlt == 200
        response.body.message == "success"
        response.body.uid == "test-chat-id"
        response.body.level == 1
    }

    def "测试企微客服群聊获取群信息异常处理"() {
        given: "准备测试数据"
        def request = new QiyuGroupInfoRequest()
        request.setChatId("test-chat-id")

        and: "Mock服务调用抛出异常"
        qiyuCrmService.getGroupInfo(request) >> { throw new RuntimeException("服务异常") }

        when: "调用企微群聊获取群信息接口"
        def response = qiyuCrmController.getGroupInfo(request)

        then: "验证异常处理"
        response != null
        response.statusCode.is2xxSuccessful()
        response.body != null
        response.body.rlt == 500
        response.body.message == "System error"
    }

    def "测试处理OPTIONS预检请求 - 微信生态接口"() {
        when: "调用OPTIONS接口"
        def response = qiyuCrmController.handleOptions()

        then: "验证返回结果"
        response != null
        response.statusCode.is2xxSuccessful()
        response.body == null
    }

    def "测试处理OPTIONS预检请求 - 群聊接口"() {
        when: "调用OPTIONS接口"
        def response = qiyuCrmController.handleOptions()

        then: "验证返回结果"
        response != null
        response.statusCode.is2xxSuccessful()
        response.body == null
    }

    def "测试QiyuTokenResponse系统错误方法"() {
        when: "调用系统错误方法"
        def response = QiyuTokenResponse.systemError("System error")

        then: "验证返回结果"
        response != null
        response.rlt == 500
        response.message == "System error"
    }

    def "测试QiyuWechatUserInfoResponse默认值"() {
        given: "创建响应对象"
        def response = new QiyuWechatUserInfoResponse()

        expect: "验证默认值"
        response.rlt == null
        response.message == null
        response.name == null
        response.email == null
    }

    def "测试QiyuGroupInfoResponse默认值"() {
        given: "创建响应对象"
        def response = new QiyuGroupInfoResponse()

        expect: "验证默认值"
        response.rlt == null
        response.message == null
        response.uid == null
        response.level == null
    }

    def "测试QiyuWechatUserInfoRequest设置和获取"() {
        given: "创建请求对象"
        def request = new QiyuWechatUserInfoRequest()

        when: "设置属性值"
        request.setOpenid("test-openid")
        request.setUnionid("test-unionid")
        request.setAppid("test-appid")

        then: "验证属性值"
        request.openid == "test-openid"
        request.unionid == "test-unionid"
        request.appid == "test-appid"
    }

    def "测试QiyuGroupInfoRequest设置和获取"() {
        given: "创建请求对象"
        def request = new QiyuGroupInfoRequest()

        when: "设置属性值"
        request.setChatId("test-chat-id")
        request.setFromType("wx_cs")
        request.setAppid("test-appid")

        then: "验证属性值"
        request.chatId == "test-chat-id"
        request.fromType == "wx_cs"
        request.appid == "test-appid"
    }

    def "测试获取Token接口参数验证"() {
        when: "调用获取Token接口 - 空参数"
        def response = qiyuCrmController.getToken("", "")

        then: "验证服务调用"
        1 * qiyuTokenService.getToken("", "") >> { throw new RuntimeException("参数错误") }
        
        and: "验证异常处理"
        response != null
        response.statusCode.is2xxSuccessful()
        response.body != null
        response.body.rlt == 500
        response.body.message == "System error"
    }

    def "测试微信生态接口参数验证"() {
        given: "准备空请求对象"
        def request = new QiyuWechatUserInfoRequest()

        and: "Mock服务调用"
        qiyuCrmService.getWechatUserInfo(request) >> { throw new RuntimeException("参数错误") }

        when: "调用微信生态接口"
        def response = qiyuCrmController.getWechatUserInfo(request)

        then: "验证异常处理"
        response != null
        response.statusCode.is2xxSuccessful()
        response.body != null
        response.body.rlt == 500
        response.body.message == "System error"
    }

    def "测试群聊接口参数验证"() {
        given: "准备空请求对象"
        def request = new QiyuGroupInfoRequest()

        and: "Mock服务调用"
        qiyuCrmService.getGroupInfo(request) >> { throw new RuntimeException("参数错误") }

        when: "调用群聊接口"
        def response = qiyuCrmController.getGroupInfo(request)

        then: "验证异常处理"
        response != null
        response.statusCode.is2xxSuccessful()
        response.body != null
        response.body.rlt == 500
        response.body.message == "System error"
    }
}