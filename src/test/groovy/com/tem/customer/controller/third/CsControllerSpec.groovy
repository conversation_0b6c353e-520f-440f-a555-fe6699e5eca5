package com.tem.customer.controller.third

import com.iplatform.common.ResponseDto
import com.iplatform.common.web.ResponseVO
import com.tem.customer.BaseControllerSpec
import com.tem.customer.model.from.UserAndStandardForm
import com.tem.customer.service.third.CsService
import com.tem.customer.shared.common.Result
import com.tem.customer.shared.common.ResultCode
import com.tem.customer.shared.utils.UserContextUtil
import com.tem.errand.butler.api.HotelStandardService
import com.tem.errand.butler.model.dto.standard.HotelStandardItemDto
import com.tem.errand.quark.api.PartnerRankService
import com.tem.platform.api.DistrictService
import com.tem.platform.api.UserOpenIdService
import com.tem.platform.api.UserService
import com.tem.platform.api.condition.DistrictQueryCondition
import com.tem.platform.api.dto.DistrictDto
import com.tem.platform.api.dto.UserDto
import org.mockito.MockedStatic
import org.mockito.Mockito
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.bean.override.mockito.MockitoBean
import spock.lang.Specification
import spock.lang.Subject

/**
 * 云客服数据请求接口控制器测试类
 * 使用Spock框架进行单元测试
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@SpringBootTest
class CsControllerSpec extends BaseControllerSpec {

    @Subject
    CsController csController

    @MockitoBean
    UserService userService = Mock()

    @MockitoBean
    DistrictService districtService = Mock()

    @MockitoBean
    CsService csService = Mock()

    @MockitoBean
    UserOpenIdService userOpenIdService = Mock()

    @MockitoBean
    PartnerRankService partnerRankService = Mock()

    @MockitoBean
    HotelStandardService hotelStandardService = Mock()

    def setup() {
        csController = new CsController()

        // 通过反射设置服务
        setService(csController, "userService", userService)
        setService(csController, "csService", csService)
        setDubboService(csController, "districtService", districtService)
        setDubboService(csController, "userOpenIdService", userOpenIdService)
        setDubboService(csController, "partnerRankService", partnerRankService)
        setDubboService(csController, "hotelStandardService", hotelStandardService)
    }

    /**
     * 通过反射设置服务
     */
    private void setService(Object target, String fieldName, Object service) {
        def field = target.class.getDeclaredField(fieldName)
        field.setAccessible(true)
        field.set(target, service)
    }

    /**
     * 通过反射设置Dubbo服务
     */
    private void setDubboService(Object target, String fieldName, Object service) {
        def field = target.class.getDeclaredField(fieldName)
        field.setAccessible(true)
        field.set(target, service)
    }

    def "测试获取用户和差标基本信息 - 用户ID为空且无openId"() {
        given: "Mock静态方法调用"
        Mockito.when(UserContextUtil.getStringValue("openId")).thenReturn(null)
        Mockito.when(UserContextUtil.getLongValue("userId")).thenReturn(null)

        when: "调用获取用户和差标基本信息接口"
        def result = csController.getUserAndStandardInfo()

        then: "验证返回结果"
        result.code == ResultCode.BAD_REQUEST.getCode()
    }

    def "测试获取用户和差标基本信息 - 用户ID为空但有openId"() {
        given: "Mock静态方法调用"
        Mockito.when(UserContextUtil.getStringValue("openId")).thenReturn("test-openid")
        Mockito.when(UserContextUtil.getLongValue("userId")).thenReturn(null)

        and: "Mock服务调用返回null"
        userOpenIdService.getUserByOpenId("test-openid") >> null

        when: "调用获取用户和差标基本信息接口"
        def result = csController.getUserAndStandardInfo()

        then: "验证返回结果"
        result.code == ResultCode.CS_USER_BIND_NO.getCode()
    }

    def "测试获取用户和差标基本信息 - 正常获取成功"() {
        given: "准备测试数据"
        def userId = 1001L
        def openId = "test-openid"
        def userAndStandardForm = new UserAndStandardForm()
        userAndStandardForm.setUserId(userId)
        userAndStandardForm.setUserName("张三")
        userAndStandardForm.setMobile("13800138000")

        and: "Mock静态方法调用"
        Mockito.when(UserContextUtil.getStringValue("openId")).thenReturn(openId)
        Mockito.when(UserContextUtil.getLongValue("userId")).thenReturn(userId)

        and: "Mock服务调用"
        csService.getUserAndStandardInfo(userId) >> userAndStandardForm

        when: "调用获取用户和差标基本信息接口"
        def result = csController.getUserAndStandardInfo()

        then: "验证返回结果"
        result.success
        result.data != null
        result.data.userId == userId
    }

    def "测试查询国内行程区划信息 - 关键字为空"() {
        when: "调用查询接口，关键字为空"
        def result = csController.queryDistrictByKeyword("")

        then: "验证返回结果"
        result.code == ResultCode.PARAM_NULL_ERROR.getCode()
    }

    def "测试查询国内行程区划信息 - 关键字为null"() {
        when: "调用查询接口，关键字为null"
        def result = csController.queryDistrictByKeyword(null)

        then: "验证返回结果"
        result.code == ResultCode.PARAM_NULL_ERROR.getCode()
    }

    def "测试查询国内行程区划信息 - 关键字为空白字符"() {
        when: "调用查询接口，关键字为空白字符"
        def result = csController.queryDistrictByKeyword("   ")

        then: "验证返回结果"
        result.code == ResultCode.PARAM_NULL_ERROR.getCode()
    }

    def "测试查询国内行程区划信息 - 正常查询成功"() {
        given: "准备测试数据"
        def keyword = "上海"
        
        // 准备区划数据
        def district1 = new DistrictDto()
        district1.setId(10081)
        district1.setNameCn("上海市")
        district1.setpId(10080)

        def district2 = new DistrictDto()
        district2.setId(10082)
        district2.setNameCn("浦东新区")
        district2.setpId(10081)
        
        def districtList = [district1, district2]
        
        // 准备父级区划数据
        def parentDistrict = new DistrictDto()
        parentDistrict.setId(10080)
        parentDistrict.setNameCn("上海直辖市")
        
        def parentMap = [(10080): parentDistrict, (10081): district1]

        and: "Mock服务调用"
        districtService.findDistrictDtoLimitByParam(_ as DistrictQueryCondition) >> ResponseDto.success(districtList)
        districtService.findMapByIds([10080, 10081]) >> ResponseDto.success(parentMap)

        when: "调用查询接口"
        def result = csController.queryDistrictByKeyword(keyword)

        then: "验证返回结果"
        result.success
        result.data != null
        result.data.size() == 2
        result.data[0].id == 10081
        result.data[0].nameCn == "上海市"
        result.data[0].parentName == "上海直辖市"
        result.data[1].id == 10082
        result.data[1].nameCn == "浦东新区"
        result.data[1].parentName == "上海市"
    }

    def "测试查询国内行程区划信息 - 查询结果为空"() {
        given: "准备测试数据"
        def keyword = "不存在的城市"

        and: "Mock服务调用返回空列表"
        districtService.findDistrictDtoLimitByParam(_ as DistrictQueryCondition) >> ResponseDto.success([])

        when: "调用查询接口"
        def result = csController.queryDistrictByKeyword(keyword)

        then: "验证返回结果"
        result.success
        result.data != null
        result.data.isEmpty()
    }

    def "测试获取酒店差标信息 - 城市ID为空"() {
        given: "Mock静态方法调用"
        Mockito.when(UserContextUtil.getLongValue("userId")).thenReturn(null)
        Mockito.when(UserContextUtil.getStringValue("openId")).thenReturn(null)

        when: "调用获取酒店差标信息接口，城市ID为空"
        def result = csController.getHotelStandardInfo(null)

        then: "验证返回结果"
        result.code == ResultCode.PARAM_NULL_ERROR.getCode()
    }

    def "测试获取酒店差标信息 - 用户ID为空"() {
        given: "准备测试数据"
        def cityId = 10081L

        and: "Mock静态方法调用"
        Mockito.when(UserContextUtil.getLongValue("userId")).thenReturn(null)
        Mockito.when(UserContextUtil.getStringValue("openId")).thenReturn(null)

        when: "调用获取酒店差标信息接口"
        def result = csController.getHotelStandardInfo(cityId)

        then: "验证返回结果"
        result.code == ResultCode.PARAM_NULL_ERROR.getCode()
    }

    def "测试获取酒店差标信息 - 正常获取成功"() {
        given: "准备测试数据"
        def cityId = 10081L
        def userId = 1001L

        def hotelStandardItem = new HotelStandardItemDto()
        hotelStandardItem.setMonth("全年")
        hotelStandardItem.setMoney(300.0)
        hotelStandardItem.setAgreementMoney(250.0)

        def hotelStandardList = [hotelStandardItem]

        and: "Mock静态方法调用"
        Mockito.when(UserContextUtil.getLongValue("userId")).thenReturn(userId)
        Mockito.when(UserContextUtil.getStringValue("openId")).thenReturn("test-openid")

        and: "Mock服务调用"
        def userDto = new UserDto()
        userDto.setId(userId)
        userDto.setPartnerId(1001L)
        userDto.setFullname("测试用户")

        userService.getUserBaseInfo(userId) >> ResponseDto.success(userDto)
        partnerRankService.getEmpRank(1001L, userId) >> ResponseDto.success("LEVEL_1")
        districtService.getParentCityid(cityId, 3) >> ResponseDto.success(10080L)
        hotelStandardService.getHotelStandardInfo(1001L, "LEVEL_1", 10080L, userId) >> ResponseDto.success(hotelStandardList)

        when: "调用获取酒店差标信息接口"
        def result = csController.getHotelStandardInfo(cityId)

        then: "验证返回结果"
        result.success
        result.data != null
        result.data.size() == 1
        result.data[0].month == "全年"
        result.data[0].money == 300.0
        result.data[0].agreementMoney == 250.0
    }

    def "测试获取订单信息 - 用户ID为空"() {
        given: "Mock静态方法调用"
        Mockito.when(UserContextUtil.getLongValue("userId")).thenReturn(null)
        Mockito.when(UserContextUtil.getStringValue("openId")).thenReturn(null)

        when: "调用获取订单信息接口"
        def result = csController.getOrderInfo()

        then: "验证返回结果"
        result.code == ResultCode.PARAM_NULL_ERROR.getCode()
    }

    def "测试获取订单信息 - 正常获取成功"() {
        given: "准备测试数据"
        def userId = 1001L
        def orderInfoResponse = ResponseDto.success("订单信息数据")

        and: "Mock静态方法调用"
        Mockito.when(UserContextUtil.getLongValue("userId")).thenReturn(userId)
        Mockito.when(UserContextUtil.getStringValue("openId")).thenReturn("test-openid")

        and: "Mock服务调用"
        csService.getOrderInfo(userId) >> orderInfoResponse

        when: "调用获取订单信息接口"
        def result = csController.getOrderInfo()

        then: "验证返回结果"
        result.code == ResultCode.SUCCESS.getCode()
        result.data != null
        result.data.success
        result.data.data == "订单信息数据"
    }

    def "测试获取订单信息 - 服务调用失败"() {
        given: "准备测试数据"
        def userId = 1001L
        def orderInfoResponse = ResponseDto.error("获取订单信息失败")

        and: "Mock静态方法调用"
        Mockito.when(UserContextUtil.getLongValue("userId")).thenReturn(userId)
        Mockito.when(UserContextUtil.getStringValue("openId")).thenReturn("test-openid")

        and: "Mock服务调用"
        csService.getOrderInfo(userId) >> orderInfoResponse

        when: "调用获取订单信息接口"
        def result = csController.getOrderInfo()

        then: "验证返回结果"
        result.code == ResultCode.SUCCESS.getCode()
        result.data != null
        !result.data.success
        result.data.msg == "获取订单信息失败"
    }
}
