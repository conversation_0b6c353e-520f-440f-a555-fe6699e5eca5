package com.tem.customer.shared.utils

import org.springframework.mock.web.MockHttpServletRequest
import org.springframework.mock.web.MockHttpServletResponse
import org.springframework.web.context.request.RequestContextHolder
import org.springframework.web.context.request.ServletRequestAttributes
import spock.lang.Specification

import jakarta.servlet.http.HttpSession

/**
 * UserContextUtil 单元测试
 * 测试用户上下文工具类的基础请求上下文功能
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
class UserContextUtilSpec extends Specification {

    MockHttpServletRequest mockRequest
    MockHttpServletResponse mockResponse
    HttpSession mockSession

    def setup() {
        mockRequest = new MockHttpServletRequest()
        mockResponse = new MockHttpServletResponse()
        mockSession = mockRequest.getSession(true)

        // 设置Spring的RequestContextHolder
        def requestAttributes = new ServletRequestAttributes(mockRequest, mockResponse)
        RequestContextHolder.setRequestAttributes(requestAttributes)
    }

    def cleanup() {
        // 清理RequestContextHolder
        RequestContextHolder.resetRequestAttributes()
    }

    def "测试获取当前HTTP请求"() {
        when: "获取当前HTTP请求"
        def request = UserContextUtil.getCurrentRequest()

        then: "应该返回正确的请求对象"
        request != null
        request == mockRequest
    }

    def "测试获取当前HTTP响应"() {
        when: "获取当前HTTP响应"
        def response = UserContextUtil.getCurrentResponse()

        then: "应该返回正确的响应对象"
        response != null
        response == mockResponse
    }

    def "测试获取当前Session"() {
        when: "获取当前Session"
        def session = UserContextUtil.getCurrentSession()

        then: "应该返回正确的Session对象"
        session != null
        session == mockSession
    }

    def "测试非Web环境下获取请求对象"() {
        given: "清除RequestContextHolder"
        RequestContextHolder.resetRequestAttributes()

        when: "获取当前HTTP请求"
        def request = UserContextUtil.getCurrentRequest()

        then: "应该返回null"
        request == null
    }

    def "测试获取请求参数"() {
        given: "设置请求参数"
        mockRequest.setParameter("userId", "12345")
        mockRequest.setParameter("userName", "张三")
        mockRequest.setParameter("page", "1")

        when: "获取请求参数"
        def userId = UserContextUtil.getParameter("userId")
        def userName = UserContextUtil.getParameter("userName")
        def page = UserContextUtil.getParameter("page")
        def nonExistent = UserContextUtil.getParameter("nonExistent")

        then: "应该返回正确的参数值"
        userId == "12345"
        userName == "张三"
        page == "1"
        nonExistent == null
    }

    def "测试Session属性操作"() {
        given: "准备Session属性"
        def key = "userInfo"
        def value = [id: 1, name: "李四", role: "admin"]

        when: "设置和获取Session属性"
        UserContextUtil.setSessionAttribute(key, value)
        def retrievedValue = UserContextUtil.getSessionAttribute(key)

        then: "应该正确设置和获取Session属性"
        retrievedValue == value
    }

    def "测试Session属性移除"() {
        given: "设置Session属性"
        def key = "tempData"
        def value = "临时数据"
        UserContextUtil.setSessionAttribute(key, value)

        when: "移除Session属性"
        UserContextUtil.removeSessionAttribute(key)
        def retrievedValue = UserContextUtil.getSessionAttribute(key)

        then: "属性应该被移除"
        retrievedValue == null
    }



    def "测试非Web环境下的参数获取"() {
        given: "清除RequestContextHolder"
        RequestContextHolder.resetRequestAttributes()

        when: "尝试获取参数"
        def param = UserContextUtil.getParameter("test")
        def sessionAttr = UserContextUtil.getSessionAttribute("test")

        then: "应该返回null或默认值"
        param == null
        sessionAttr == null
    }

    def "测试Session无效时的属性操作"() {
        given: "清除RequestContextHolder"
        RequestContextHolder.resetRequestAttributes()

        when: "尝试操作Session属性"
        UserContextUtil.setSessionAttribute("test", "value")
        def value = UserContextUtil.getSessionAttribute("test")

        then: "应该安全处理，不抛出异常"
        value == null
    }



    def "测试请求属性操作"() {
        given: "准备请求属性"
        def key = "requestData"
        def value = [timestamp: System.currentTimeMillis(), source: "test"]

        when: "设置和获取请求属性"
        UserContextUtil.setRequestAttribute(key, value)
        def retrievedValue = UserContextUtil.getRequestAttribute(key)

        then: "应该正确设置和获取请求属性"
        retrievedValue == value
    }

    def "测试请求属性移除"() {
        given: "设置请求属性"
        def key = "tempRequestData"
        def value = "临时请求数据"
        UserContextUtil.setRequestAttribute(key, value)

        when: "移除请求属性"
        UserContextUtil.removeRequestAttribute(key)
        def retrievedValue = UserContextUtil.getRequestAttribute(key)

        then: "属性应该被移除"
        retrievedValue == null
    }

    def "测试获取字符串参数值"() {
        given: "设置请求参数"
        mockRequest.setParameter("testParam", "testValue")

        when: "获取字符串参数值"
        def value = UserContextUtil.getStringValue("testParam")
        def nullValue = UserContextUtil.getStringValue("nonExistent")

        then: "应该返回正确的值"
        value == "testValue"
        nullValue == null
    }

    def "测试获取Long参数值"() {
        given: "设置请求参数"
        mockRequest.setParameter("longParam", "12345")
        mockRequest.setParameter("invalidLong", "notANumber")
        mockRequest.setParameter("undefinedParam", "undefined")

        when: "获取Long参数值"
        def validLong = UserContextUtil.getLongValue("longParam")
        def invalidLong = UserContextUtil.getLongValue("invalidLong")
        def undefinedLong = UserContextUtil.getLongValue("undefinedParam")
        def nullLong = UserContextUtil.getLongValue("nonExistent")

        then: "应该正确处理各种情况"
        validLong == 12345L
        invalidLong == null
        undefinedLong == null
        nullLong == null
    }

    def "测试获取Integer参数值"() {
        given: "设置请求参数"
        mockRequest.setParameter("intParam", "123")
        mockRequest.setParameter("invalidInt", "notANumber")

        when: "获取Integer参数值"
        def validInt = UserContextUtil.getIntegerValue("intParam")
        def invalidInt = UserContextUtil.getIntegerValue("invalidInt")
        def nullInt = UserContextUtil.getIntegerValue("nonExistent")

        then: "应该正确处理各种情况"
        validInt == 123
        invalidInt == null
        nullInt == null
    }

    def "测试获取partnerId"() {
        given: "设置partnerId参数"
        mockRequest.setParameter("partnerId", "999")

        when: "获取partnerId"
        def partnerId = UserContextUtil.getPartnerId()

        then: "应该返回正确的partnerId"
        partnerId == 999L
    }

    def "测试非Web环境下的请求属性操作"() {
        given: "清除RequestContextHolder"
        RequestContextHolder.resetRequestAttributes()

        when: "尝试操作请求属性"
        UserContextUtil.setRequestAttribute("test", "value")
        def value = UserContextUtil.getRequestAttribute("test")

        then: "应该安全处理，不抛出异常"
        value == null
    }

    def "测试响应对象设置和清理"() {
        given: "创建响应对象"
        def customResponse = new MockHttpServletResponse()

        when: "设置和获取响应对象"
        UserContextUtil.setResponse(customResponse)
        def retrievedResponse = UserContextUtil.getResponse()

        then: "应该返回设置的响应对象"
        retrievedResponse == customResponse

        when: "清理响应对象"
        UserContextUtil.clearResponse()
        def clearedResponse = UserContextUtil.getResponse()

        then: "应该返回ServletRequestAttributes中的响应对象"
        clearedResponse == mockResponse
    }

    def "测试基础请求对象获取方法"() {
        when: "使用基础方法获取请求对象"
        def request = UserContextUtil.getRequest()

        then: "应该返回正确的请求对象"
        request == mockRequest
    }

    def "测试基础会话对象获取方法"() {
        when: "使用基础方法获取会话对象"
        def session = UserContextUtil.getSession()

        then: "应该返回正确的会话对象"
        session == mockSession
    }

    def "测试非Web环境下的基础方法"() {
        given: "清除RequestContextHolder"
        RequestContextHolder.resetRequestAttributes()

        when: "调用基础方法"
        def request = UserContextUtil.getRequest()
        def response = UserContextUtil.getResponse()
        def session = UserContextUtil.getSession()

        then: "应该安全返回null"
        request == null
        response == null
        session == null
    }

    def "测试参数获取的边界情况"() {
        given: "设置空字符串参数"
        mockRequest.setParameter("emptyParam", "")
        mockRequest.setParameter("spaceParam", "   ")

        when: "获取参数"
        def emptyParam = UserContextUtil.getParameter("emptyParam")
        def spaceParam = UserContextUtil.getParameter("spaceParam")
        def longFromEmpty = UserContextUtil.getLongValue("emptyParam")
        def intFromSpace = UserContextUtil.getIntegerValue("spaceParam")

        then: "应该正确处理边界情况"
        emptyParam == ""
        spaceParam == "   "
        longFromEmpty == null
        intFromSpace == null
    }

    def "测试Session属性的边界情况"() {
        when: "设置null值到Session"
        UserContextUtil.setSessionAttribute("nullKey", null)
        def nullValue = UserContextUtil.getSessionAttribute("nullKey")

        then: "应该能正确处理null值"
        nullValue == null

        when: "移除不存在的Session属性"
        UserContextUtil.removeSessionAttribute("nonExistentKey")

        then: "应该不抛出异常"
        noExceptionThrown()
    }

    def "测试请求属性的边界情况"() {
        when: "设置null值到请求属性"
        UserContextUtil.setRequestAttribute("nullKey", null)
        def nullValue = UserContextUtil.getRequestAttribute("nullKey")

        then: "应该能正确处理null值"
        nullValue == null

        when: "移除不存在的请求属性"
        UserContextUtil.removeRequestAttribute("nonExistentKey")

        then: "应该不抛出异常"
        noExceptionThrown()
    }

}
