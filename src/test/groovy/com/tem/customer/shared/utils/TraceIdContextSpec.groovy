package com.tem.customer.shared.utils

import org.slf4j.MDC
import spock.lang.Specification
import spock.lang.Timeout

import java.util.concurrent.Callable
import java.util.concurrent.CountDownLatch
import java.util.concurrent.TimeUnit

/**
 * TraceIdContext 单元测试
 * 测试TraceId上下文工具类的各种功能
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
class TraceIdContextSpec extends Specification {

    def cleanup() {
        // 每个测试后清理MDC
        MDC.clear()
    }

    def "测试获取当前TraceId"() {
        given: "设置TraceId到MDC"
        def expectedTraceId = "test-trace-id-123"
        MDC.put("traceId", expectedTraceId)

        when: "获取当前TraceId"
        def actualTraceId = TraceIdContext.getCurrentTraceId()

        then: "应该返回正确的TraceId"
        actualTraceId == expectedTraceId
    }

    def "测试获取空TraceId"() {
        given: "清空MDC"
        MDC.clear()

        when: "获取当前TraceId"
        def traceId = TraceIdContext.getCurrentTraceId()

        then: "应该返回null"
        traceId == null
    }

    def "测试设置TraceId"() {
        given: "准备TraceId"
        def traceId = "new-trace-id-456"

        when: "设置TraceId"
        TraceIdContext.setTraceId(traceId)

        then: "应该能从MDC中获取到"
        MDC.get("traceId") == traceId
    }

    def "测试设置空白TraceId"() {
        when: "设置空白TraceId"
        TraceIdContext.setTraceId(input)

        then: "MDC中不应该有TraceId"
        MDC.get("traceId") == null

        where:
        input << [null, "", "   ", "\t", "\n"]
    }

    def "测试设置带空格的TraceId"() {
        given: "带空格的TraceId"
        def traceIdWithSpaces = "  trace-id-with-spaces  "

        when: "设置TraceId"
        TraceIdContext.setTraceId(traceIdWithSpaces)

        then: "应该自动去除空格"
        MDC.get("traceId") == "trace-id-with-spaces"
    }

    def "测试生成并设置新TraceId"() {
        when: "生成并设置新TraceId"
        def generatedTraceId = TraceIdContext.generateAndSetTraceId()

        then: "应该生成有效的TraceId并设置到MDC"
        generatedTraceId != null
        generatedTraceId.length() == 22
        MDC.get("traceId") == generatedTraceId
        ShortUUID.isValidTraceId(generatedTraceId)
    }

    def "测试生成并设置带时间戳的TraceId"() {
        when: "生成并设置带时间戳的TraceId"
        def timestampTraceId = TraceIdContext.generateAndSetTimestampTraceId()

        then: "应该生成有效的带时间戳TraceId并设置到MDC"
        timestampTraceId != null
        timestampTraceId.length() == 22
        MDC.get("traceId") == timestampTraceId
        ShortUUID.isValidTraceId(timestampTraceId)
    }

    def "测试生成并设置不含特殊字符的TraceId"() {
        when: "生成并设置不含特殊字符的TraceId"
        def cleanTraceId = TraceIdContext.generateAndSetCleanTraceId()

        then: "应该生成不含特殊字符的TraceId"
        cleanTraceId != null
        cleanTraceId.length() == 22
        !cleanTraceId.contains('-')
        !cleanTraceId.contains('_')
        MDC.get("traceId") == cleanTraceId
    }

    def "测试生成并设置不含特殊字符的带时间戳TraceId"() {
        when: "生成并设置不含特殊字符的带时间戳TraceId"
        def cleanTimestampTraceId = TraceIdContext.generateAndSetCleanTimestampTraceId()

        then: "应该生成不含特殊字符的带时间戳TraceId"
        cleanTimestampTraceId != null
        cleanTimestampTraceId.length() == 22
        !cleanTimestampTraceId.contains('-')
        !cleanTimestampTraceId.contains('_')
        MDC.get("traceId") == cleanTimestampTraceId
    }

    def "测试清理TraceId"() {
        given: "设置TraceId"
        def traceId = "trace-to-be-cleared"
        TraceIdContext.setTraceId(traceId)

        when: "清理TraceId"
        TraceIdContext.clearTraceId()

        then: "MDC中应该没有TraceId"
        MDC.get("traceId") == null
    }

    def "测试清理空TraceId"() {
        given: "确保MDC中没有TraceId"
        MDC.clear()

        when: "清理TraceId"
        TraceIdContext.clearTraceId()

        then: "应该安全执行，不抛出异常"
        MDC.get("traceId") == null
    }

    def "测试复制MDC"() {
        given: "设置多个MDC属性"
        MDC.put("traceId", "trace-123")
        MDC.put("userId", "user-456")
        MDC.put("requestId", "req-789")

        when: "复制MDC"
        def mdcCopy = TraceIdContext.copyMDC()

        then: "应该包含所有MDC属性"
        mdcCopy != null
        mdcCopy["traceId"] == "trace-123"
        mdcCopy["userId"] == "user-456"
        mdcCopy["requestId"] == "req-789"
    }

    def "测试复制空MDC"() {
        given: "清空MDC"
        MDC.clear()

        when: "复制MDC"
        def mdcCopy = TraceIdContext.copyMDC()

        then: "应该返回null或空Map"
        mdcCopy == null || mdcCopy.isEmpty()
    }

    def "测试恢复MDC"() {
        given: "准备原始MDC和备份"
        def originalMDC = ["traceId": "original-trace", "userId": "original-user"]
        MDC.put("traceId", "current-trace")
        MDC.put("sessionId", "current-session")

        when: "恢复MDC"
        TraceIdContext.restoreMDC(originalMDC)

        then: "应该恢复到原始状态"
        MDC.get("traceId") == "original-trace"
        MDC.get("userId") == "original-user"
        MDC.get("sessionId") == null // 应该被清除
    }

    def "测试恢复null MDC"() {
        given: "设置当前MDC"
        MDC.put("traceId", "current-trace")

        when: "恢复null MDC"
        TraceIdContext.restoreMDC(null)

        then: "应该清空MDC"
        MDC.getCopyOfContextMap() == null || MDC.getCopyOfContextMap().isEmpty()
    }

    @Timeout(10)
    def "测试包装Runnable任务"() {
        given: "设置当前TraceId"
        def originalTraceId = "wrapper-test-trace"
        TraceIdContext.setTraceId(originalTraceId)
        
        def capturedTraceId = null
        def latch = new CountDownLatch(1)
        
        def originalTask = new Runnable() {
            @Override
            void run() {
                capturedTraceId = MDC.get("traceId")
                latch.countDown()
            }
        }

        when: "包装并执行任务"
        def wrappedTask = TraceIdContext.wrapWithTraceId(originalTask)
        // 清除当前线程的TraceId，模拟异步执行
        MDC.clear()
        wrappedTask.run()
        latch.await(5, TimeUnit.SECONDS)

        then: "异步任务中应该能获取到原始TraceId"
        capturedTraceId == originalTraceId
    }

    @Timeout(10)
    def "测试包装Callable任务"() {
        given: "设置当前TraceId"
        def originalTraceId = "callable-test-trace"
        TraceIdContext.setTraceId(originalTraceId)
        
        def originalTask = new Callable<String>() {
            @Override
            String call() throws Exception {
                return MDC.get("traceId")
            }
        }

        when: "包装并执行任务"
        def wrappedTask = TraceIdContext.wrapWithTraceId(originalTask)
        // 清除当前线程的TraceId，模拟异步执行
        MDC.clear()
        def result = wrappedTask.call()

        then: "应该返回原始TraceId"
        result == originalTraceId
    }

    def "测试包装null任务"() {
        when: "包装null任务"
        def wrappedRunnable = TraceIdContext.wrapWithTraceId((Runnable) null)
        def wrappedCallable = TraceIdContext.wrapWithTraceId((Callable) null)

        then: "应该返回null"
        wrappedRunnable == null
        wrappedCallable == null
    }

    def "测试使用指定TraceId包装任务"() {
        given: "准备指定的TraceId"
        def specifiedTraceId = "specified-trace-id"
        
        def capturedTraceId = null
        def originalTask = new Runnable() {
            @Override
            void run() {
                capturedTraceId = MDC.get("traceId")
            }
        }

        when: "使用指定TraceId包装任务"
        def wrappedTask = TraceIdContext.wrapWithTraceId(originalTask, specifiedTraceId)
        wrappedTask.run()

        then: "应该使用指定的TraceId"
        capturedTraceId == specifiedTraceId
    }

    def "测试使用空TraceId包装任务"() {
        given: "准备任务"
        def capturedTraceId = null
        def originalTask = new Runnable() {
            @Override
            void run() {
                capturedTraceId = MDC.get("traceId")
            }
        }

        when: "使用空TraceId包装任务"
        def wrappedTask = TraceIdContext.wrapWithTraceId(originalTask, null)
        wrappedTask.run()

        then: "应该生成新的TraceId"
        capturedTraceId != null
        capturedTraceId.length() == 22
        !capturedTraceId.contains('-')
        !capturedTraceId.contains('_')
    }

    def "测试在指定TraceId上下文中执行任务"() {
        given: "准备TraceId和任务"
        def specifiedTraceId = "context-execution-trace"
        def capturedTraceId = null
        
        def task = new Runnable() {
            @Override
            void run() {
                capturedTraceId = MDC.get("traceId")
            }
        }

        when: "在指定TraceId上下文中执行任务"
        TraceIdContext.runWithTraceId(specifiedTraceId, task)

        then: "任务中应该能获取到指定的TraceId"
        capturedTraceId == specifiedTraceId
    }

    def "测试上下文执行后的MDC恢复"() {
        given: "设置原始MDC"
        def originalTraceId = "original-context-trace"
        MDC.put("traceId", originalTraceId)
        MDC.put("userId", "original-user")
        
        def task = new Runnable() {
            @Override
            void run() {
                // 任务中修改MDC
                MDC.put("tempKey", "tempValue")
            }
        }

        when: "在新TraceId上下文中执行任务"
        TraceIdContext.runWithTraceId("new-trace-id", task)

        then: "执行后应该恢复原始MDC"
        MDC.get("traceId") == originalTraceId
        MDC.get("userId") == "original-user"
        MDC.get("tempKey") == null
    }

    def "测试多次生成TraceId的唯一性"() {
        when: "多次生成TraceId"
        def traceIds = (1..100).collect {
            TraceIdContext.generateAndSetTraceId()
        }

        then: "所有TraceId都应该是唯一的"
        traceIds.toSet().size() == 100
        traceIds.each { traceId ->
            assert traceId.length() == 22
            assert ShortUUID.isValidTraceId(traceId)
        }
    }
}
