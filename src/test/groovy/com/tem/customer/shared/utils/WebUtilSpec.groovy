package com.tem.customer.shared.utils

import org.springframework.mock.web.MockHttpServletRequest
import org.springframework.mock.web.MockHttpServletResponse
import org.springframework.web.context.request.RequestContextHolder
import org.springframework.web.context.request.ServletRequestAttributes
import spock.lang.Specification
import spock.lang.Unroll

/**
 * WebUtil 单元测试
 * 测试Web工具类的HTTP请求相关功能
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
class WebUtilSpec extends Specification {

    MockHttpServletRequest mockRequest
    MockHttpServletResponse mockResponse

    def setup() {
        mockRequest = new MockHttpServletRequest()
        mockResponse = new MockHttpServletResponse()
        
        // 设置Spring的RequestContextHolder
        def requestAttributes = new ServletRequestAttributes(mockRequest, mockResponse)
        RequestContextHolder.setRequestAttributes(requestAttributes)
    }

    def cleanup() {
        // 清理RequestContextHolder
        RequestContextHolder.resetRequestAttributes()
    }

    def "测试获取当前HTTP请求"() {
        when: "获取当前HTTP请求"
        def request = WebUtil.getCurrentRequest()

        then: "应该返回正确的请求对象"
        request != null
        request == mockRequest
    }

    def "测试非Web环境下获取请求"() {
        given: "清除RequestContextHolder"
        RequestContextHolder.resetRequestAttributes()

        when: "获取当前HTTP请求"
        def request = WebUtil.getCurrentRequest()

        then: "应该返回null"
        request == null
    }

    def "测试获取当前HTTP请求Optional包装"() {
        when: "获取Optional包装的HTTP请求"
        def requestOptional = WebUtil.getCurrentRequestOptional()

        then: "应该返回包含请求对象的Optional"
        requestOptional.isPresent()
        requestOptional.get() == mockRequest
    }

    def "测试非Web环境下获取Optional请求"() {
        given: "清除RequestContextHolder"
        RequestContextHolder.resetRequestAttributes()

        when: "获取Optional包装的HTTP请求"
        def requestOptional = WebUtil.getCurrentRequestOptional()

        then: "应该返回空的Optional"
        !requestOptional.isPresent()
    }

    @Unroll
    def "测试获取客户端IP地址 - 请求头: #header, IP: #ip, 期望: #expected"() {
        given: "设置请求头和远程地址"
        if (header && ip) {
            mockRequest.addHeader(header, ip)
        }
        mockRequest.setRemoteAddr("127.0.0.1")

        when: "获取客户端IP地址"
        def result = WebUtil.getClientIpAddress()

        then: "应该返回正确的IP地址"
        result == expected

        where:
        header                | ip                    | expected
        "X-Forwarded-For"     | "*************"       | "*************"
        "X-Real-IP"           | "*********"           | "*********"
        "Proxy-Client-IP"     | "**********0"         | "**********0"
        "WL-Proxy-Client-IP"  | "***********"         | "***********"
        "HTTP_CLIENT_IP"      | "************"        | "************"
        "HTTP_X_FORWARDED_FOR"| "***********"         | "***********"
        null                  | null                  | "127.0.0.1"
    }

    def "测试X-Forwarded-For多IP处理"() {
        given: "设置包含多个IP的X-Forwarded-For头"
        mockRequest.addHeader("X-Forwarded-For", "*************, ********, **********")

        when: "获取客户端IP地址"
        def result = WebUtil.getClientIpAddress()

        then: "应该返回第一个IP"
        result == "*************"
    }

    def "测试IPv6 localhost处理"() {
        given: "设置IPv6 localhost"
        mockRequest.setRemoteAddr("0:0:0:0:0:0:0:1")

        when: "获取客户端IP地址"
        def result = WebUtil.getClientIpAddress()

        then: "应该转换为IPv4 localhost"
        result == "127.0.0.1"
    }

    @Unroll
    def "测试无效IP处理 - 请求头值: #headerValue"() {
        given: "设置无效的IP请求头"
        mockRequest.addHeader("X-Forwarded-For", headerValue)
        mockRequest.setRemoteAddr("***********")

        when: "获取客户端IP地址"
        def result = WebUtil.getClientIpAddress()

        then: "应该使用getRemoteAddr的值"
        result == "***********"

        where:
        headerValue << ["unknown", "null", "", "   "]
    }

    def "测试直接传入请求对象获取IP"() {
        given: "准备请求对象"
        def request = new MockHttpServletRequest()
        request.addHeader("X-Forwarded-For", "***********00")

        when: "直接传入请求对象获取IP"
        def result = WebUtil.getClientIpAddress(request)

        then: "应该返回正确的IP"
        result == "***********00"
    }

    def "测试传入null请求对象"() {
        when: "传入null请求对象"
        def result = WebUtil.getClientIpAddress(null)

        then: "应该返回unknown"
        result == "unknown"
    }

    def "测试非Web环境下获取IP"() {
        given: "清除RequestContextHolder"
        RequestContextHolder.resetRequestAttributes()

        when: "获取客户端IP地址"
        def result = WebUtil.getClientIpAddress()

        then: "应该返回unknown"
        result == "unknown"
    }

    def "测试获取用户代理"() {
        given: "设置User-Agent请求头"
        def userAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        mockRequest.addHeader("User-Agent", userAgent)

        when: "获取用户代理"
        def result = WebUtil.getUserAgent()

        then: "应该返回正确的User-Agent"
        result == userAgent
    }

    def "测试获取空用户代理"() {
        when: "不设置User-Agent请求头"
        def result = WebUtil.getUserAgent()

        then: "应该返回unknown"
        result == "unknown"
    }

    def "测试非Web环境下获取用户代理"() {
        given: "清除RequestContextHolder"
        RequestContextHolder.resetRequestAttributes()

        when: "获取用户代理"
        def result = WebUtil.getUserAgent()

        then: "应该返回unknown"
        result == "unknown"
    }

    def "测试获取请求URI"() {
        given: "设置请求URI"
        mockRequest.setRequestURI("/api/users/profile")

        when: "获取请求URI"
        def result = WebUtil.getRequestUri()

        then: "应该返回正确的URI"
        result == "/api/users/profile"
    }

    def "测试获取空请求URI"() {
        when: "不设置请求URI"
        def result = WebUtil.getRequestUri()

        then: "应该返回unknown"
        result == "unknown"
    }

    def "测试非Web环境下获取请求URI"() {
        given: "清除RequestContextHolder"
        RequestContextHolder.resetRequestAttributes()

        when: "获取请求URI"
        def result = WebUtil.getRequestUri()

        then: "应该返回unknown"
        result == "unknown"
    }

    def "测试获取请求方法"() {
        given: "设置请求方法"
        mockRequest.setMethod("POST")

        when: "获取请求方法"
        def result = WebUtil.getRequestMethod()

        then: "应该返回正确的请求方法"
        result == "POST"
    }

    def "测试获取空请求方法"() {
        when: "不设置请求方法"
        def result = WebUtil.getRequestMethod()

        then: "应该返回unknown"
        result == "unknown"
    }

    def "测试非Web环境下获取请求方法"() {
        given: "清除RequestContextHolder"
        RequestContextHolder.resetRequestAttributes()

        when: "获取请求方法"
        def result = WebUtil.getRequestMethod()

        then: "应该返回unknown"
        result == "unknown"
    }

    def "测试获取完整请求URL"() {
        given: "设置请求URL相关信息"
        mockRequest.setScheme("https")
        mockRequest.setServerName("api.example.com")
        mockRequest.setServerPort(443)
        mockRequest.setRequestURI("/api/users")
        mockRequest.setQueryString("page=1&size=10")

        when: "获取完整请求URL"
        def result = WebUtil.getRequestUrl()

        then: "应该返回完整的URL"
        result.contains("https://api.example.com")
        result.contains("/api/users")
        result.contains("page=1&size=10")
    }

    def "测试获取不带查询参数的URL"() {
        given: "设置请求URL但不设置查询参数"
        mockRequest.setScheme("http")
        mockRequest.setServerName("localhost")
        mockRequest.setServerPort(8080)
        mockRequest.setRequestURI("/api/test")

        when: "获取完整请求URL"
        def result = WebUtil.getRequestUrl()

        then: "应该返回不带查询参数的URL"
        result.contains("http://localhost:8080/api/test")
        !result.contains("?")
    }

    def "测试非Web环境下获取请求URL"() {
        given: "清除RequestContextHolder"
        RequestContextHolder.resetRequestAttributes()

        when: "获取完整请求URL"
        def result = WebUtil.getRequestUrl()

        then: "应该返回unknown"
        result == "unknown"
    }

    def "测试获取查询字符串"() {
        given: "设置查询字符串"
        mockRequest.setQueryString("name=张三&age=25&active=true")

        when: "获取查询字符串"
        def result = WebUtil.getQueryString()

        then: "应该返回正确的查询字符串"
        result == "name=张三&age=25&active=true"
    }

    def "测试获取空查询字符串"() {
        when: "不设置查询字符串"
        def result = WebUtil.getQueryString()

        then: "应该返回空字符串"
        result == ""
    }

    def "测试非Web环境下获取查询字符串"() {
        given: "清除RequestContextHolder"
        RequestContextHolder.resetRequestAttributes()

        when: "获取查询字符串"
        def result = WebUtil.getQueryString()

        then: "应该返回空字符串"
        result == ""
    }

    def "测试复杂场景的IP获取"() {
        given: "设置复杂的代理环境"
        mockRequest.addHeader("X-Forwarded-For", "*************, unknown, ********")
        mockRequest.addHeader("X-Real-IP", "**********")
        mockRequest.setRemoteAddr("127.0.0.1")

        when: "获取客户端IP地址"
        def result = WebUtil.getClientIpAddress()

        then: "应该返回第一个有效的IP"
        result == "*************"
    }

    def "测试所有代理头都无效的情况"() {
        given: "设置所有代理头都为无效值"
        mockRequest.addHeader("X-Forwarded-For", "unknown")
        mockRequest.addHeader("X-Real-IP", "null")
        mockRequest.addHeader("Proxy-Client-IP", "")
        mockRequest.setRemoteAddr("***********")

        when: "获取客户端IP地址"
        def result = WebUtil.getClientIpAddress()

        then: "应该使用getRemoteAddr的值"
        result == "***********"
    }
}
