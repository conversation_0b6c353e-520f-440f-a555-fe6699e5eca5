package com.tem.customer.shared.utils

import org.apache.commons.codec.digest.DigestUtils
import spock.lang.Specification
import spock.lang.Unroll

/**
 * QiyuCrmSignatureUtil 单元测试
 * 测试七鱼CRM签名验证工具类的各种功能
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
class QiyuCrmSignatureUtilSpec extends Specification {

    def "测试私有构造函数"() {
        when: "尝试实例化工具类"
        new QiyuCrmSignatureUtil()

        then: "应该可以正常实例化（没有抛出异常）"
        noExceptionThrown()
    }

    def "测试新版认证签名验证 - 成功场景"() {
        given: "准备有效的签名参数"
        def appSecret = "test-app-secret"
        def requestBody = '{"userId":123,"action":"login"}'
        def time = 1691136000L // 2023-08-04 12:00:00
        
        // 计算期望的签名
        // 使用与实际实现一致的DigestUtils方法
        def requestBodyMd5 = DigestUtils.md5Hex(requestBody)
        def signString = appSecret + requestBodyMd5 + time
        def expectedChecksum = DigestUtils.sha1Hex(signString).toLowerCase()

        when: "验证签名"
        def result = QiyuCrmSignatureUtil.validateNewAuthSignature(appSecret, requestBody, time, expectedChecksum)

        then: "应该验证成功"
        result == true
    }

    def "测试新版认证签名验证 - 失败场景"() {
        given: "准备无效的签名参数"
        def appSecret = "test-app-secret"
        def requestBody = '{"userId":123,"action":"login"}'
        def time = 1691136000L
        def wrongChecksum = "wrong-checksum-value"

        when: "验证签名"
        def result = QiyuCrmSignatureUtil.validateNewAuthSignature(appSecret, requestBody, time, wrongChecksum)

        then: "应该验证失败"
        result == false
    }

    @Unroll
    def "测试新版认证签名验证 - 参数不完整: appSecret=#appSecret, time=#time, checksum=#checksum"() {
        when: "验证不完整的参数"
        def result = QiyuCrmSignatureUtil.validateNewAuthSignature(appSecret, "test body", time, checksum)

        then: "应该验证失败"
        result == false

        where:
        appSecret | time        | checksum
        null      | 1691136000L | "valid-checksum"
        ""        | 1691136000L | "valid-checksum"
        "secret"  | null        | "valid-checksum"
        "secret"  | 1691136000L | null
        "secret"  | 1691136000L | ""
    }

    def "测试新版认证签名验证 - 空请求体"() {
        given: "准备空请求体的签名参数"
        def appSecret = "test-app-secret"
        def requestBody = null
        def time = 1691136000L

        // 计算期望的签名（空请求体应该被当作空字符串处理）
        // 使用与实际实现一致的DigestUtils方法
        def requestBodyMd5 = DigestUtils.md5Hex("")
        def signString = appSecret + requestBodyMd5 + time
        def expectedChecksum = DigestUtils.sha1Hex(signString).toLowerCase()

        when: "验证签名"
        def result = QiyuCrmSignatureUtil.validateNewAuthSignature(appSecret, requestBody, time, expectedChecksum)

        then: "应该验证成功"
        result == true
    }

    def "测试时间戳验证 - 有效时间"() {
        given: "准备当前时间戳"
        def currentTime = (System.currentTimeMillis() / 1000) as Long
        def timeoutMinutes = 5

        when: "验证时间戳"
        def result = QiyuCrmSignatureUtil.validateTimestamp(currentTime, timeoutMinutes)

        then: "应该验证成功"
        result == true
    }

    def "测试时间戳验证 - 过期时间"() {
        given: "准备过期的时间戳"
        def expiredTime = ((System.currentTimeMillis() / 1000) - 600) as Long // 10分钟前
        def timeoutMinutes = 5

        when: "验证时间戳"
        def result = QiyuCrmSignatureUtil.validateTimestamp(expiredTime, timeoutMinutes)

        then: "应该验证失败"
        result == false
    }

    def "测试时间戳验证 - 未来时间"() {
        given: "准备未来的时间戳"
        def futureTime = ((System.currentTimeMillis() / 1000) + 600) as Long // 10分钟后
        def timeoutMinutes = 5

        when: "验证时间戳"
        def result = QiyuCrmSignatureUtil.validateTimestamp(futureTime, timeoutMinutes)

        then: "应该验证失败"
        result == false
    }

    def "测试时间戳验证 - null时间"() {
        when: "验证null时间戳"
        def result = QiyuCrmSignatureUtil.validateTimestamp(null, 5)

        then: "应该验证失败"
        result == false
    }

    def "测试生成访问令牌"() {
        given: "准备应用ID和时间戳"
        def appId = "test-app-id"
        def timestamp = System.currentTimeMillis()

        when: "生成访问令牌"
        def token1 = QiyuCrmSignatureUtil.generateToken(appId, timestamp)
        def token2 = QiyuCrmSignatureUtil.generateToken(appId, timestamp + 1)

        then: "应该生成有效的令牌"
        token1 != null
        token2 != null
        token1.length() == 40 // SHA1哈希长度
        token2.length() == 40
        token1 != token2 // 不同时间戳应该生成不同令牌
    }

    def "测试应用凭证验证 - 成功场景"() {
        given: "准备匹配的应用凭证"
        def appId = "test-app-id"
        def appSecret = "test-app-secret"
        def configAppId = "test-app-id"
        def configAppSecret = "test-app-secret"

        when: "验证应用凭证"
        def result = QiyuCrmSignatureUtil.validateAppCredentials(appId, appSecret, configAppId, configAppSecret)

        then: "应该验证成功"
        result == true
    }

    def "测试应用凭证验证 - 失败场景"() {
        given: "准备不匹配的应用凭证"
        def appId = "test-app-id"
        def appSecret = "test-app-secret"
        def configAppId = "different-app-id"
        def configAppSecret = "different-app-secret"

        when: "验证应用凭证"
        def result = QiyuCrmSignatureUtil.validateAppCredentials(appId, appSecret, configAppId, configAppSecret)

        then: "应该验证失败"
        result == false
    }

    @Unroll
    def "测试应用凭证验证 - 参数不完整: appId=#appId, appSecret=#appSecret, configAppId=#configAppId, configAppSecret=#configAppSecret"() {
        when: "验证不完整的参数"
        def result = QiyuCrmSignatureUtil.validateAppCredentials(appId, appSecret, configAppId, configAppSecret)

        then: "应该验证失败"
        result == false

        where:
        appId     | appSecret | configAppId | configAppSecret
        null      | "secret"  | "config"    | "config-secret"
        ""        | "secret"  | "config"    | "config-secret"
        "app"     | null      | "config"    | "config-secret"
        "app"     | ""        | "config"    | "config-secret"
        "app"     | "secret"  | null        | "config-secret"
        "app"     | "secret"  | ""          | "config-secret"
        "app"     | "secret"  | "config"    | null
        "app"     | "secret"  | "config"    | ""
    }

    def "测试新版签名验证 - 成功场景"() {
        given: "准备有效的签名参数"
        def requestBody = '{"data":"test"}'
        def time = 1691136000L
        def appSecret = "test-secret"
        
        // 计算期望的签名
        def md5 = QiyuCrmSignatureUtil.calculateMd5(requestBody)
        def expectedChecksum = QiyuCrmSignatureUtil.calculateSha1(appSecret + md5 + time)

        when: "验证新版签名"
        def result = QiyuCrmSignatureUtil.validateNewSignature(requestBody, time, expectedChecksum, appSecret)

        then: "应该验证成功"
        result == true
    }

    def "测试新版签名验证 - 失败场景"() {
        given: "准备无效的签名参数"
        def requestBody = '{"data":"test"}'
        def time = 1691136000L
        def appSecret = "test-secret"
        def wrongChecksum = "wrong-checksum"

        when: "验证新版签名"
        def result = QiyuCrmSignatureUtil.validateNewSignature(requestBody, time, wrongChecksum, appSecret)

        then: "应该验证失败"
        result == false
    }

    @Unroll
    def "测试MD5计算 - 输入: #input, 期望长度: 32"() {
        when: "计算MD5"
        def result = QiyuCrmSignatureUtil.calculateMd5(input)

        then: "应该返回正确长度的MD5值"
        if (input == null || input.trim().isEmpty()) {
            result == null
        } else {
            result != null
            result.length() == 32
            result.matches(/^[a-f0-9]+$/) // 只包含小写十六进制字符
        }

        where:
        input << ["hello world", "测试中文", "123456", "", null, "   "]
    }

    @Unroll
    def "测试SHA1计算 - 输入: #input, 期望长度: 40"() {
        when: "计算SHA1"
        def result = QiyuCrmSignatureUtil.calculateSha1(input)

        then: "应该返回正确长度的SHA1值"
        if (input == null || input.trim().isEmpty()) {
            result == null
        } else {
            result != null
            result.length() == 40
            result.matches(/^[a-f0-9]+$/) // 只包含小写十六进制字符
        }

        where:
        input << ["hello world", "测试中文", "123456", "", null, "   "]
    }

    def "测试MD5计算的一致性"() {
        given: "相同的输入"
        def input = "consistent test string"

        when: "多次计算MD5"
        def result1 = QiyuCrmSignatureUtil.calculateMd5(input)
        def result2 = QiyuCrmSignatureUtil.calculateMd5(input)

        then: "应该返回相同的结果"
        result1 == result2
        result1 != null
        result1.length() == 32
    }

    def "测试SHA1计算的一致性"() {
        given: "相同的输入"
        def input = "consistent test string"

        when: "多次计算SHA1"
        def result1 = QiyuCrmSignatureUtil.calculateSha1(input)
        def result2 = QiyuCrmSignatureUtil.calculateSha1(input)

        then: "应该返回相同的结果"
        result1 == result2
        result1 != null
        result1.length() == 40
    }

    def "测试完整的签名流程"() {
        given: "准备完整的签名参数"
        def appSecret = "my-app-secret-key"
        def requestBody = '{"userId":12345,"action":"updateProfile","data":{"name":"张三","email":"<EMAIL>"}}'
        def time = 1691136000L

        when: "执行完整的签名流程"
        // 1. 计算请求体MD5
        // 使用与实际实现一致的DigestUtils方法
        def md5 = DigestUtils.md5Hex(requestBody)

        // 2. 拼接签名字符串
        def signString = appSecret + md5 + time

        // 3. 计算SHA1签名
        def checksum = DigestUtils.sha1Hex(signString).toLowerCase()

        // 4. 验证签名
        def isValid = QiyuCrmSignatureUtil.validateNewAuthSignature(appSecret, requestBody, time, checksum)

        then: "整个流程应该成功"
        md5 != null
        md5.length() == 32
        checksum != null
        checksum.length() == 40
        isValid == true
    }
}
