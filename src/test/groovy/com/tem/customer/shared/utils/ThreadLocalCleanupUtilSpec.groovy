package com.tem.customer.shared.utils

import org.slf4j.MDC
import spock.lang.Specification

/**
 * ThreadLocalCleanupUtil 单元测试
 * 测试ThreadLocal清理工具类的各种功能
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
class ThreadLocalCleanupUtilSpec extends Specification {

    def cleanup() {
        // 每个测试后清理MDC
        MDC.clear()
    }

    def "测试完整的ThreadLocal清理"() {
        given: "设置一些ThreadLocal数据"
        MDC.put("traceId", "test-trace-123")
        MDC.put("userId", "user-456")
        MDC.put("sessionId", "session-789")

        when: "执行完整清理"
        ThreadLocalCleanupUtil.performCompleteCleanup()

        then: "应该正常执行，不抛出异常"
        noExceptionThrown()
    }

    def "测试ContextHolder清理"() {
        given: "设置MDC数据"
        MDC.put("testKey", "testValue")

        when: "执行完整清理（包含ContextHolder清理）"
        ThreadLocalCleanupUtil.performCompleteCleanup()

        then: "应该正常执行，不抛出异常"
        noExceptionThrown()
    }

    def "测试UserContextUtil清理"() {
        given: "模拟UserContextUtil中的ThreadLocal数据"
        // 注意：这里我们主要测试方法调用不会抛出异常
        // 实际的ThreadLocal清理效果需要在集成测试中验证

        when: "执行完整清理（包含UserContextUtil清理）"
        ThreadLocalCleanupUtil.performCompleteCleanup()

        then: "应该正常执行，不抛出异常"
        noExceptionThrown()
    }

    def "测试反射清理"() {
        given: "设置一些ThreadLocal数据"
        MDC.put("reflectionTest", "value")

        when: "执行完整清理（包含反射清理）"
        ThreadLocalCleanupUtil.performCompleteCleanup()

        then: "应该正常执行，不抛出异常"
        noExceptionThrown()
    }

    def "测试清理过程中的异常处理"() {
        when: "在正常情况下执行清理"
        ThreadLocalCleanupUtil.performCompleteCleanup()

        then: "应该正常执行，不抛出异常"
        noExceptionThrown()
    }

    def "测试多次清理的安全性"() {
        given: "设置一些ThreadLocal数据"
        MDC.put("multiCleanupTest", "value")

        when: "多次执行清理"
        ThreadLocalCleanupUtil.performCompleteCleanup()
        ThreadLocalCleanupUtil.performCompleteCleanup()
        ThreadLocalCleanupUtil.performCompleteCleanup()

        then: "应该安全执行，不抛出异常"
        noExceptionThrown()
    }

    def "测试空ThreadLocal的清理"() {
        given: "确保ThreadLocal为空"
        MDC.clear()

        when: "对空ThreadLocal执行清理"
        ThreadLocalCleanupUtil.performCompleteCleanup()

        then: "应该安全执行，不抛出异常"
        noExceptionThrown()
    }

    def "测试并发清理的安全性"() {
        given: "准备并发测试"
        def threads = []
        def exceptions = Collections.synchronizedList(new ArrayList<Exception>())

        when: "并发执行清理操作"
        (1..10).each { i ->
            threads.add(Thread.start {
                try {
                    // 每个线程设置自己的ThreadLocal数据
                    MDC.put("threadId", "thread-${i}")
                    MDC.put("timestamp", System.currentTimeMillis().toString())
                    
                    // 执行清理
                    ThreadLocalCleanupUtil.performCompleteCleanup()
                } catch (Exception e) {
                    exceptions.add(e)
                }
            })
        }

        threads.each { it.join() }

        then: "所有线程都应该安全执行"
        exceptions.isEmpty()
    }

    def "测试清理后的ThreadLocal状态"() {
        given: "设置多种类型的ThreadLocal数据"
        MDC.put("traceId", "trace-123")
        MDC.put("userId", "user-456")
        MDC.put("requestId", "req-789")
        MDC.put("sessionId", "session-abc")

        when: "执行清理"
        ThreadLocalCleanupUtil.performCompleteCleanup()

        then: "应该正常执行，不抛出异常"
        noExceptionThrown()

        // 验证hasRemainingUserContext方法正常工作
        def hasRemaining = ThreadLocalCleanupUtil.hasRemainingUserContext()
        hasRemaining != null // 方法应该返回boolean值，不抛异常
    }

    def "测试清理操作的性能"() {
        given: "设置大量ThreadLocal数据"
        (1..100).each { i ->
            MDC.put("key${i}", "value${i}")
        }

        when: "测量清理操作的执行时间"
        def startTime = System.currentTimeMillis()
        ThreadLocalCleanupUtil.performCompleteCleanup()
        def endTime = System.currentTimeMillis()
        def duration = endTime - startTime

        then: "清理操作应该在合理时间内完成"
        duration < 1000 // 应该在1秒内完成
        noExceptionThrown()
    }

    def "测试清理操作的幂等性"() {
        given: "设置ThreadLocal数据"
        MDC.put("idempotentTest", "value")

        when: "多次执行相同的清理操作"
        ThreadLocalCleanupUtil.performCompleteCleanup()
        def firstResult = ThreadLocalCleanupUtil.hasRemainingUserContext()

        ThreadLocalCleanupUtil.performCompleteCleanup()
        def secondResult = ThreadLocalCleanupUtil.hasRemainingUserContext()

        ThreadLocalCleanupUtil.performCompleteCleanup()
        def thirdResult = ThreadLocalCleanupUtil.hasRemainingUserContext()

        then: "多次清理应该都正常执行，不抛出异常"
        noExceptionThrown()
        firstResult != null
        secondResult != null
        thirdResult != null
    }

    def "测试清理过程中的线程信息记录"() {
        given: "当前线程"
        def currentThread = Thread.currentThread()
        def originalThreadName = currentThread.getName()

        when: "执行清理操作"
        ThreadLocalCleanupUtil.performCompleteCleanup()

        then: "线程信息应该被正确记录（通过日志）"
        // 这里主要验证操作不会影响线程本身
        Thread.currentThread().getName() == originalThreadName
        Thread.currentThread().threadId() == currentThread.threadId()
        noExceptionThrown()
    }

    def "测试特殊字符和编码的ThreadLocal清理"() {
        given: "设置包含特殊字符的ThreadLocal数据"
        MDC.put("中文Key", "中文值")
        MDC.put("specialChars", "special!@#\$%^&*()")
        MDC.put("emoji", "😀😃😄😁")

        when: "执行清理"
        ThreadLocalCleanupUtil.performCompleteCleanup()

        then: "应该正常执行，不抛出异常"
        noExceptionThrown()
    }

    def "测试清理操作对其他线程的影响"() {
        given: "在主线程设置ThreadLocal数据"
        MDC.put("mainThread", "mainValue")

        def otherThreadData = [:]
        def otherThread = Thread.start {
            MDC.put("otherThread", "otherValue")
            Thread.sleep(100) // 确保主线程有时间执行清理
            otherThreadData.put("afterCleanup", MDC.get("otherThread"))
        }

        when: "在主线程执行清理"
        ThreadLocalCleanupUtil.performCompleteCleanup()
        otherThread.join()

        then: "清理操作应该正常执行，不影响其他线程"
        noExceptionThrown()
        otherThreadData["afterCleanup"] == "otherValue" // 其他线程的数据不受影响
    }

    def "测试内存泄漏预防"() {
        given: "创建大量ThreadLocal数据"
        def largeData = "x" * 10000 // 10KB的字符串
        (1..50).each { i ->
            MDC.put("largeKey${i}", largeData)
        }

        when: "执行清理"
        ThreadLocalCleanupUtil.performCompleteCleanup()

        then: "应该正常执行，不抛出异常"
        noExceptionThrown()

        // 验证清理方法能处理大量数据而不出错
        def hasRemaining = ThreadLocalCleanupUtil.hasRemainingUserContext()
        hasRemaining != null
    }
}
