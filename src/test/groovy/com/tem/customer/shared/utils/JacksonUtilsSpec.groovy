package com.tem.customer.shared.utils

import com.fasterxml.jackson.core.type.TypeReference
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.node.ArrayNode
import com.fasterxml.jackson.databind.node.ObjectNode
import spock.lang.Specification
import spock.lang.Unroll

import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.ZonedDateTime

/**
 * JacksonUtils 单元测试
 * 测试JSON工具类的序列化、反序列化、异常处理等功能
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
class JacksonUtilsSpec extends Specification {

    def "测试私有构造函数"() {
        when: "尝试实例化工具类"
        def utils = new JacksonUtils()

        then: "应该能正常实例化"
        utils != null
    }

    def "测试对象转JSON字符串"() {
        given: "准备测试对象"
        def testObj = [
            name: "张三",
            age: 25,
            email: "<EMAIL>",
            active: true
        ]

        when: "转换为JSON字符串"
        def result = JacksonUtils.toJson(testObj)

        then: "应该返回正确的JSON字符串"
        result != null
        result.contains('"name":"张三"')
        result.contains('"age":25')
        result.contains('"email":"<EMAIL>"')
        result.contains('"active":true')
    }

    def "测试null对象转JSON"() {
        when: "转换null对象"
        def result = JacksonUtils.toJson(null)

        then: "应该返回null字符串"
        result == "null"
    }

    def "测试空对象转JSON"() {
        when: "转换空Map"
        def result = JacksonUtils.toJson([:])

        then: "应该返回空JSON对象"
        result == "{}"
    }

    def "测试对象转JSON字节数组"() {
        given: "准备测试对象"
        def testObj = [name: "测试", value: 123]

        when: "转换为JSON字节数组"
        def result = JacksonUtils.toJsonBytes(testObj)

        then: "应该返回正确的字节数组"
        result != null
        result.length > 0
        new String(result, "UTF-8").contains('"name":"测试"')
    }

    def "测试JSON字符串转对象"() {
        given: "准备JSON字符串"
        def jsonStr = '{"name":"李四","age":30,"active":false}'

        when: "转换为Map对象"
        def result = JacksonUtils.toObj(jsonStr, Map.class)

        then: "应该返回正确的对象"
        result != null
        result.name == "李四"
        result.age == 30
        result.active == false
    }

    def "测试JSON字符串转指定类型对象"() {
        given: "准备JSON字符串和目标类型"
        def jsonStr = '{"name":"王五","age":35}'
        
        when: "转换为指定类型"
        def result = JacksonUtils.toObj(jsonStr, TestUser.class)

        then: "应该返回正确类型的对象"
        result != null
        result instanceof TestUser
        result.name == "王五"
        result.age == 35
    }

    def "测试JSON字节数组转对象"() {
        given: "准备JSON字节数组"
        def jsonBytes = '{"name":"赵六","value":100}'.getBytes("UTF-8")

        when: "转换为Map对象"
        def result = JacksonUtils.toObj(jsonBytes, Map.class)

        then: "应该返回正确的对象"
        result != null
        result.name == "赵六"
        result.value == 100
    }

    def "测试JSON字符串转JsonNode"() {
        given: "准备JSON字符串"
        def jsonStr = '{"name":"测试","items":[1,2,3],"nested":{"key":"value"}}'

        when: "转换为JsonNode"
        def result = JacksonUtils.toObj(jsonStr)

        then: "应该返回JsonNode对象"
        result != null
        result instanceof JsonNode
        result.get("name").asText() == "测试"
        result.get("items").isArray()
        result.get("items").size() == 3
        result.get("nested").get("key").asText() == "value"
    }

    @Unroll
    def "测试JSON格式验证 - 输入: #input, 期望: #expected"() {
        when:
        def result = JacksonUtils.isJson(input)

        then:
        result == expected

        where:
        input                           | expected
        '{"name":"test"}'               | true
        '{"name":"test","age":25}'      | true
        '[1,2,3]'                       | true
        '[]'                            | true
        '{}'                            | true
        'null'                          | true
        'true'                          | true
        'false'                         | true
        '"string"'                      | true
        '123'                           | true
        '123.45'                        | true
        'invalid json'                  | false
        '{"name":}'                     | false
        '{"name":"test",}'              | false
        '{name:"test"}'                 | false
        ''                              | false
    }

    def "测试JSON格式验证 - null值处理"() {
        when: "验证null值"
        JacksonUtils.isJson(null)

        then: "应该抛出IllegalArgumentException"
        thrown(IllegalArgumentException)
    }

    def "测试序列化异常处理"() {
        given: "准备一个无法序列化的对象"
        def problematicObj = new Object() {
            def getValue() {
                throw new RuntimeException("序列化错误")
            }
        }

        when: "尝试序列化"
        JacksonUtils.toJson(problematicObj)

        then: "应该抛出RuntimeException"
        def ex = thrown(RuntimeException)
        ex.message.contains("JSON serialization failed")
    }

    def "测试反序列化异常处理"() {
        given: "准备无效的JSON字符串"
        def invalidJson = '{"name":"test"'

        when: "尝试反序列化"
        JacksonUtils.toObj(invalidJson, Map.class)

        then: "应该抛出RuntimeException"
        def ex = thrown(RuntimeException)
        ex.message.contains("JSON deserialization failed")
    }

    def "测试时间格式处理"() {
        given: "准备包含时间的对象"
        def testObj = [
            name: "时间测试",
            createTime: LocalDateTime.of(2025, 8, 4, 10, 30, 45)
        ]

        when: "序列化包含时间的对象"
        def jsonStr = JacksonUtils.toJson(testObj)

        then: "应该正确格式化时间"
        jsonStr != null
        jsonStr.contains("2025-08-04 10:30:45")
    }

    def "测试null值处理"() {
        given: "准备包含null值的对象"
        def testObj = [
            name: "测试",
            value: null,
            description: "有值"
        ]

        when: "序列化对象"
        def jsonStr = JacksonUtils.toJson(testObj)

        then: "null值应该被排除"
        jsonStr != null
        jsonStr.contains('"name":"测试"')
        jsonStr.contains('"description":"有值"')
        !jsonStr.contains('"value"')
    }

    def "测试未知属性处理"() {
        given: "准备包含未知属性的JSON"
        def jsonStr = '{"name":"测试","age":25,"unknownField":"unknown","anotherUnknown":123}'

        when: "反序列化为TestUser对象"
        def result = JacksonUtils.toObj(jsonStr, TestUser.class)

        then: "应该忽略未知属性，正常反序列化"
        result != null
        result.name == "测试"
        result.age == 25
    }

    def "测试空字符串转null处理"() {
        given: "准备包含空字符串的JSON"
        def jsonStr = '{"name":"","age":25}'

        when: "反序列化为TestUser对象"
        def result = JacksonUtils.toObj(jsonStr, TestUser.class)

        then: "空字符串应该被转换为null"
        result != null
        result.name == null
        result.age == 25
    }

    def "测试复杂嵌套对象"() {
        given: "准备复杂嵌套对象"
        def complexObj = [
            user: [
                name: "张三",
                profile: [
                    age: 30,
                    hobbies: ["读书", "游泳", "编程"]
                ]
            ],
            metadata: [
                version: "1.0",
                timestamp: System.currentTimeMillis()
            ]
        ]

        when: "序列化和反序列化"
        def jsonStr = JacksonUtils.toJson(complexObj)
        def result = JacksonUtils.toObj(jsonStr, Map.class)

        then: "应该正确处理嵌套结构"
        jsonStr != null
        result != null
        result.user.name == "张三"
        result.user.profile.age == 30
        result.user.profile.hobbies.size() == 3
        result.metadata.version == "1.0"
    }

    // ==================== 新增功能测试 ====================

    def "测试美化输出JSON"() {
        given: "准备测试对象"
        def testObj = [
            name: "张三",
            age: 25,
            hobbies: ["读书", "编程"]
        ]

        when: "转换为美化JSON字符串"
        def result = JacksonUtils.toPrettyJson(testObj)

        then: "应该返回格式化的JSON字符串"
        result != null
        result.contains("\n")  // 包含换行符
        result.contains("  \"name\" : \"张三\"")
        result.contains("  \"age\" : 25")
        result.contains("  \"hobbies\" : [")
        // 验证格式化效果：普通JSON不应该包含缩进
        def normalJson = JacksonUtils.toJson(testObj)
        !normalJson.contains("  \"name\"")  // 普通JSON没有缩进
    }

    def "测试多种时间类型序列化"() {
        given: "准备包含多种时间类型的对象"
        def testObj = [
            localDateTime: LocalDateTime.of(2025, 8, 4, 10, 30, 45),
            localDate: LocalDate.of(2025, 8, 4),
            localTime: LocalTime.of(10, 30, 45),
            zonedDateTime: ZonedDateTime.parse("2025-08-04T10:30:45+08:00")
        ]

        when: "序列化对象"
        def jsonStr = JacksonUtils.toJson(testObj)

        then: "应该正确格式化各种时间类型"
        jsonStr != null
        jsonStr.contains("2025-08-04 10:30:45")  // LocalDateTime
        jsonStr.contains("2025-08-04")           // LocalDate
        jsonStr.contains("10:30:45")             // LocalTime
        jsonStr.contains("+08:00")               // ZonedDateTime
    }

    def "测试convertValue对象转换"() {
        given: "准备源对象"
        def sourceMap = [name: "张三", age: 25]

        when: "转换为TestUser对象"
        def result = JacksonUtils.convertValue(sourceMap, TestUser.class)

        then: "应该成功转换"
        result != null
        result instanceof TestUser
        result.name == "张三"
        result.age == 25
    }

    def "测试convertValue泛型转换"() {
        given: "准备源对象"
        def sourceList = [
            [name: "张三", age: 25],
            [name: "李四", age: 30]
        ]

        when: "转换为List<TestUser>"
        def result = JacksonUtils.convertValue(sourceList, new TypeReference<List<TestUser>>() {})

        then: "应该成功转换"
        result != null
        result.size() == 2
        result[0] instanceof TestUser
        result[0].name == "张三"
        result[1].name == "李四"
    }

    def "测试深拷贝对象"() {
        given: "准备源对象"
        def original = new TestUser("张三", 25)

        when: "深拷贝对象"
        def copy = JacksonUtils.deepCopy(original, TestUser.class)

        then: "应该创建独立的副本"
        copy != null
        copy != original  // 不是同一个对象
        copy.name == original.name
        copy.age == original.age
    }

    def "测试JSON合并"() {
        given: "准备两个JSON字符串"
        def json1 = '{"name":"张三","age":25,"city":"北京"}'
        def json2 = '{"age":30,"email":"<EMAIL>"}'

        when: "合并JSON"
        def result = JacksonUtils.mergeJson(json1, json2)
        def merged = JacksonUtils.toObj(result, Map.class)

        then: "应该正确合并"
        merged != null
        merged.name == "张三"      // 保留json1的值
        merged.age == 30          // json2覆盖json1
        merged.city == "北京"      // 保留json1的值
        merged.email == "<EMAIL>"  // 新增json2的值
    }

    def "测试JSON路径查询"() {
        given: "准备复杂JSON"
        def json = '{"user":{"name":"张三","profile":{"age":25,"city":"北京"}},"items":[1,2,3]}'

        when: "使用路径查询"
        def nameNode = JacksonUtils.getValueByPath(json, "/user/name")
        def ageNode = JacksonUtils.getValueByPath(json, "/user/profile/age")
        def firstItem = JacksonUtils.getValueByPath(json, "/items/0")

        then: "应该正确获取值"
        nameNode != null && nameNode.asText() == "张三"
        ageNode != null && ageNode.asInt() == 25
        firstItem != null && firstItem.asInt() == 1
    }

    def "测试类型化路径查询"() {
        given: "准备JSON"
        def json = '{"name":"张三","age":25,"active":true,"score":98.5}'

        when: "使用类型化查询"
        def name = JacksonUtils.getStringByPath(json, "/name")
        def age = JacksonUtils.getIntByPath(json, "/age")
        def active = JacksonUtils.getBooleanByPath(json, "/active")

        then: "应该返回正确类型的值"
        name == "张三"
        age == 25
        active == true
    }

    def "测试字段名递归查找"() {
        given: "准备嵌套JSON"
        def json = '{"user":{"profile":{"name":"张三"},"settings":{"name":"用户设置"}}}'

        when: "查找字段名"
        def firstNameNode = JacksonUtils.findValueByFieldName(json, "name")
        def allNameNodes = JacksonUtils.findValuesByFieldName(json, "name")

        then: "应该找到对应的值"
        firstNameNode != null
        firstNameNode.asText() == "张三"  // 找到第一个
        allNameNodes.size() == 2         // 找到所有的
    }

    def "测试创建JSON节点"() {
        when: "创建各种JSON节点"
        def objectNode = JacksonUtils.createObjectNode()
        def arrayNode = JacksonUtils.createArrayNode()
        def mapNode = JacksonUtils.createObjectNode([name: "张三", age: 25])
        def listNode = JacksonUtils.createArrayNode([1, 2, 3])

        then: "应该创建正确的节点"
        objectNode instanceof ObjectNode
        arrayNode instanceof ArrayNode
        mapNode.get("name").asText() == "张三"
        listNode.size() == 3
    }

    def "测试工具方法"() {
        when: "获取mapper实例"
        def mapper = JacksonUtils.getMapper()
        def prettyMapper = JacksonUtils.getPrettyMapper()

        then: "应该返回有效的mapper"
        mapper != null
        prettyMapper != null
        mapper != prettyMapper  // 应该是不同的实例
    }

    def "测试节点空值检查"() {
        given: "准备各种节点"
        def json = '{"name":"张三","empty":null}'
        def rootNode = JacksonUtils.toObj(json)
        def nameNode = rootNode.get("name")
        def emptyNode = rootNode.get("empty")
        def missingNode = rootNode.get("notexist")

        when: "检查节点"
        def nameIsNull = JacksonUtils.isNullOrMissing(nameNode)
        def emptyIsNull = JacksonUtils.isNullOrMissing(emptyNode)
        def missingIsNull = JacksonUtils.isNullOrMissing(missingNode)

        then: "应该正确判断"
        !nameIsNull      // 有值的节点
        emptyIsNull      // null节点
        missingIsNull    // 不存在的节点
    }

    def "测试JSON类型检查"() {
        given: "准备不同类型的JSON字符串"
        def objectJson = '{"name":"张三"}'
        def arrayJson = '[1,2,3]'
        def invalidJson = 'invalid'

        when: "检查JSON类型"
        def isObjectJson = JacksonUtils.isJsonObject(objectJson)
        def isArrayJson = JacksonUtils.isJsonArray(arrayJson)
        def objectIsArray = JacksonUtils.isJsonArray(objectJson)
        def arrayIsObject = JacksonUtils.isJsonObject(arrayJson)
        def invalidIsObject = JacksonUtils.isJsonObject(invalidJson)
        def invalidIsArray = JacksonUtils.isJsonArray(invalidJson)

        then: "应该正确判断类型"
        isObjectJson
        isArrayJson
        !objectIsArray
        !arrayIsObject
        !invalidIsObject
        !invalidIsArray
    }

    def "测试异常处理 - convertValue"() {
        given: "准备不兼容的对象"
        def incompatibleObj = "字符串不能转换为TestUser"

        when: "尝试转换"
        JacksonUtils.convertValue(incompatibleObj, TestUser.class)

        then: "应该抛出RuntimeException"
        def ex = thrown(RuntimeException)
        ex.message.contains("Object conversion failed")
    }

    def "测试异常处理 - mergeJson"() {
        given: "准备无效的JSON"
        def validJson = '{"name":"张三"}'
        def invalidJson = '{"name":}'

        when: "尝试合并"
        JacksonUtils.mergeJson(validJson, invalidJson)

        then: "应该抛出RuntimeException"
        def ex = thrown(RuntimeException)
        ex.message.contains("JSON merge failed")
    }

    def "测试异常处理 - getValueByPath"() {
        given: "准备无效的JSON"
        def invalidJson = '{"name":}'

        when: "尝试路径查询"
        JacksonUtils.getValueByPath(invalidJson, "/name")

        then: "应该抛出RuntimeException"
        def ex = thrown(RuntimeException)
        ex.message.contains("Failed to get value by path")
    }

    def "测试边界情况 - 空字符串和null"() {
        when: "测试各种边界情况"
        def emptyObjectJson = JacksonUtils.isJsonObject("")
        def emptyArrayJson = JacksonUtils.isJsonArray("")
        def nullObjectJson = JacksonUtils.isJsonObject(null)
        def nullArrayJson = JacksonUtils.isJsonArray(null)

        then: "应该正确处理边界情况"
        !emptyObjectJson
        !emptyArrayJson
        !nullObjectJson
        !nullArrayJson
    }

    // 测试用的简单类
    static class TestUser {
        String name
        Integer age

        TestUser() {}

        TestUser(String name, Integer age) {
            this.name = name
            this.age = age
        }
    }
}
