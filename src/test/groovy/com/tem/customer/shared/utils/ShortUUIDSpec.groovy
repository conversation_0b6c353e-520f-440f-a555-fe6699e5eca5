package com.tem.customer.shared.utils

import spock.lang.Specification
import spock.lang.Unroll
/**
 * ShortUUID 单元测试
 * 测试22位短UUID字符串生成类的各种功能
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
class ShortUUIDSpec extends Specification {

    def "测试生成标准短UUID"() {
        when: "生成短UUID"
        def uuid1 = ShortUUID.getUuid()
        def uuid2 = ShortUUID.getUuid()

        then: "应该生成22位长度的不同UUID"
        uuid1 != null
        uuid2 != null
        uuid1.length() == 22
        uuid2.length() == 22
        uuid1 != uuid2
    }

    def "测试UUID字符集合法性"() {
        when: "生成多个UUID"
        def uuids = (1..100).collect { ShortUUID.getUuid() }

        then: "所有UUID都应该只包含合法字符"
        uuids.each { uuid ->
            assert uuid.matches(/^[A-Za-z0-9\-_]+$/)
            assert uuid.length() == 22
        }
    }

    def "测试生成带时间戳的TraceId"() {
        when: "生成带时间戳的TraceId"
        def traceId1 = ShortUUID.getTimestampTraceId()
        Thread.sleep(10) // 确保时间戳不同
        def traceId2 = ShortUUID.getTimestampTraceId()

        then: "应该生成22位长度的不同TraceId"
        traceId1 != null
        traceId2 != null
        traceId1.length() == 22
        traceId2.length() == 22
        traceId1 != traceId2
    }

    def "测试生成不含特殊字符的UUID"() {
        when: "生成不含特殊字符的UUID"
        def uuids = (1..50).collect { ShortUUID.getUuidWithoutSpecialChars() }

        then: "所有UUID都不应该包含横线和下划线"
        uuids.each { uuid ->
            assert uuid.length() == 22
            assert !uuid.contains('-')
            assert !uuid.contains('_')
            assert uuid.matches(/^[A-Za-z0-9XY]+$/)
        }
    }

    def "测试生成不含特殊字符的带时间戳TraceId"() {
        when: "生成不含特殊字符的带时间戳TraceId"
        def traceIds = (1..50).collect { ShortUUID.getTimestampTraceIdWithoutSpecialChars() }

        then: "所有TraceId都不应该包含横线和下划线"
        traceIds.each { traceId ->
            assert traceId.length() == 22
            assert !traceId.contains('-')
            assert !traceId.contains('_')
            assert traceId.matches(/^[A-Za-z0-9XY]+$/)
        }
    }

    @Unroll
    def "测试TraceId格式验证 - 输入: #input, 期望: #expected"() {
        when:
        def result = ShortUUID.isValidTraceId(input)

        then:
        result == expected

        where:
        input                      | expected
        "AbCdEfGhIjKlMnOpQrStUv"   | true
        "1234567890123456789012"   | true
        "ABCDEFGHIJKLMNOPQRSTUV"   | true
        "abcdefghijklmnopqrstuv"   | true
        "A1B2C3D4E5F6G7H8I9J0K1"   | true
        "Test-Trace_ID_123"        | true
        "ShortTraceId"             | true
        "A"                        | true
        ""                         | false
        null                       | false
        "   "                      | false
        "12345678901234567890123"  | false  // 超过22位
        "Invalid@TraceId"          | false  // 包含非法字符
        "Trace Id With Spaces"     | false  // 包含空格
        "TraceId#WithHash"         | false  // 包含井号
        "TraceId%WithPercent"      | false  // 包含百分号
    }

    def "测试UUID唯一性"() {
        when: "生成大量UUID"
        def uuidSet = new HashSet<String>()
        def count = 10000
        
        (1..count).each {
            uuidSet.add(ShortUUID.getUuid())
        }

        then: "所有UUID都应该是唯一的"
        uuidSet.size() == count
    }

    def "测试带时间戳TraceId的时间顺序性"() {
        when: "按时间顺序生成TraceId"
        def traceIds = []
        (1..10).each {
            traceIds.add(ShortUUID.getTimestampTraceId())
            Thread.sleep(5) // 确保时间差异
        }

        then: "TraceId应该体现时间顺序（至少前几位应该相似或递增）"
        traceIds.size() == 10
        // 验证所有TraceId都是有效的
        traceIds.each { traceId ->
            assert ShortUUID.isValidTraceId(traceId)
            assert traceId.length() == 22
        }
    }

    def "测试特殊字符替换的一致性"() {
        when: "生成包含特殊字符的UUID并替换"
        def originalUuids = (1..100).collect { ShortUUID.getUuid() }
        def cleanUuids = originalUuids.collect { ShortUUID.getUuidWithoutSpecialChars() }

        then: "替换应该是一致的"
        originalUuids.size() == cleanUuids.size()
        cleanUuids.each { uuid ->
            assert !uuid.contains('-')
            assert !uuid.contains('_')
        }
    }

    def "测试并发生成UUID的安全性"() {
        when: "并发生成UUID"
        def uuidSet = Collections.synchronizedSet(new HashSet<String>())
        def threads = []
        def threadCount = 10
        def uuidsPerThread = 1000

        (1..threadCount).each { threadIndex ->
            threads.add(Thread.start {
                (1..uuidsPerThread).each {
                    uuidSet.add(ShortUUID.getUuid())
                }
            })
        }

        threads.each { it.join() }

        then: "所有UUID都应该是唯一的"
        uuidSet.size() == threadCount * uuidsPerThread
    }

    def "测试边界值处理"() {
        when: "测试各种边界情况"
        def validTraceIds = [
            "A",                      // 最短有效
            "1234567890123456789012", // 22位数字
            "ABCDEFGHIJKLMNOPQRSTUV", // 22位大写字母
            "abcdefghijklmnopqrstuv", // 22位小写字母
            "A-B_C1D2E3F4G5H6I7J8K9", // 包含特殊字符
        ]

        def invalidTraceIds = [
            "123456789012345678901234", // 超过22位
            "Test@Invalid",              // 包含非法字符
            "Test Invalid",              // 包含空格
        ]

        then: "应该正确验证边界值"
        validTraceIds.each { traceId ->
            assert ShortUUID.isValidTraceId(traceId)
        }
        
        invalidTraceIds.each { traceId ->
            assert !ShortUUID.isValidTraceId(traceId)
        }
    }

    def "测试UUID字符分布"() {
        when: "生成大量UUID并分析字符分布"
        def uuids = (1..1000).collect { ShortUUID.getUuid() }
        def charCounts = [:]

        uuids.each { uuid ->
            uuid.each { char1 ->
                charCounts[char1] = (charCounts[char1] ?: 0) + 1
            }
        }

        then: "字符分布应该相对均匀"
        charCounts.size() > 10 // 应该使用多种字符
        // 验证只包含合法字符
        charCounts.keySet().each { char1 ->
            assert char1.toString().matches(/[A-Za-z0-9\-_]/)
        }
    }

    def "测试长时间运行的稳定性"() {
        when: "长时间生成UUID"
        def startTime = System.currentTimeMillis()
        def uuids = []
        
        while (System.currentTimeMillis() - startTime < 100) { // 运行100毫秒
            uuids.add(ShortUUID.getUuid())
        }

        then: "应该稳定生成有效的UUID"
        uuids.size() > 0
        uuids.each { uuid ->
            assert uuid != null
            assert uuid.length() == 22
            assert ShortUUID.isValidTraceId(uuid)
        }
        // 验证唯一性
        assert uuids.toSet().size() == uuids.size()
    }

    def "测试内存使用效率"() {
        when: "生成大量UUID"
        def uuids = (1..10000).collect { ShortUUID.getUuid() }

        then: "应该高效生成UUID"
        uuids.size() == 10000
        uuids.each { uuid ->
            assert uuid.length() == 22
        }
        // 验证没有内存泄漏（通过唯一性检查）
        assert uuids.toSet().size() == uuids.size()
    }
}
