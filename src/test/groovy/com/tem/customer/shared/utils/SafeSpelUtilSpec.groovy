package com.tem.customer.shared.utils

import spock.lang.Specification
import spock.lang.Unroll
/**
 * SafeSpelUtil 单元测试
 * 测试安全的SpEL表达式工具类的各种功能
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
class SafeSpelUtilSpec extends Specification {

    // 测试用的业务对象
    static class TestBusinessObject {
        String name
        Integer age
        Long id

        TestBusinessObject(String name, Integer age, Long id) {
            this.name = name
            this.age = age
            this.id = id
        }

        void setName(String name) {
            this.name = name
        }

        void updateInfo(String name, Integer age) {
            this.name = name
            this.age = age
        }
    }

    def "测试安全表达式解析 - 基本功能"() {
        given: "准备测试参数"
        def method = String.class.getMethod("toString")
        def args = [] as Object[]
        def result = "test"

        when: "解析安全表达式"
        def parseResult = SafeSpelUtil.safeParseExpression("#result", method, args, result)

        then: "应该返回正确的结果"
        parseResult == "test"
    }

    def "测试Long表达式解析"() {
        given: "准备测试参数"
        def method = String.class.getMethod("toString")
        def args = [] as Object[]
        def result = 123L

        when: "解析Long表达式"
        def parseResult = SafeSpelUtil.parseLongExpression("#result", method, args, result)

        then: "应该返回正确的Long结果"
        parseResult == 123L
    }

    def "测试String表达式解析"() {
        given: "准备测试参数"
        def method = String.class.getMethod("toString")
        def args = [] as Object[]
        def result = "Hello World"

        when: "解析String表达式"
        def parseResult = SafeSpelUtil.parseStringExpression("#result", method, args, result)

        then: "应该返回正确的String结果"
        parseResult == "Hello World"
    }

    def "测试null表达式处理"() {
        given: "准备测试参数"
        def method = String.class.getMethod("toString")
        def args = [] as Object[]
        def result = "test"

        when: "解析null表达式"
        def parseResult = SafeSpelUtil.safeParseExpression(null, method, args, result)

        then: "应该返回null"
        parseResult == null
    }

    def "测试空表达式处理"() {
        given: "准备测试参数"
        def method = String.class.getMethod("toString")
        def args = [] as Object[]
        def result = "test"

        when: "解析空表达式"
        def parseResult = SafeSpelUtil.safeParseExpression("", method, args, result)

        then: "应该返回null"
        parseResult == null
    }

    def "测试空白表达式处理"() {
        given: "准备测试参数"
        def method = String.class.getMethod("toString")
        def args = [] as Object[]
        def result = "test"

        when: "解析空白表达式"
        def parseResult = SafeSpelUtil.safeParseExpression("   ", method, args, result)

        then: "应该返回null"
        parseResult == null
    }

    def "测试业务对象属性访问"() {
        given: "准备业务对象"
        def method = TestBusinessObject.class.getMethod("getName")
        def args = [] as Object[]
        def result = new TestBusinessObject("张三", 25, 123L)

        when: "解析对象属性表达式"
        def nameResult = SafeSpelUtil.safeParseExpression("#result.name", method, args, result)
        def ageResult = SafeSpelUtil.safeParseExpression("#result.age", method, args, result)
        def idResult = SafeSpelUtil.safeParseExpression("#result.id", method, args, result)

        then: "应该返回正确的属性值"
        nameResult == "张三"
        ageResult == 25
        idResult == 123L
    }

    def "测试复杂表达式计算"() {
        given: "准备测试参数"
        def method = TestBusinessObject.class.getMethod("getName")
        def args = [] as Object[]
        def result = new TestBusinessObject("李四", 30, 456L)

        when: "解析复杂表达式"
        def stringConcatResult = SafeSpelUtil.safeParseExpression("#result.name + ' - ' + #result.age", method, args, result)
        def booleanResult = SafeSpelUtil.safeParseExpression("#result.age > 18", method, args, result)
        def nullCheckResult = SafeSpelUtil.safeParseExpression("#result.id != null", method, args, result)

        then: "应该返回正确的计算结果"
        stringConcatResult == "李四 - 30"
        booleanResult == true
        nullCheckResult == true
    }

    def "测试常量表达式"() {
        given: "准备测试参数"
        def method = String.class.getMethod("toString")
        def args = [] as Object[]
        def result = "test"

        when: "解析常量表达式"
        def stringConstant = SafeSpelUtil.safeParseExpression("'常量字符串'", method, args, result)
        def numberConstant = SafeSpelUtil.safeParseExpression("123", method, args, result)
        def booleanConstant = SafeSpelUtil.safeParseExpression("true", method, args, result)

        then: "应该返回正确的常量值"
        stringConstant == "常量字符串"
        numberConstant == 123
        booleanConstant == true
    }

    def "测试null结果处理"() {
        given: "准备测试参数"
        def method = String.class.getMethod("toString")
        def args = [] as Object[]
        def result = null

        when: "解析包含null结果的表达式"
        def parseResult = SafeSpelUtil.safeParseExpression("#result", method, args, result)

        then: "应该返回null"
        parseResult == null
    }

    // ========== 危险表达式检测测试 ==========

    @Unroll
    def "测试危险表达式检测 - T()操作符: #expression"() {
        given: "准备测试参数"
        def method = String.class.getMethod("toString")
        def args = [] as Object[]
        def result = "test"

        when: "解析危险表达式"
        def parseResult = SafeSpelUtil.safeParseExpression(expression, method, args, result)

        then: "应该返回null（被安全检查拦截）"
        parseResult == null

        where:
        expression << [
            "T(java.lang.Runtime).getRuntime().exec('ls')",
            "T(java.lang.System).exit(0)",
            "T(java.lang.ProcessBuilder)",
            "T(java.lang.Class).forName('java.lang.Runtime')",
            "T(java.io.File)",
            "T(java.nio.file.Files)"
        ]
    }

    @Unroll
    def "测试危险表达式检测 - Bean引用: #expression"() {
        given: "准备测试参数"
        def method = String.class.getMethod("toString")
        def args = [] as Object[]
        def result = "test"

        when: "解析危险表达式"
        def parseResult = SafeSpelUtil.safeParseExpression(expression, method, args, result)

        then: "应该返回null（被安全检查拦截）"
        parseResult == null

        where:
        expression << [
            "@systemService.dangerousMethod()",
            "@userService.deleteAll()",
            "@applicationContext.getBean('dangerousBean')"
        ]
    }

    @Unroll
    def "测试危险表达式检测 - 构造函数调用: #expression"() {
        given: "准备测试参数"
        def method = String.class.getMethod("toString")
        def args = [] as Object[]
        def result = "test"

        when: "解析危险表达式"
        def parseResult = SafeSpelUtil.safeParseExpression(expression, method, args, result)

        then: "应该返回null（被安全检查拦截）"
        parseResult == null

        where:
        expression << [
            "new java.io.File('/etc/passwd')",
            "new java.lang.ProcessBuilder('rm', '-rf', '/')",
            "new java.net.URL('http://evil.com')"
        ]
    }

    @Unroll
    def "测试危险表达式检测 - 反射相关: #expression"() {
        given: "准备测试参数"
        def method = String.class.getMethod("toString")
        def args = [] as Object[]
        def result = new TestBusinessObject("test", 25, 123L)

        when: "解析危险表达式"
        def parseResult = SafeSpelUtil.safeParseExpression(expression, method, args, result)

        then: "应该返回null（被安全检查拦截）"
        parseResult == null

        where:
        expression << [
            "#result.getClass().forName('java.lang.Runtime')",
            "#result.class.classLoader",
            "getClass().getDeclaredMethod('exec')",
            "#result.getClass().getMethod('toString').invoke(#result)",
            "constructor",
            "field",
            "method",
            "reflect"
        ]
    }

    @Unroll
    def "测试危险方法名检测: #expression"() {
        given: "准备测试参数"
        def method = String.class.getMethod("toString")
        def args = [] as Object[]
        def result = "test"

        when: "解析包含危险方法的表达式"
        def parseResult = SafeSpelUtil.safeParseExpression(expression, method, args, result)

        then: "应该返回null（被安全检查拦截）"
        parseResult == null

        where:
        expression << [
            "getClass()",
            "forName('java.lang.String')",
            "newInstance()",
            "getClassLoader()",
            "getMethod('toString')",
            "getDeclaredMethod('toString')",
            "invoke()",
            "exec('ls')",
            "getRuntime()",
            "exit(0)",
            "halt(0)",
            "load('library')",
            "loadLibrary('library')"
        ]
    }

    @Unroll
    def "测试危险类名检测: #className"() {
        given: "准备测试参数"
        def method = String.class.getMethod("toString")
        def args = [] as Object[]
        def result = "test"
        def expression = "T(" + className + ")"

        when: "解析包含危险类的表达式"
        def parseResult = SafeSpelUtil.safeParseExpression(expression, method, args, result)

        then: "应该返回null（被安全检查拦截）"
        parseResult == null

        where:
        className << [
            "java.lang.Runtime",
            "java.lang.ProcessBuilder",
            "java.lang.System",
            "java.lang.Class",
            "java.lang.ClassLoader",
            "java.io.File",
            "java.nio.file.Files",
            "java.nio.file.Paths"
        ]
    }

    def "测试混合危险表达式"() {
        given: "准备测试参数"
        def method = String.class.getMethod("toString")
        def args = [] as Object[]
        def result = "test"

        when: "解析复杂的危险表达式"
        def expressions = [
            "T(java.lang.Runtime).getRuntime().exec('rm -rf /')",
            "@applicationContext.getBean('userService').getClass().forName('java.lang.System').exit(0)",
            "new java.io.File('/etc/passwd').getClass().getMethod('delete').invoke()",
            "#result.getClass().getClassLoader().loadClass('java.lang.Runtime')"
        ]

        then: "所有危险表达式都应该被拦截"
        expressions.each { expr ->
            def parseResult = SafeSpelUtil.safeParseExpression(expr, method, args, result)
            assert parseResult == null : "危险表达式应该被拦截: ${expr}"
        }
    }

    // ========== Long类型转换测试 ==========

    def "测试Long表达式解析 - 数字类型转换"() {
        given: "准备测试参数"
        def method = String.class.getMethod("toString")
        def args = [] as Object[]

        when: "解析不同数字类型的表达式"
        def intResult = SafeSpelUtil.parseLongExpression("#result", method, args, 123)
        def longResult = SafeSpelUtil.parseLongExpression("#result", method, args, 456L)
        def doubleResult = SafeSpelUtil.parseLongExpression("#result", method, args, 789.0d)
        def floatResult = SafeSpelUtil.parseLongExpression("#result", method, args, 321.5f)

        then: "应该正确转换为Long类型"
        intResult == 123L
        longResult == 456L
        doubleResult == 789L
        floatResult == 321L
    }

    def "测试Long表达式解析 - 字符串转换"() {
        given: "准备测试参数"
        def method = String.class.getMethod("toString")
        def args = [] as Object[]

        when: "解析字符串数字表达式"
        def validStringResult = SafeSpelUtil.parseLongExpression("#result", method, args, "123")
        def invalidStringResult = SafeSpelUtil.parseLongExpression("#result", method, args, "not_a_number")

        then: "有效字符串应该转换成功，无效字符串应该返回null"
        validStringResult == 123L
        invalidStringResult == null
    }

    def "测试Long表达式解析 - null和空值处理"() {
        given: "准备测试参数"
        def method = String.class.getMethod("toString")
        def args = [] as Object[]

        when: "解析null和空值表达式"
        def nullExpressionResult = SafeSpelUtil.parseLongExpression(null, method, args, 123L)
        def emptyExpressionResult = SafeSpelUtil.parseLongExpression("", method, args, 123L)
        def nullResultResult = SafeSpelUtil.parseLongExpression("#result", method, args, null)

        then: "应该正确处理null和空值"
        nullExpressionResult == null
        emptyExpressionResult == null
        nullResultResult == null
    }

    def "测试Long表达式解析 - result为null时的特殊处理"() {
        given: "准备测试参数"
        def method = String.class.getMethod("toString")
        def args = [] as Object[]

        when: "解析引用null result的表达式"
        def result = SafeSpelUtil.parseLongExpression("#result.someProperty", method, args, null)

        then: "应该返回null并记录警告"
        result == null
    }

    // ========== String类型转换测试 ==========

    def "测试String表达式解析 - 各种类型转换"() {
        given: "准备测试参数"
        def method = String.class.getMethod("toString")
        def args = [] as Object[]

        when: "解析不同类型的表达式"
        def stringResult = SafeSpelUtil.parseStringExpression("#result", method, args, "Hello")
        def numberResult = SafeSpelUtil.parseStringExpression("#result", method, args, 123)
        def booleanResult = SafeSpelUtil.parseStringExpression("#result", method, args, true)
        def objectResult = SafeSpelUtil.parseStringExpression("#result.name", method, args, new TestBusinessObject("测试", 25, 123L))

        then: "应该正确转换为String类型"
        stringResult == "Hello"
        numberResult == "123"
        booleanResult == "true"
        objectResult == "测试"
    }

    def "测试String表达式解析 - null值处理"() {
        given: "准备测试参数"
        def method = String.class.getMethod("toString")
        def args = [] as Object[]

        when: "解析null值表达式"
        def nullResult = SafeSpelUtil.parseStringExpression("#result", method, args, null)

        then: "应该返回null"
        nullResult == null
    }

    // ========== 异常处理测试 ==========

    def "测试表达式解析异常处理"() {
        given: "准备测试参数"
        def method = String.class.getMethod("toString")
        def args = [] as Object[]
        def result = new TestBusinessObject("测试", 25, 123L)

        when: "解析无效的SpEL表达式"
        def invalidSyntaxResult = SafeSpelUtil.safeParseExpression("#result.nonExistentProperty", method, args, result)
        def invalidOperationResult = SafeSpelUtil.safeParseExpression("#result + #nonExistentVariable", method, args, result)

        then: "应该返回null而不是抛出异常"
        invalidSyntaxResult == null
        invalidOperationResult == null
    }

    // ========== 方法参数处理测试 ==========

    def "测试方法参数访问"() {
        given: "准备带参数的方法"
        def method = TestBusinessObject.class.getMethod("setName", String.class)
        def args = ["新名称"] as Object[]
        def result = new TestBusinessObject("原名称", 25, 123L)

        when: "解析方法参数表达式（使用参数名）"
        // 注意：Java中参数名可能不可用，这个测试可能返回null
        def paramResult = SafeSpelUtil.safeParseExpression("#name", method, args, result)

        then: "参数名可能不可用，允许返回null"
        paramResult == "新名称" || paramResult == null
    }

    def "测试多个方法参数访问"() {
        given: "准备多参数方法"
        def method = TestBusinessObject.class.getDeclaredMethod("updateInfo", String.class, Integer.class)
        def args = ["更新名称", 30] as Object[]
        def result = new TestBusinessObject("原名称", 25, 123L)

        when: "解析多个方法参数表达式"
        // 注意：Java中参数名可能不可用，这些测试可能返回null
        def param0Result = SafeSpelUtil.safeParseExpression("#name", method, args, result)
        def param1Result = SafeSpelUtil.safeParseExpression("#age", method, args, result)

        then: "参数名可能不可用，允许返回null"
        (param0Result == "更新名称" || param0Result == null) &&
        (param1Result == 30 || param1Result == null)
    }

    def "测试不安全对象过滤"() {
        given: "准备测试参数"
        def method = String.class.getMethod("toString")
        def args = [new File("/tmp")] as Object[]  // File对象应该被过滤
        def result = "test"

        when: "解析包含不安全对象的表达式"
        // 即使参数名可用，File对象也应该被过滤
        def paramResult = SafeSpelUtil.safeParseExpression("#arg0", method, args, result)

        then: "不安全对象应该被过滤，无法访问"
        paramResult == null
    }

    // ========== 边界值和特殊情况测试 ==========

    def "测试空方法和空参数处理"() {
        given: "准备空参数"
        def method = null
        def args = null
        def result = "test"

        when: "解析表达式"
        def parseResult = SafeSpelUtil.safeParseExpression("#result", method, args, result)

        then: "应该正常处理"
        parseResult == "test"
    }

    def "测试业务对象安全检查"() {
        given: "准备com.tem包下的业务对象"
        def method = String.class.getMethod("toString")
        def args = [] as Object[]
        def result = new TestBusinessObject("测试", 25, 123L)

        when: "解析业务对象表达式"
        def parseResult = SafeSpelUtil.safeParseExpression("#result.name", method, args, result)

        then: "com.tem包下的对象应该被允许"
        parseResult == "测试"
    }

    def "测试基本类型安全检查"() {
        given: "准备基本类型参数"
        def method = String.class.getMethod("toString")
        def args = [123, "test", true, 456L, 78.9d] as Object[]
        def result = "test"

        when: "解析基本类型参数表达式"
        // 注意：由于参数名可能不可用，这些测试主要验证不会抛出异常
        def intResult = SafeSpelUtil.safeParseExpression("#arg0", method, args, result)
        def stringResult = SafeSpelUtil.safeParseExpression("#arg1", method, args, result)
        def booleanResult = SafeSpelUtil.safeParseExpression("#arg2", method, args, result)
        def longResult = SafeSpelUtil.safeParseExpression("#arg3", method, args, result)
        def doubleResult = SafeSpelUtil.safeParseExpression("#arg4", method, args, result)

        then: "基本类型应该被允许（如果参数名可用）或返回null（如果参数名不可用）"
        // 由于参数名可能不可用，我们主要验证不会抛出异常
        intResult == 123 || intResult == null
        stringResult == "test" || stringResult == null
        booleanResult == true || booleanResult == null
        longResult == 456L || longResult == null
        doubleResult == 78.9d || doubleResult == null
    }


}
