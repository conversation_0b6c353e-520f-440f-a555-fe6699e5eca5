package com.tem.customer.model.convert;

import com.tem.customer.model.dto.partner.PartnerNoteDTO;
import com.tem.customer.repository.entity.PartnerNote;
import com.tem.customer.model.vo.partner.PartnerNoteVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 企业备注数据转换器
 * 使用MapStruct进行实体类、DTO、VO之间的数据转换
 *
 * <AUTHOR>
 * @since 2025-06-19
 */
@Mapper
public interface PartnerNoteConverter {

    PartnerNoteConverter INSTANCE = Mappers.getMapper(PartnerNoteConverter.class);


    /**
     * DTO转实体类
     */
    PartnerNote toEntity(PartnerNoteDTO dto);

    /**
     * 实体类转DTO
     */
    PartnerNoteDTO toDTO(PartnerNote entity);

    /**
     * 实体类转VO
     */
    PartnerNoteVO toVO(PartnerNote entity);

    /**
     * 实体类列表转VO列表
     */
    List<PartnerNoteVO> toVOList(List<PartnerNote> entities);



}
