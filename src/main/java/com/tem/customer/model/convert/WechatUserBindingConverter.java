package com.tem.customer.model.convert;

import com.tem.customer.model.dto.partner.WechatUserBindingDTO;
import com.tem.customer.repository.entity.WechatUserBinding;
import com.tem.customer.model.vo.partner.WechatUserBindingVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 微信用户绑定关系转换器
 * 使用MapStruct进行对象转换
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
@Mapper
public interface WechatUserBindingConverter {

    WechatUserBindingConverter INSTANCE = Mappers.getMapper(WechatUserBindingConverter.class);

    /**
     * DTO转Entity
     *
     * @param dto DTO对象
     * @return Entity对象
     */
    WechatUserBinding toEntity(WechatUserBindingDTO dto);

    /**
     * Entity转VO
     *
     * @param entity Entity对象
     * @return VO对象
     */
    @Mapping(target = "userName", ignore = true) // 需要在Service层填充
    @Mapping(target = "partnerName", ignore = true) // 需要在Service层填充
    @Mapping(target = "groupName", ignore = true) // 需要在Service层填充
    WechatUserBindingVO toVO(WechatUserBinding entity);

    /**
     * Entity列表转VO列表
     *
     * @param entities Entity列表
     * @return VO列表
     */
    List<WechatUserBindingVO> toVOList(List<WechatUserBinding> entities);

}
