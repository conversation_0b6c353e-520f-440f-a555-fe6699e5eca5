package com.tem.customer.model.convert;

import com.tem.customer.model.dto.partner.PartnerWechatGroupDTO;
import com.tem.customer.repository.entity.PartnerWechatGroup;
import com.tem.customer.model.vo.partner.PartnerWechatGroupVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 企业微信群绑定关系转换器
 * 使用MapStruct进行对象转换
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
@Mapper
public interface PartnerWechatGroupConverter {

    PartnerWechatGroupConverter INSTANCE = Mappers.getMapper(PartnerWechatGroupConverter.class);

    /**
     * DTO转Entity
     *
     * @param dto DTO对象
     * @return Entity对象
     */
    PartnerWechatGroup toEntity(PartnerWechatGroupDTO dto);

    /**
     * Entity转VO
     *
     * @param entity Entity对象
     * @return VO对象
     */
    @Mapping(target = "groupTypeDesc", expression = "java(getGroupTypeDesc(entity.getGroupType()))")
    @Mapping(target = "statusDesc", expression = "java(getStatusDesc(entity.getStatus()))")
    PartnerWechatGroupVO toVO(PartnerWechatGroup entity);

    /**
     * Entity列表转VO列表
     *
     * @param entities Entity列表
     * @return VO列表
     */
    List<PartnerWechatGroupVO> toVOList(List<PartnerWechatGroup> entities);

    /**
     * 获取群类型描述
     *
     * @param groupType 群类型
     * @return 群类型描述
     */
    default String getGroupTypeDesc(String groupType) {
        if (groupType == null) {
            return null;
        }
        for (PartnerWechatGroup.GroupType type : PartnerWechatGroup.GroupType.values()) {
            if (type.getCode().equals(groupType)) {
                return type.getDesc();
            }
        }
        return groupType;
    }

    /**
     * 获取状态描述
     *
     * @param status 状态
     * @return 状态描述
     */
    default String getStatusDesc(Integer status) {
        if (status == null) {
            return null;
        }
        for (PartnerWechatGroup.Status statusEnum : PartnerWechatGroup.Status.values()) {
            if (statusEnum.getCode().equals(status)) {
                return statusEnum.getDesc();
            }
        }
        return status.toString();
    }
}
