package com.tem.customer.model.from;

import com.tem.platform.api.dto.UserPartnerDto;
import com.tem.platform.api.dto.UserTagDto;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@Data
public class UserAndStandardForm implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 用户姓名
     */
    private String userName;
    /**
     * 性别 0:男 1:女
     */
    private String gender;

    private String mobile;

    private String email;
    /**
     * 是否是Vip 0：否，1：是
     */
    private Integer vipLevel;
    /**
     * 企业id
     */
    private Long partnerId;
    /**
     * 企业，名称
     */
    private String partnerName;

    /**
     * 企业备注
     */
    private String partnerRemarks;
    /**
     * 其他关联企业信息
     */
    private List<UserPartnerDto> allPartnerList;

    /**
     * 销售员姓名
     */
    private String salerName;

    /**
     * 销售经理姓名
     */
    private String managerName;
    /**
     * 企业来源渠道
     */
    private String partnerChannelName;
    /**
     * 部门全路径名称
     */
    private String orgPathNames;
    /**
     * 职级名称
     */
    private String empLevelName;


    private List<UserTagDto> tags;

    /**
     * 国内机票 因公预订开关
     */
    private boolean flightSwitch;
    /**
     * 国际机票 因公预订开关
     */
    private boolean intlFlightSwitch;
    /**
     * 国内酒店 因公预订开关
     */
    private boolean hotelSwitch;
    /**
     * 火车因公预订开关
     */
    private boolean trainSwitch;
    /**
     * 用车因公预订开关
     */
    private boolean carSwitch;
    /**
     * 通用订单 因公预订开关
     */
    private boolean generalSwitch;

    /**
     * 国内机票差标规则数据
     */
    private FlightStandardInfo flightStandardInfo;
    /**
     * 国际机票差标规则数据
     */
    private IntlFlightStandardInfo intlFlightStandardInfo;
    /**
     * 酒店差标金额明细数据
     */
    private HotelStandardInfo hotelStandardInfo;
    /**
     * 火车差标规则数据
     */
    private TrainStandardInfo trainStandardInfo;
    /**
     * 用车差标规则数据
     */
    private CarStandardInfo carStandardInfo;

    /**
     * 酒店差标金额明细数据
     */
    @Data
    public static class HotelStandardItemInfo implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;
        /**
         * 月份：1月……  或者  全年
         */
        private String month;
        /**
         * 普通酒店差标金额
         */
        private Double money;
        /**
         * 协议酒店差标金额
         */
        private Double agreementMoney;
    }

    /**
     * 国内机票差标规则数据
     */
    @Data
    public static class FlightStandardInfo implements Serializable {

        /**
         *
         */
        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 可预订舱位
         */
        private String canBookingCabin;

        /**
         * 经济舱限制
         * NON,不限制;  2,二折  ...... ; 10,十折
         */
        private String econLimit;
        /**
         * 公务舱限制
         * NON,不限制;  2,二折  ...... ; 10,十折
         */
        private String busiLimit;
        /**
         * 头等舱限制
         * NON,不限制;  2,二折  ...... ; 10,十折
         */
        private String firLimit;

        /**
         * 是否允许公司账户支付 true:允许公司账户支付
         */
        private Boolean companyPayAuth;
        /**
         * 是否需要审批 true:需要审批
         */
        private Boolean needApproAuth;

        /**
         * 是否允许超标自付 true:允许超标自付
         */
        private Boolean overStandardSelfpay;
    }

    /**
     * 国际机票差标规则数据
     */
    @Data
    public static class IntlFlightStandardInfo implements Serializable {
        @Serial
        private static final long serialVersionUID = 1L;
        /**
         * 可预订舱位
         */
        private String canBookingCabin;

        /**
         * 是否允许公司账户支付 true:允许公司账户支付
         */
        private Boolean companyPayAuth;
        /**
         * 是否需要审批 true:需要审批
         */
        private Boolean needApproAuth;

        /**
         * 是否允许超标自付 true:允许超标自付
         */
        private Boolean overStandardSelfpay;
    }

    /**
     * 酒店差标规则数据
     */
    @Data
    public static class HotelStandardInfo implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 是否允许公司账户支付 true:允许公司账户支付
         */
        private Boolean companyPayAuth;
        /**
         * 是否需要审批 true:需要审批
         */
        private Boolean needApproAuth;

        /**
         * 是否允许超标自付 true:允许超标自付
         */
        private Boolean overStandardSelfpay;

    }

    /**
     * 火车差标规则数据
     */
    @Data
    public static class TrainStandardInfo implements Serializable {
        @Serial
        private static final long serialVersionUID = -2997205193136843860L;
        /**
         * Z-直达/T-特快/K-快速/其他
         * NON,不允许 ; HT,硬座 ; HS,硬卧 ; ST,软座; SS,软卧;  HSS,高级软卧
         */
        private String trainZtko;

        /**
         * 动车
         * NON,不允许; SECS,二等座 ; FIRS,一等座 ; SS,软卧;  HSS,高级软卧
         */
        private String trainD;

        /**
         * G-高铁 NON,
         * NON,不允许 ; SECS,二等座;  FIRS,一等座;  SPES,特等座 ; BUSS,商务座
         */
        private String trainG;

        /**
         * C-城际
         * NON,不允许;  SECS,二等座;  FIRS,一等座 ; SPES,特等座;  BUSS,商务座
         */
        private String trainC;

        /**
         * 是否允许公司账户支付 true:允许公司账户支付
         */
        private Boolean companyPayAuth;
        /**
         * 是否需要审批 true:需要审批
         */
        private Boolean needApproAuth;

        /**
         * 是否允许超标自付 true:允许超标自付
         */
        private Boolean overStandardSelfpay;
    }

    /**
     * 用车差标规则数据
     */
    @Data
    public static class CarStandardInfo implements Serializable {
        @Serial
        private static final long serialVersionUID = -7847072473208156872L;
        /**
         * 无差旅标准
         */
        private String carStandardtext;
        /**
         * 是否允许公司账户支付 true:允许公司账户支付
         */
        private Boolean companyPayAuth;
    }
}
