package com.tem.customer.model.vo.partner;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 企业微信群绑定关系响应VO
 * 用于返回给前端的数据
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
@Data
@Accessors(chain = true)
public class PartnerWechatGroupVO {

    /**
     * 记录ID
     */
    private Long id;

    /**
     * 企业ID
     */
    private Long partnerId;

    /**
     * 企业名称
     */
    private String partnerName;

    /**
     * 企业微信群ID，企业微信群的唯一标识
     */
    private String chatId;

    /**
     * 群名称，用于管理界面展示
     */
    private String groupName;

    /**
     * 群类型：CUSTOMER_SERVICE-客服群
     */
    private String groupType;

    /**
     * 群类型描述
     */
    private String groupTypeDesc;

    /**
     * 状态：1-启用，0-禁用
     */
    private Integer status;

    /**
     * 状态描述
     */
    private String statusDesc;

    /**
     * 排序字段，数值越小越靠前
     */
    private Integer sortOrder;

    /**
     * 备注说明
     */
    private String remark;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新人
     */
    private String updateBy;
}
