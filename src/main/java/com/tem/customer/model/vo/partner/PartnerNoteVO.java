package com.tem.customer.model.vo.partner;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 企业备注响应VO
 * 用于返回给前端的数据
 *
 * <AUTHOR>
 * @since 2025-06-19
 */
@Data
@Accessors(chain = true)
public class PartnerNoteVO {

    /**
     * 备注ID
     */
    private Long id;

    /**
     * 企业ID
     */
    private Long partnerId;

    /**
     * 备注标题，最大15个字符
     */
    private String title;

    /**
     * 备注内容，支持Markdown格式
     */
    private String content;

    /**
     * 排序字段，数值越小越靠前
     */
    private Integer sortOrder;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新人
     */
    private String updateBy;

}
