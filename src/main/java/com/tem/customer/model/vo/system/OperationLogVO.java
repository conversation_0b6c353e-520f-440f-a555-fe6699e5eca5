package com.tem.customer.model.vo.system;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 操作记录日志响应VO
 * 用于返回给前端的数据
 *
 * <AUTHOR>
 * @since 2025-06-23
 */
@Data
@Accessors(chain = true)
public class OperationLogVO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 业务类型代码
     */
    private String businessType;

    /**
     * 业务类型描述
     */
    private String businessTypeDesc;

    /**
     * 业务数据ID
     */
    private Long businessId;

    /**
     * 操作类型代码
     */
    private String operationType;

    /**
     * 操作类型描述
     */
    private String operationTypeDesc;

    /**
     * 操作描述
     */
    private String operationDesc;

    /**
     * 操作人ID
     */
    private Long operatorId;

    /**
     * 操作人姓名
     */
    private String operatorName;

    /**
     * 操作人用户名
     */
    private String operatorUsername;

    /**
     * 操作人所属企业ID
     */
    private Long partnerId;

    /**
     * 目标企业ID（被操作的企业备注所属企业）
     */
    private Long targetPartnerId;

    /**
     * 操作IP地址
     */
    private String ipAddress;

    /**
     * 用户代理信息
     */
    private String userAgent;

    /**
     * 请求URI
     */
    private String requestUri;

    /**
     * 请求方法
     */
    private String requestMethod;

    /**
     * 执行耗时（毫秒）
     */
    private Integer executionTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 格式化的创建时间
     */
    private String formattedCreateTime;

    /**
     * 执行时间描述
     */
    private String executionTimeDesc;

    /**
     * 操作结果描述
     */
    private String operationResultDesc;

    /**
     * 是否执行成功
     */
    private Boolean success;

    /**
     * 获取格式化的创建时间
     */
    public String getFormattedCreateTime() {
        if (createTime != null) {
            return createTime.toString().replace("T", " ");
        }
        return "";
    }

    /**
     * 获取执行时间描述
     */
    public String getExecutionTimeDesc() {
        if (executionTime == null) {
            return "未知";
        }
        
        if (executionTime < 1000) {
            return executionTime + "ms";
        } else if (executionTime < 60000) {
            return String.format("%.2fs", executionTime / 1000.0);
        } else {
            int minutes = executionTime / 60000;
            int seconds = (executionTime % 60000) / 1000;
            return String.format("%dm%ds", minutes, seconds);
        }
    }

    /**
     * 获取操作结果描述
     */
    public String getOperationResultDesc() {
        if (operationDesc != null && operationDesc.contains("执行失败")) {
            return "失败";
        }
        return "成功";
    }

    /**
     * 是否执行成功
     */
    public Boolean getSuccess() {
        return operationDesc == null || !operationDesc.contains("执行失败");
    }
}
