package com.tem.customer.model.vo;

import lombok.Data;

/**
 * 验证码响应VO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class CaptchaVO {

    /**
     * 验证码key
     */
    private String captchaKey;

    /**
     * 验证码图片（base64编码或URL）
     */
    private String captchaImage;

    /**
     * 过期时间（秒）
     */
    private Integer expireTime;

    /**
     * 生成时间戳
     */
    private Long timestamp = System.currentTimeMillis();
}
