package com.tem.customer.model.vo.common;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 用户订单信息VO
 *
 * <AUTHOR>
 * @since 2025-07-10
 */
@Data
public class UserOrderInfoVO {

    /**
     * 订单ID
     */
    private Long id;

    /**
     * 业务类型，10-机票/11-酒店/12-火车/13-国际酒店/14-需求单/15-保险/16-用车/17-国际机票/18-国际火车/19-国际用车等(来源于订单号前两位)
     */
    private String bizType;

    /**
     * 客户应付订单总金额
     */
    private BigDecimal totalAmount;

    /**
     * 业务系统显示状态，来源于业务系统
     */
    private String orderShowStatus;

    /**
     * 酒店名称
     */
    private String hotelName;

    /**
     * 酒店地址
     */
    private String hotelAddress;

    /**
     * 行程
     */
    private String trip;

    /**
     * 保险产品名称
     */
    private String insuranceName;

    /**
     * 服务品类名称
     */
    private String serviceName;

    /**
     * 行程开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date travelStartTime;

    /**
     * 乘客用户姓名
     */
    private String userName;

    /**
     * 入住天数
     */
    private Integer stayDays;
}
