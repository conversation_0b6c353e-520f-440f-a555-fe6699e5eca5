package com.tem.customer.model.dto.qiyu;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

/**
 * 七鱼微信生态获取用户信息请求对象
 *
 * <p>根据七鱼文档，请求参数包含：</p>
 * <ul>
 *   <li>appid: 企业分配给网易七鱼系统的 appid</li>
 *   <li>openid: 微信三方id</li>
 *   <li>sourceappid: 主体id</li>
 *   <li>unionid: 企微客服(fromType为wx_cs)渠道值为密文external_user_id，其他渠道为微信unionId</li>
 *   <li>fromType: wx_cs：企微客服渠道,其他为微信生态</li>
 *   <li>wxworkUserId: 企微助手id</li>
 *   <li>wxworkUserName: 企微助手名字</li>
 * </ul>
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class QiyuWechatUserInfoRequest extends QiyuCrmRequest {

    /**
     * 企微客服渠道标识
     */
    public static final String FROM_TYPE_WX_CS = "wx_cs";

    /**
     * 微信三方id
     */
    private String openid;

    /**
     * 主体id
     */
    private String sourceappid;

    /**
     * 企微客服(fromType为wx_cs)渠道值为密文external_user_id，其他渠道为微信unionId
     */
    private String unionid;

    /**
     * 来源类型：wx_cs：企微客服渠道,其他为微信生态
     */
    private String fromType;

    /**
     * 企微助手id
     */
    private String wxworkUserId;

    /**
     * 企微助手名字
     */
    private String wxworkUserName;

    /**
     * 验证微信生态获取用户信息的必要参数
     *
     * @return 验证结果
     */
    public boolean validateWechatUserInfoParams() {
        // 必须有unionid和fromType
        if (!StringUtils.hasText(unionid) || !StringUtils.hasText(fromType)) {
            return false;
        }

        // 如果是企微客服渠道，建议有wxworkUserId和wxworkUserName
        if (isWxCsChannel()) {
            // 企微客服渠道的基本验证通过即可，wxworkUserId和wxworkUserName为可选
            return true;
        }

        // 其他微信生态渠道的基本验证
        return true;
    }

    /**
     * 判断是否为企微客服渠道
     *
     * @return true-企微客服渠道，false-其他微信生态渠道
     */
    public boolean isWxCsChannel() {
        return FROM_TYPE_WX_CS.equals(fromType);
    }
}
