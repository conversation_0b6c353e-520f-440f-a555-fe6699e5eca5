package com.tem.customer.model.dto.wechat;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 企业微信API响应基础类
 *
 * <AUTHOR>
 * @since 2025-07-31
 */
@Data
public class WechatApiResponse {

    /**
     * 返回码
     */
    @JsonProperty("errcode")
    private Integer errCode;

    /**
     * 返回码说明
     */
    @JsonProperty("errmsg")
    private String errMsg;

    /**
     * 判断是否成功
     *
     * @return 是否成功
     */
    public boolean isSuccess() {
        return errCode != null && errCode == 0;
    }

    /**
     * 获取错误信息
     *
     * @return 错误信息
     */
    public String getErrorMessage() {
        if (isSuccess()) {
            return null;
        }
        return String.format("错误码: %d, 错误信息: %s", errCode, errMsg);
    }
}
