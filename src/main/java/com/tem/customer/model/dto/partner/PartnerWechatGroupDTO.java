package com.tem.customer.model.dto.partner;

import lombok.Data;
import lombok.experimental.Accessors;

import jakarta.validation.constraints.*;

/**
 * 企业微信群绑定关系请求DTO
 * 用于接收前端请求参数
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
@Data
@Accessors(chain = true)
public class PartnerWechatGroupDTO {

    /**
     * 记录ID（更新时必填）
     */
    private Long id;

    /**
     * 企业ID
     */
    @NotNull(message = "企业ID不能为空")
    private Long partnerId;

    /**
     * 企业微信群ID，企业微信群的唯一标识
     */
    @NotBlank(message = "企业微信群ID不能为空")
    @Size(max = 64, message = "企业微信群ID长度不能超过64个字符")
    private String chatId;

    /**
     * 群名称，用于管理界面展示
     */
//    @NotBlank(message = "群名称不能为空")
    @Size(max = 100, message = "群名称长度不能超过100个字符")
    private String groupName;

    /**
     * 群类型：CUSTOMER_SERVICE-客服群
     */
    @Size(max = 20, message = "群类型长度不能超过20个字符")
    private String groupType;

    /**
     * 状态：1-启用，0-禁用
     */
    @Min(value = 0, message = "状态值必须为0或1")
    @Max(value = 1, message = "状态值必须为0或1")
    private Integer status;

    /**
     * 排序字段，数值越小越靠前
     */
    @Min(value = 0, message = "排序值不能为负数")
    private Integer sortOrder;

    /**
     * 备注说明
     */
    @Size(max = 500, message = "备注长度不能超过500个字符")
    private String remark;
}
