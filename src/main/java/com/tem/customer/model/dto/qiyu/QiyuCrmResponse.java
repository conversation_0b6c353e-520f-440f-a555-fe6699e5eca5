package com.tem.customer.model.dto.qiyu;

import lombok.Data;

/**
 * 七鱼CRM响应基础对象
 * 
 * <AUTHOR>
 * @since 2025-07-03
 */
@Data
public class QiyuCrmResponse {

    /**
     * 表明接口调用是否成功。值为0时表示接口调用成功
     */
    private Integer rlt;

    /**
     * 错误消息
     */
    private String message;

    /**
     * 创建成功响应
     * 
     * @return 成功响应对象
     */
    public static QiyuCrmResponse success() {
        QiyuCrmResponse response = new QiyuCrmResponse();
        response.setRlt(0);
        return response;
    }

    /**
     * 创建成功响应
     * 
     * @param message 成功消息
     * @return 成功响应对象
     */
    public static QiyuCrmResponse success(String message) {
        QiyuCrmResponse response = new QiyuCrmResponse();
        response.setRlt(0);
        response.setMessage(message);
        return response;
    }

    /**
     * 创建失败响应
     * 
     * @param code 错误码
     * @param message 错误消息
     * @return 失败响应对象
     */
    public static QiyuCrmResponse error(Integer code, String message) {
        QiyuCrmResponse response = new QiyuCrmResponse();
        response.setRlt(code);
        response.setMessage(message);
        return response;
    }

    /**
     * 创建Token失效响应
     * 
     * @return Token失效响应对象
     */
    public static QiyuCrmResponse tokenExpired() {
        QiyuCrmResponse response = new QiyuCrmResponse();
        response.setRlt(2);
        response.setMessage("Token expired");
        return response;
    }

    /**
     * 创建参数错误响应
     * 
     * @param message 错误消息
     * @return 参数错误响应对象
     */
    public static QiyuCrmResponse paramError(String message) {
        QiyuCrmResponse response = new QiyuCrmResponse();
        response.setRlt(1);
        response.setMessage(message);
        return response;
    }

    /**
     * 创建系统错误响应
     * 
     * @param message 错误消息
     * @return 系统错误响应对象
     */
    public static QiyuCrmResponse systemError(String message) {
        QiyuCrmResponse response = new QiyuCrmResponse();
        response.setRlt(500);
        response.setMessage(message);
        return response;
    }
}
