package com.tem.customer.model.dto.qiyu;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 七鱼微信生态获取用户信息响应对象
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class QiyuWechatUserInfoResponse extends QiyuCrmResponse {

    /**
     * 用户唯一id，用于将服务记录打通
     * uid没有就没有，不要使用unionId来填充
     */
    private String uid;

    /**
     * 用户姓名
     */
    private String name;

    /**
     * 用户邮箱
     */
    private String email;

    /**
     * 用户手机号
     */
    private String mobile;

    /**
     * 用户级别
     */
    private Integer level;

    /**
     * 用一个数组表示用户详细信息的数据
     */
    private List<QiyuDataItem> data;

    /**
     * 创建成功的微信用户信息响应
     *
     * @param uid 用户唯一标识
     * @param name 用户姓名
     * @param email 用户邮箱
     * @param mobile 用户手机号
     * @param level 用户级别
     * @param data 用户数据
     * @return 微信用户信息响应对象
     */
    public static QiyuWechatUserInfoResponse success(String uid, String name, String email, String mobile, Integer level, List<QiyuDataItem> data) {
        QiyuWechatUserInfoResponse response = new QiyuWechatUserInfoResponse();
        response.setRlt(0);
        response.setUid(uid);
        response.setName(name);
        response.setEmail(email);
        response.setMobile(mobile);
        response.setLevel(level);
        response.setData(data);
        return response;
    }

    /**
     * 创建未绑定用户的微信用户信息响应
     *
     * @param data 用户数据
     * @return 微信用户信息响应对象
     */
    public static QiyuWechatUserInfoResponse successUnbound(List<QiyuDataItem> data) {
        QiyuWechatUserInfoResponse response = new QiyuWechatUserInfoResponse();
        response.setRlt(0);
        // uid没有就没有，不要使用unionId来填充
        response.setUid(null);
        response.setName(null);
        response.setEmail(null);
        response.setMobile(null);
        response.setLevel(null);
        response.setData(data);
        return response;
    }

    /**
     * 创建参数错误响应
     *
     * @param message 错误消息
     * @return 参数错误响应对象
     */
    public static QiyuWechatUserInfoResponse paramError(String message) {
        QiyuWechatUserInfoResponse response = new QiyuWechatUserInfoResponse();
        response.setRlt(1);
        response.setMessage(message);
        response.setUid(null);
        response.setName(null);
        response.setEmail(null);
        response.setMobile(null);
        response.setLevel(null);
        response.setData(null);
        return response;
    }

    /**
     * 创建系统错误响应
     *
     * @param message 错误消息
     * @return 系统错误响应对象
     */
    public static QiyuWechatUserInfoResponse systemError(String message) {
        QiyuWechatUserInfoResponse response = new QiyuWechatUserInfoResponse();
        response.setRlt(500);
        response.setMessage(message);
        response.setUid(null);
        response.setName(null);
        response.setEmail(null);
        response.setMobile(null);
        response.setLevel(null);
        response.setData(null);
        return response;
    }
}
