package com.tem.customer.model.dto.wechat;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 企业微信获取客户详情请求DTO
 *
 * <AUTHOR>
 * @since 2025-07-31
 */
@Data
public class WechatCustomerDetailRequest {

    /**
     * 企业客户的external_userid
     */
    @JsonProperty("external_userid")
    private String externalUserId;

    public WechatCustomerDetailRequest() {
    }

    public WechatCustomerDetailRequest(String externalUserId) {
        this.externalUserId = externalUserId;
    }
}