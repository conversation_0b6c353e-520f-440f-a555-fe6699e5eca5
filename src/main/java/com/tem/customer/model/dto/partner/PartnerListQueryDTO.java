package com.tem.customer.model.dto.partner;

import lombok.Data;
import lombok.experimental.Accessors;

import jakarta.validation.constraints.Min;

/**
 * 企业列表查询请求DTO
 * 用于封装企业列表查询的参数
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
@Accessors(chain = true)
public class PartnerListQueryDTO {

    /**
     * 页码（从1开始）
     */
    @Min(value = 1, message = "页码必须大于0")
    private Integer pageNo;

    /**
     * 页大小
     */
    @Min(value = 1, message = "页大小必须大于0")
    private Integer pageSize;

    /**
     * 搜索关键字（企业名称或编码）
     */
    private String key;

    /**
     * 企业类型：0-企业，1-TMC，2-渠道，null-全部
     */
    private Integer partnerType;

    /**
     * TMC ID（用于权限控制）
     */
    private Long tmcId;

    /**
     * 获取计算后的起始位置
     * 
     * @return 起始位置（从0开始）
     */
    public Integer getStart() {
        if (pageNo == null || pageSize == null) {
            return 0;
        }
        return (pageNo - 1) * pageSize;
    }

    /**
     * 获取默认页大小
     * 
     * @return 页大小，默认10
     */
    public Integer getPageSizeWithDefault() {
        return pageSize != null ? pageSize : 10;
    }

    /**
     * 检查是否有搜索关键字
     * 
     * @return 如果有关键字返回true
     */
    public boolean hasSearchKey() {
        return key != null && !key.trim().isEmpty();
    }
}
