package com.tem.customer.model.dto.qiyu;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 七鱼企微客服群聊获取群信息响应对象
 * 
 * <AUTHOR>
 * @since 2025-07-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class QiyuGroupInfoResponse extends QiyuCrmResponse {

    /**
     * 用户唯一id，用于将服务记录打通（可选）
     */
    private String uid;

    /**
     * 级别
     */
    private Integer level;

    /**
     * 用一个数组表示群详细信息的数据
     */
    private List<QiyuDataItem> data;

    /**
     * 创建成功的群信息响应
     * 
     * @param level 级别
     * @param data 群数据
     * @return 群信息响应对象
     */
    public static QiyuGroupInfoResponse success(Integer level, List<QiyuDataItem> data) {
        QiyuGroupInfoResponse response = new QiyuGroupInfoResponse();
        response.setRlt(0);
        response.setLevel(level);
        response.setData(data);
        return response;
    }

    /**
     * 创建带用户ID的群信息响应
     *
     * @param uid 用户唯一标识
     * @param level 级别
     * @param data 群数据
     * @return 群信息响应对象
     */
    public static QiyuGroupInfoResponse success(String uid, Integer level, List<QiyuDataItem> data) {
        QiyuGroupInfoResponse response = new QiyuGroupInfoResponse();
        response.setRlt(0);
        response.setUid(uid);
        response.setLevel(level);
        response.setData(data);
        return response;
    }

    /**
     * 创建参数错误响应
     *
     * @param message 错误消息
     * @return 参数错误响应对象
     */
    public static QiyuGroupInfoResponse paramError(String message) {
        QiyuGroupInfoResponse response = new QiyuGroupInfoResponse();
        response.setRlt(1);
        response.setMessage(message);
        response.setUid(null);
        response.setLevel(null);
        response.setData(null);
        return response;
    }

    /**
     * 创建系统错误响应
     *
     * @param message 错误消息
     * @return 系统错误响应对象
     */
    public static QiyuGroupInfoResponse systemError(String message) {
        QiyuGroupInfoResponse response = new QiyuGroupInfoResponse();
        response.setRlt(500);
        response.setMessage(message);
        response.setUid(null);
        response.setLevel(null);
        response.setData(null);
        return response;
    }
}
