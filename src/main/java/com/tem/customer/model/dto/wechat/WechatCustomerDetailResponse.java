package com.tem.customer.model.dto.wechat;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 企业微信获取客户详情响应DTO
 *
 * <AUTHOR>
 * @since 2025-07-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WechatCustomerDetailResponse extends WechatApiResponse {

    /**
     * 外部联系人详情
     */
    @JsonProperty("external_contact")
    private ExternalContact externalContact;

    /**
     * 外部联系人详情
     */
    @Data
    public static class ExternalContact {
        /**
         * 外部联系人的userid
         */
        @JsonProperty("external_userid")
        private String externalUserId;

        /**
         * 外部联系人的名称
         */
        @JsonProperty("name")
        private String name;

        /**
         * 外部联系人的头像
         */
        @JsonProperty("avatar")
        private String avatar;

        /**
         * 外部联系人的类型
         * 1-微信用户
         * 2-企业微信用户
         */
        @JsonProperty("type")
        private Integer type;

        /**
         * 外部联系人的性别
         * 0-未知
         * 1-男性
         * 2-女性
         */
        @JsonProperty("gender")
        private Integer gender;

        /**
         * 外部联系人的unionid
         */
        @JsonProperty("unionid")
        private String unionId;

        /**
         * 外部联系人的备注
         */
        @JsonProperty("remark")
        private String remark;

        /**
         * 外部联系人的描述
         */
        @JsonProperty("description")
        private String description;

        /**
         * 外部联系人的公司名称
         */
        @JsonProperty("corp_name")
        private String corpName;

        /**
         * 外部联系人的职位
         */
        @JsonProperty("position")
        private String position;

        /**
         * 添加该外部联系人的企业成员userid
         */
        @JsonProperty("userid")
        private String userId;

        /**
         * 添加该外部联系人的时间
         */
        @JsonProperty("add_time")
        private Long addTime;

        /**
         * 该外部联系人被打标签的时间
         */
        @JsonProperty("mark_time")
        private Long markTime;
    }
}