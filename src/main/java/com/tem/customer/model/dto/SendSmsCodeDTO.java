package com.tem.customer.model.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

/**
 * 发送短信验证码请求DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class SendSmsCodeDTO {

    /**
     * 手机号
     */
    @NotBlank(message = "手机号不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String mobile;

    /**
     * 图形验证码
     */
    @NotBlank(message = "图形验证码不能为空")
    private String captcha;

    /**
     * 图形验证码key
     */
    @NotBlank(message = "图形验证码key不能为空")
    private String captchaKey;
}
