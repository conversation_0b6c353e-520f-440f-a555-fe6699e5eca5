package com.tem.customer.model.dto.qiyu;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 七鱼获取Token响应对象
 * 
 * <AUTHOR>
 * @since 2025-07-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class QiyuTokenResponse extends QiyuCrmResponse {

    /**
     * 后续接口调用时使用的有效 token
     */
    private String token;

    /**
     * 表示此token离过期剩余的毫秒数（非过期时间戳）
     * 若不指定、或小于等于零，则默认为 2 小时
     */
    private Long expires;

    /**
     * 创建成功的Token响应
     * 
     * @param token 访问令牌
     * @param expires 过期时间（毫秒）
     * @return Token响应对象
     */
    public static QiyuTokenResponse success(String token, Long expires) {
        QiyuTokenResponse response = new QiyuTokenResponse();
        response.setRlt(0);
        response.setToken(token);
        response.setExpires(expires);
        return response;
    }

    /**
     * 创建默认过期时间的Token响应
     *
     * @param token 访问令牌
     * @return Token响应对象
     */
    public static QiyuTokenResponse success(String token) {
        return success(token, 2 * 60 * 60 * 1000L); // 默认2小时
    }

    /**
     * 创建失败响应
     *
     * @param code 错误码
     * @param message 错误消息
     * @return 失败响应对象
     */
    public static QiyuTokenResponse error(Integer code, String message) {
        QiyuTokenResponse response = new QiyuTokenResponse();
        response.setRlt(code);
        response.setMessage(message);
        return response;
    }

    /**
     * 创建系统错误响应
     *
     * @param message 错误消息
     * @return 系统错误响应对象
     */
    public static QiyuTokenResponse systemError(String message) {
        QiyuTokenResponse response = new QiyuTokenResponse();
        response.setRlt(500);
        response.setMessage(message);
        return response;
    }
}
