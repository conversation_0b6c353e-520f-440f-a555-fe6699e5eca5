package com.tem.customer.repository.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serial;

/**
 * 企业备注实体类
 * 用于存储企业的备注信息，支持多条备注，每条包含标题和Markdown格式的详情内容
 *
 * <AUTHOR>
 * @since 2025-06-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_partner_note")
public class PartnerNote extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 企业ID
     */
    @TableField("partner_id")
    private Long partnerId;

    /**
     * 备注标题，最大15个字符
     */
    @TableField("title")
    private String title;

    /**
     * 备注内容，支持Markdown格式
     */
    @TableField("content")
    private String content;

    /**
     * 排序字段，数值越小越靠前
     */
    @TableField("sort_order")
    private Integer sortOrder;
}
