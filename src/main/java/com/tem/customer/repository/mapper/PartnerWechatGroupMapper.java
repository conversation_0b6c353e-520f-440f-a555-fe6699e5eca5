package com.tem.customer.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tem.customer.repository.entity.PartnerWechatGroup;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 企业微信群绑定关系Mapper接口
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
@Mapper
public interface PartnerWechatGroupMapper extends BaseMapper<PartnerWechatGroup> {

    /**
     * 根据企业ID查询微信群列表，按排序字段升序排列
     *
     * @param partnerId 企业ID
     * @return 微信群列表
     */
    List<PartnerWechatGroup> selectByPartnerIdOrderBySort(@Param("partnerId") Long partnerId);

    /**
     * 根据企业ID查询启用状态的微信群列表，按排序字段升序排列
     *
     * @param partnerId 企业ID
     * @return 启用状态的微信群列表
     */
    List<PartnerWechatGroup> selectEnabledByPartnerIdOrderBySort(@Param("partnerId") Long partnerId);

    /**
     * 根据企业ID统计微信群数量
     *
     * @param partnerId 企业ID
     * @return 微信群数量
     */
    int countByPartnerId(@Param("partnerId") Long partnerId);

    /**
     * 根据企业ID统计启用状态的微信群数量
     *
     * @param partnerId 企业ID
     * @return 启用状态的微信群数量
     */
    int countEnabledByPartnerId(@Param("partnerId") Long partnerId);

    /**
     * 根据企业ID获取微信群的最大排序值
     *
     * @param partnerId 企业ID
     * @return 最大排序值
     */
    Integer getMaxSortOrderByPartnerId(@Param("partnerId") Long partnerId);

    /**
     * 根据chatId查询微信群信息
     *
     * @param chatId 企业微信群ID
     * @return 微信群信息
     */
    PartnerWechatGroup selectByChatId(@Param("chatId") String chatId);

    /**
     * 检查chatId是否已存在（排除指定ID）
     *
     * @param chatId 企业微信群ID
     * @param excludeId 排除的记录ID
     * @return 是否存在
     */
    boolean existsByChatIdExcludeId(@Param("chatId") String chatId, @Param("excludeId") Long excludeId);

    /**
     * 物理删除企业微信群绑定关系
     *
     * @param id 记录ID
     * @return 删除影响的行数
     */
    int deleteByIdPhysically(@Param("id") Long id);
}
