package com.tem.customer.shared.converter;

import com.tem.customer.model.dto.wechat.WechatGroupDetailResponse;
import com.tem.customer.model.vo.wechat.WechatGroupDetailVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;

/**
 * 企业微信群详情转换器
 *
 * <AUTHOR>
 * @since 2025-07-31
 */
@Mapper
public interface WechatGroupDetailConverter {

    WechatGroupDetailConverter INSTANCE = Mappers.getMapper(WechatGroupDetailConverter.class);

    /**
     * 转换群详情响应为VO
     *
     * @param response 群详情响应
     * @return 群详情VO
     */
    @Mapping(source = "groupChat.chatId", target = "chatId")
    @Mapping(source = "groupChat.name", target = "name")
    @Mapping(source = "groupChat.owner", target = "owner")
    @Mapping(source = "groupChat.createTime", target = "createTime", qualifiedByName = "timestampToLocalDateTime")
    @Mapping(source = "groupChat.notice", target = "notice")
    @Mapping(source = "groupChat.memberList", target = "memberList")
    @Mapping(source = "groupChat.adminList", target = "adminList")
    @Mapping(source = "groupChat.memberVersion", target = "memberVersion")
    @Mapping(expression = "java(response.getGroupChat() != null && response.getGroupChat().getMemberList() != null ? response.getGroupChat().getMemberList().size() : 0)", target = "memberCount")
    @Mapping(expression = "java(response.getGroupChat() != null && response.getGroupChat().getAdminList() != null ? response.getGroupChat().getAdminList().size() : 0)", target = "adminCount")
    WechatGroupDetailVO toVO(WechatGroupDetailResponse response);

    /**
     * 转换群成员信息
     *
     * @param member 群成员信息
     * @return 群成员VO
     */
    @Mapping(source = "userId", target = "userId")
    @Mapping(source = "type", target = "type")
    @Mapping(source = "type", target = "typeDesc", qualifiedByName = "memberTypeToDesc")
    @Mapping(source = "unionId", target = "unionId")
    @Mapping(source = "joinTime", target = "joinTime", qualifiedByName = "timestampToLocalDateTime")
    @Mapping(source = "joinScene", target = "joinScene")
    @Mapping(source = "joinScene", target = "joinSceneDesc", qualifiedByName = "joinSceneToDesc")
    @Mapping(source = "invitor.userId", target = "invitorUserId")
    @Mapping(source = "groupNickname", target = "groupNickname")
    @Mapping(source = "name", target = "name")
    WechatGroupDetailVO.MemberVO toMemberVO(WechatGroupDetailResponse.Member member);

    /**
     * 转换群管理员信息
     *
     * @param admin 群管理员信息
     * @return 群管理员VO
     */
    WechatGroupDetailVO.AdminVO toAdminVO(WechatGroupDetailResponse.Admin admin);

    /**
     * 转换群成员列表
     *
     * @param memberList 群成员列表
     * @return 群成员VO列表
     */
    List<WechatGroupDetailVO.MemberVO> toMemberVOList(List<WechatGroupDetailResponse.Member> memberList);

    /**
     * 转换群管理员列表
     *
     * @param adminList 群管理员列表
     * @return 群管理员VO列表
     */
    List<WechatGroupDetailVO.AdminVO> toAdminVOList(List<WechatGroupDetailResponse.Admin> adminList);

    /**
     * 时间戳转LocalDateTime
     *
     * @param timestamp 时间戳（秒）
     * @return LocalDateTime
     */
    @Named("timestampToLocalDateTime")
    default LocalDateTime timestampToLocalDateTime(Long timestamp) {
        if (timestamp == null) {
            return null;
        }
        return LocalDateTime.ofInstant(Instant.ofEpochSecond(timestamp), ZoneId.systemDefault());
    }

    /**
     * 成员类型转描述
     *
     * @param type 成员类型
     * @return 类型描述
     */
    @Named("memberTypeToDesc")
    default String memberTypeToDesc(Integer type) {
        if (type == null) {
            return "未知";
        }
        return switch (type) {
            case 1 -> "企业成员";
            case 2 -> "外部联系人";
            default -> "未知";
        };
    }

    /**
     * 入群方式转描述
     *
     * @param joinScene 入群方式
     * @return 入群方式描述
     */
    @Named("joinSceneToDesc")
    default String joinSceneToDesc(Integer joinScene) {
        if (joinScene == null) {
            return "未知";
        }
        return switch (joinScene) {
            case 1 -> "直接邀请入群";
            case 2 -> "邀请链接入群";
            case 3 -> "扫描群二维码入群";
            default -> "未知";
        };
    }
}
