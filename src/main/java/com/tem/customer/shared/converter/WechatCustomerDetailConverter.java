package com.tem.customer.shared.converter;

import com.tem.customer.model.dto.wechat.WechatCustomerDetailResponse;
import com.tem.customer.model.vo.wechat.WechatCustomerDetailVO;
import org.springframework.stereotype.Component;

/**
 * 企业微信客户详情转换器
 *
 * <AUTHOR>
 * @since 2025-07-31
 */
@Component
public class WechatCustomerDetailConverter {

    /**
     * 将DTO转换为VO
     *
     * @param response 客户详情响应
     * @return 客户详情VO
     */
    public WechatCustomerDetailVO convertToVO(WechatCustomerDetailResponse response) {
        if (response == null || response.getExternalContact() == null) {
            return null;
        }

        WechatCustomerDetailResponse.ExternalContact contact = response.getExternalContact();
        WechatCustomerDetailVO vo = new WechatCustomerDetailVO();

        vo.setExternalUserId(contact.getExternalUserId());
        vo.setName(contact.getName());
        vo.setAvatar(contact.getAvatar());
        vo.setType(contact.getType());
        vo.setGender(contact.getGender());
        vo.setUnionId(contact.getUnionId());
        vo.setRemark(contact.getRemark());
        vo.setDescription(contact.getDescription());
        vo.setCorpName(contact.getCorpName());
        vo.setPosition(contact.getPosition());
        vo.setUserId(contact.getUserId());
        vo.setAddTime(contact.getAddTime());
        vo.setMarkTime(contact.getMarkTime());

        return vo;
    }
}