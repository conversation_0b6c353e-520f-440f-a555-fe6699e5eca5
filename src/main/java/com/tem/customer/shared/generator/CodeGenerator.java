package com.tem.customer.shared.generator;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.generator.FastAutoGenerator;
import com.baomidou.mybatisplus.generator.config.OutputFile;
import com.baomidou.mybatisplus.generator.config.rules.DateType;
import com.baomidou.mybatisplus.generator.config.rules.NamingStrategy;
import com.baomidou.mybatisplus.generator.engine.FreemarkerTemplateEngine;
import com.baomidou.mybatisplus.generator.fill.Column;
import com.baomidou.mybatisplus.generator.fill.Property;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Scanner;

/**
 * MyBatis-Plus 代码生成器
 * 基于官方文档配置，适配当前项目结构
 * 数据库配置直接写在代码中，需要时手动修改
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
public class CodeGenerator {

    /**
     * 项目配置
     */
    private static final String PROJECT_PATH = System.getProperty("user.dir");
    private static final String JAVA_PATH = "/src/main/java";
    private static final String RESOURCES_PATH = "/src/main/resources";

    /**
     * 包配置
     */
    private static final String PARENT_PACKAGE = "com.tem.customer";
    private static final String AUTHOR = "fumouren";

    /**
     * 数据库配置 - 直接在代码中配置，需要时手动修改
     */
    // 开发环境数据库配置
    private static final String DEV_DB_URL = "*************************************************************************************************************************************************************";
    private static final String DEV_DB_USERNAME = "root";
    private static final String DEV_DB_PASSWORD = "tem123456";


    public static void main(String[] args) {
        // 获取用户输入
        Scanner scanner = new Scanner(System.in);

        System.out.println("=== MyBatis-Plus 代码生成器 ===");

        // 选择环境
        System.out.println("请选择数据库环境：");
        System.out.println("1. dev - 开发环境");

        System.out.print("请输入环境编号（默认为1-dev）：");
        String envInput = scanner.nextLine().trim();

        // 默认环境
        String selectedEnvironment = "dev";
        if (!envInput.isEmpty()) {
            try {
                int envIndex = Integer.parseInt(envInput);
                if (envIndex != 1) {
                    System.out.println("无效的环境编号，使用默认环境：dev");
                }
                selectedEnvironment = "dev";
            } catch (NumberFormatException e) {
                System.out.println("无效的环境编号，使用默认环境：dev");
                selectedEnvironment = "dev";
            }
        }

        System.out.println("已选择环境：" + selectedEnvironment);

        System.out.println("请输入要生成的表名（多个表名用逗号分隔，如：user,role）：");
        String tableNames = scanner.nextLine().trim();

        if (tableNames.isEmpty()) {
            System.err.println("表名不能为空！");
            scanner.close();
            return;
        }

        System.out.println("请输入模块名（可选，直接回车跳过）：");
        String moduleName = scanner.nextLine().trim();

        scanner.close();

        // 执行代码生成
        generateCode(tableNames, moduleName, selectedEnvironment);
    }

    /**
     * 执行代码生成
     *
     * @param tableNames  表名（逗号分隔）
     * @param moduleName  模块名
     * @param environment 环境名称
     */
    private static void generateCode(String tableNames, String moduleName, String environment) {
        // 根据环境获取数据库配置
        String dbUrl, dbUsername, dbPassword;

        dbUrl = DEV_DB_URL;
        dbUsername = DEV_DB_USERNAME;
        dbPassword = DEV_DB_PASSWORD;

        System.out.println("使用数据库配置：");
        System.out.println("URL: " + dbUrl);
        System.out.println("Username: " + dbUsername);
        System.out.println("Password: ***");

        String[] tables = tableNames.split(",");
        for (int i = 0; i < tables.length; i++) {
            tables[i] = tables[i].trim();
        }

        FastAutoGenerator.create(dbUrl, dbUsername, dbPassword)
                // 全局配置
                .globalConfig(builder -> {
                    builder.author(AUTHOR) // 设置作者
//                            .enableSwagger() // 开启 Swagger 模式
                            .disableOpenDir() // 禁止自动打开输出目录
                            .outputDir(PROJECT_PATH + JAVA_PATH) // 指定输出目录
                            .dateType(DateType.TIME_PACK) // 使用 java.time 包
                            .commentDate("yyyy-MM-dd"); // 注释日期格式
                })
                // 包配置
                .packageConfig(builder -> {
                    builder.parent(PARENT_PACKAGE) // 设置父包名
                            .moduleName(moduleName.isEmpty() ? null : moduleName) // 设置父包模块名
                            .entity("domain.model") // 设置实体类包名
                            .service("service") // 设置 Service 包名
                            .serviceImpl("service.impl") // 设置 Service 实现类包名
                            .mapper("mapper") // 设置 Mapper 包名
                            .pathInfo(Collections.singletonMap(
                                    OutputFile.xml,
                                    PROJECT_PATH + RESOURCES_PATH + "/com/tem/customer/mapper"
                            )); // 设置 Mapper XML 文件路径
                })
                // 策略配置
                .strategyConfig(builder -> {
                    builder.addInclude(tables) // 设置需要生成的表名
                            .addTablePrefix("t_", "tb_") // 设置过滤表前缀

                            // Entity 策略配置
                            .entityBuilder()
                            .superClass("com.tem.customer.repository.entity.BaseEntity") // 设置父类
                            .addSuperEntityColumns("id", "create_time", "update_time", "create_by", "update_by", "deleted", "version") // 添加父类公共字段
                            .enableLombok() // 开启 Lombok 模型
                            .enableTableFieldAnnotation() // 开启生成实体时生成字段注解
                            .enableChainModel() // 开启链式模型
                            .naming(NamingStrategy.underline_to_camel) // 数据库表映射到实体的命名策略
                            .columnNaming(NamingStrategy.underline_to_camel) // 数据库表字段映射到实体的命名策略
                            .idType(IdType.ASSIGN_ID) // 全局主键类型，使用自定义雪花算法
                            .formatFileName("%s") // 格式化文件名称
                            .addTableFills(new Column("create_time", FieldFill.INSERT)) // 添加表字段填充
                            .addTableFills(new Column("update_time", FieldFill.INSERT_UPDATE))
                            .addTableFills(new Column("create_by", FieldFill.INSERT))
                            .addTableFills(new Column("update_by", FieldFill.INSERT_UPDATE))
                            .addTableFills(new Column("deleted", FieldFill.INSERT))
                            .addTableFills(new Property("version", FieldFill.INSERT))

//                            // Controller 策略配置
//                            .controllerBuilder()
//                            .enableRestStyle() // 开启生成@RestController 控制器
//                            .enableHyphenStyle() // 开启驼峰转连字符
//                            .formatFileName("%sController") // 格式化文件名称
//
                            // Service 策略配置
                            .serviceBuilder()
                            .superServiceClass("com.baomidou.mybatisplus.extension.service.IService") // 设置 Service 接口父类
                            .superServiceImplClass("com.baomidou.mybatisplus.extension.service.impl.ServiceImpl") // 设置 Service 实现类父类
                            .formatServiceFileName("%sService") // 格式化 Service 接口文件名称
                            .formatServiceImplFileName("%sServiceImpl") // 格式化 Service 实现类文件名称

                            // Mapper 策略配置
                            .mapperBuilder()
                            .superClass("com.baomidou.mybatisplus.core.mapper.BaseMapper") // 设置父类
                            .mapperAnnotation(org.apache.ibatis.annotations.Mapper.class) // 开启 @Mapper 注解
                            .enableBaseResultMap() // 启用 BaseResultMap 生成
                            .enableBaseColumnList() // 启用 BaseColumnList
                            .formatMapperFileName("%sMapper") // 格式化 Mapper 文件名称
                            .formatXmlFileName("%sMapper"); // 格式化 XML 文件名称

                })
                // 注入配置
                .injectionConfig(consumer -> {
                    Map<String, Object> customMap = new HashMap<>();
                    customMap.put("projectName", "Customer Web");
                    customMap.put("author", AUTHOR);
                    customMap.put("date", java.time.LocalDate.now().toString());
                    consumer.customMap(customMap);
                })
                // 使用 Freemarker 模板引擎
                .templateEngine(new FreemarkerTemplateEngine())
                // 执行生成
                .execute();

        System.out.println("代码生成完成！");
        System.out.println("生成的文件位置：");
        System.out.println("- Java 文件：" + PROJECT_PATH + JAVA_PATH);
        System.out.println("- XML 文件：" + PROJECT_PATH + RESOURCES_PATH + "/mapper");
    }
}
