package com.tem.customer.shared.utils;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Objects;

/**
 * 金额工具类
 * <p>
 * 提供分和元之间的转换功能，支持多种数据类型
 * 统一使用四舍五入的舍入模式，保留2位小数精度
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-29
 */
public final class AmountUtil {

    /**
     * 分转元的除数（100）
     */
    private static final BigDecimal HUNDRED = BigDecimal.valueOf(100);

    /**
     * 默认小数位数（2位）
     */
    private static final int DEFAULT_SCALE = 2;

    /**
     * 默认舍入模式（四舍五入）
     */
    private static final RoundingMode DEFAULT_ROUNDING_MODE = RoundingMode.HALF_UP;

    /**
     * 零值常量
     */
    private static final Long ZERO_LONG = 0L;

    /**
     * 私有构造函数，防止实例化
     */
    private AmountUtil() {
        throw new UnsupportedOperationException("Utility class cannot be instantiated");
    }

    /**
     * 将分（Long类型）转换为元（BigDecimal类型）
     *
     * @param fenAmount 分金额，可以为null
     * @return 元金额，null时返回BigDecimal.ZERO
     */
    public static BigDecimal fenToYuan(Long fenAmount) {
        if (Objects.isNull(fenAmount)) {
            return BigDecimal.ZERO;
        }
        return BigDecimal.valueOf(fenAmount).divide(HUNDRED, DEFAULT_SCALE, DEFAULT_ROUNDING_MODE);
    }

    /**
     * 将分（BigDecimal类型）转换为元（BigDecimal类型）
     *
     * @param fenAmount 分金额，可以为null
     * @return 元金额，null时返回BigDecimal.ZERO
     */
    public static BigDecimal fenToYuan(BigDecimal fenAmount) {
        if (Objects.isNull(fenAmount)) {
            return BigDecimal.ZERO;
        }
        return fenAmount.divide(HUNDRED, DEFAULT_SCALE, DEFAULT_ROUNDING_MODE);
    }

    /**
     * 将元（String类型）转换为分（Long类型）
     *
     * @param yuanAmount 元金额字符串，可以为null或空字符串
     * @return 分金额，无效输入时返回0L
     * @throws IllegalArgumentException 当字符串格式不是有效数字时抛出
     */
    public static Long yuanToFen(String yuanAmount) {
        if (StringUtils.isBlank(yuanAmount)) {
            return ZERO_LONG;
        }
        if (!NumberUtils.isCreatable(yuanAmount)) {
            throw new IllegalArgumentException("Invalid amount format: " + yuanAmount);
        }
        try {
            return new BigDecimal(yuanAmount).multiply(HUNDRED).setScale(0, DEFAULT_ROUNDING_MODE).longValue();
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("Invalid amount format: " + yuanAmount, e);
        }
    }

    /**
     * 将元（BigDecimal类型）转换为分（Long类型）
     *
     * @param yuanAmount 元金额，可以为null
     * @return 分金额，null时返回0L
     */
    public static Long yuanToFen(BigDecimal yuanAmount) {
        if (Objects.isNull(yuanAmount)) {
            return ZERO_LONG;
        }
        return yuanAmount.multiply(HUNDRED).setScale(0, DEFAULT_ROUNDING_MODE).longValue();
    }

    /**
     * 将元（Long类型）转换为分（Long类型）
     *
     * @param yuanAmount 元金额，可以为null
     * @return 分金额，null时返回0L
     */
    public static Long yuanToFen(Long yuanAmount) {
        if (Objects.isNull(yuanAmount)) {
            return ZERO_LONG;
        }
        return BigDecimal.valueOf(yuanAmount).multiply(HUNDRED).longValue();
    }

    /**
     * 将元（BigDecimal类型）转换为分（BigDecimal类型）
     *
     * @param yuanAmount 元金额，可以为null
     * @return 分金额，null时返回BigDecimal.ZERO
     */
    public static BigDecimal yuanToFenDecimal(BigDecimal yuanAmount) {
        if (Objects.isNull(yuanAmount)) {
            return BigDecimal.ZERO;
        }
        return yuanAmount.multiply(HUNDRED);
    }

    /**
     * 检查金额是否为正数
     *
     * @param amount 待检查的金额
     * @return 如果金额大于0返回true，否则返回false
     */
    public static boolean isPositive(BigDecimal amount) {
        return Objects.nonNull(amount) && amount.compareTo(BigDecimal.ZERO) > 0;
    }

    /**
     * 检查金额是否为零或负数
     *
     * @param amount 待检查的金额
     * @return 如果金额小于等于0返回true，否则返回false
     */
    public static boolean isZeroOrNegative(BigDecimal amount) {
        return Objects.isNull(amount) || amount.compareTo(BigDecimal.ZERO) <= 0;
    }

    /**
     * 安全的金额相加
     *
     * @param amount1 第一个金额
     * @param amount2 第二个金额
     * @return 相加结果，任一参数为null时视为0
     */
    public static BigDecimal add(BigDecimal amount1, BigDecimal amount2) {
        BigDecimal a1 = Objects.isNull(amount1) ? BigDecimal.ZERO : amount1;
        BigDecimal a2 = Objects.isNull(amount2) ? BigDecimal.ZERO : amount2;
        return a1.add(a2);
    }

    /**
     * 安全的金额相减
     *
     * @param amount1 被减数
     * @param amount2 减数
     * @return 相减结果，任一参数为null时视为0
     */
    public static BigDecimal subtract(BigDecimal amount1, BigDecimal amount2) {
        BigDecimal a1 = Objects.isNull(amount1) ? BigDecimal.ZERO : amount1;
        BigDecimal a2 = Objects.isNull(amount2) ? BigDecimal.ZERO : amount2;
        return a1.subtract(a2);
    }
}
