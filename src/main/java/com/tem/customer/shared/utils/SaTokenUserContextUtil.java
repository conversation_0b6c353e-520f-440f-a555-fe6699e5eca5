package com.tem.customer.shared.utils;

import cn.dev33.satoken.stp.StpUtil;
import com.iplatform.common.ResponseDto;
import com.iplatform.common.SpringContextUtils;
import com.iplatform.common.utils.LogUtils;
import com.tem.platform.api.UserService;
import com.tem.platform.api.dto.UserDto;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Optional;

/**
 * 用户上下文工具类
 * 基于Sa-Token实现，提供可靠的用户信息获取方法和完整的Sa-Token操作功能
 * <p>
 * 主要功能：
 * 1. 获取当前登录用户的基本信息
 * 2. 获取用户归属的企业ID、组织信息等
 * 3. 提供安全的空值处理
 * 4. 统一的异常处理和日志记录
 * 5. 基于Sa-Token的用户会话管理
 * 6. Token管理（超时、续签、踢下线等）
 * 7. 会话数据管理
 * 8. 指定用户操作
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Slf4j
public class SaTokenUserContextUtil {


    /**
     * 获取当前登录用户对象
     * 基于Sa-Token获取用户信息
     *
     * @return 当前用户对象，如果未登录则返回null
     */
    public static UserDto getCurrentUser() {
        try {
            if (!StpUtil.isLogin()) {
                return null;
            }

            Long userId = StpUtil.getLoginIdAsLong();

            // 从Dubbo服务获取用户详细信息
            UserService userService = SpringContextUtils.getBean(UserService.class);
            ResponseDto<UserDto> userResponse = userService.getUser(userId);

            if (userResponse.isSuccess() && userResponse.getData() != null) {
                return userResponse.getData();
            }

            LogUtils.warn(log, "获取用户信息失败: userId={}, response={}", userId, userResponse);
            return null;

        } catch (Exception e) {
            LogUtils.error(log, "获取当前用户信息异常", e);
            return null;
        }
    }

    /**
     * 简化版获取当前用户
     * 仅返回基本的用户ID信息，不查询详细信息
     *
     * @return 当前用户ID，如果未登录则返回null
     */
    public static Long getCurrentUserIdSimple() {
        return getCurrentUserId();
    }

    /**
     * 获取当前登录用户对象（Optional包装）
     *
     * @return Optional包装的当前用户对象
     */
    public static Optional<UserDto> getCurrentUserOptional() {
        return Optional.ofNullable(getCurrentUser());
    }

    /**
     * 获取当前用户ID
     * 基于Sa-Token获取
     *
     * @return 当前用户ID，如果未登录则返回null
     */
    public static Long getCurrentUserId() {
        try {
            if (StpUtil.isLogin()) {
                return StpUtil.getLoginIdAsLong();
            }
        } catch (Exception e) {
            LogUtils.warn(log, "获取当前用户ID失败", e);
        }
        return null;
    }

    /**
     * 获取当前用户全名
     * 基于Sa-Token获取用户信息后提取
     *
     * @return 当前用户全名，如果未登录则返回null
     */
    public static String getCurrentUserFullname() {
        UserDto user = getCurrentUser();
        return user != null ? user.getFullname() : null;
    }


    /**
     * 获取当前用户归属的企业ID
     * 基于Sa-Token获取用户信息后提取
     *
     * @return 企业ID，如果未登录或未关联企业则返回null
     */
    public static Long getCurrentUserPartnerId() {
        UserDto user = getCurrentUser();
        return user != null ? user.getPartnerId() : null;
    }

    /**
     * 获取当前用户的组织ID
     * 基于Sa-Token获取用户信息后提取
     *
     * @return 组织ID，如果未登录或未关联组织则返回null
     */
    public static Long getCurrentUserOrgId() {
        UserDto user = getCurrentUser();
        return user != null ? user.getOrgId() : null;
    }


    /**
     * 获取用户上下文快照
     * 用于异步任务中传递用户信息
     *
     * @return 用户上下文快照，如果未登录则返回null
     */
    public static UserContextSnapshot getCurrentUserSnapshot() {
        UserDto user = getCurrentUser();
        if (user == null) {
            return null;
        }

        try {
            return new UserContextSnapshot(
                    user.getId(),
                    user.getUsername(),
                    user.getFullname(),
                    user.getPartnerId(),
                    user.getOrgId()
            );
        } catch (Exception e) {
            LogUtils.warn(log, "创建用户上下文快照失败", e);
            return null;
        }
    }

    /**
     * 检查当前用户是否已登录
     *
     * @return true-已登录，false-未登录
     */
    public static boolean isLogin() {
        try {
            return StpUtil.isLogin();
        } catch (Exception e) {
            LogUtils.warn(log, "检查登录状态失败", e);
            return false;
        }
    }

    /**
     * 获取当前用户的Token值
     *
     * @return Token值，如果未登录则返回null
     */
    public static String getCurrentToken() {
        try {
            if (StpUtil.isLogin()) {
                return StpUtil.getTokenValue();
            }
        } catch (Exception e) {
            LogUtils.warn(log, "获取当前Token失败", e);
        }
        return null;
    }

    /**
     * 获取当前登录用户ID（字符串形式）
     *
     * @return 用户ID字符串，未登录返回null
     */
    public static String getCurrentUserIdAsString() {
        try {
            if (StpUtil.isLogin()) {
                return StpUtil.getLoginIdAsString();
            }
        } catch (Exception e) {
            LogUtils.warn(log, "获取当前登录用户ID字符串失败: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 获取Token剩余有效时间（秒）
     *
     * @return 剩余有效时间，-1表示永不过期，-2表示已过期
     */
    public static long getTokenTimeout() {
        try {
            if (StpUtil.isLogin()) {
                return StpUtil.getTokenTimeout();
            }
        } catch (Exception e) {
            LogUtils.warn(log, "获取Token超时时间失败: {}", e.getMessage());
        }
        return -2;
    }

    /**
     * 强制指定用户下线
     *
     * @param userId 用户ID
     */
    public static void kickout(Long userId) {
        try {
            StpUtil.kickout(userId);
            LogUtils.info(log, "强制用户下线成功: userId={}", userId);
        } catch (Exception e) {
            LogUtils.error(log, "强制用户下线失败: userId=" + userId, e);
        }
    }

    /**
     * 强制指定用户下线（指定设备）
     *
     * @param userId 用户ID
     * @param device 设备标识
     */
    public static void kickout(Long userId, String device) {
        try {
            StpUtil.kickout(userId, device);
            LogUtils.info(log, "强制用户下线成功: userId={}, device={}", userId, device);
        } catch (Exception e) {
            LogUtils.error(log, "强制用户下线失败: userId=" + userId + ", device=" + device, e);
        }
    }

    /**
     * 获取指定用户的Token列表
     *
     * @param userId 用户ID
     * @return Token列表
     */
    public static List<String> getTokenValueListByLoginId(Long userId) {
        try {
            return StpUtil.getTokenValueListByLoginId(userId);
        } catch (Exception e) {
            LogUtils.error(log, "获取用户Token列表失败: userId=" + userId, e);
            return null;
        }
    }

    /**
     * 检查指定用户是否在线
     *
     * @param userId 用户ID
     * @return 是否在线
     */
    public static boolean isLogin(Long userId) {
        try {
            return StpUtil.isLogin(userId);
        } catch (Exception e) {
            LogUtils.warn(log, "检查用户登录状态失败: userId=" + userId, e);
            return false;
        }
    }

    /**
     * 续签Token
     *
     * @param timeout 续签时间（秒）
     */
    public static void renewTimeout(long timeout) {
        try {
            if (StpUtil.isLogin()) {
                StpUtil.renewTimeout(timeout);
                LogUtils.info(log, "Token续签成功: timeout={}", timeout);
            }
        } catch (Exception e) {
            LogUtils.error(log, "Token续签失败: timeout=" + timeout, e);
        }
    }

    /**
     * 获取当前会话的额外数据
     *
     * @param key 数据键
     * @return 数据值
     */
    public static Object getExtra(String key) {
        try {
            if (StpUtil.isLogin()) {
                return StpUtil.getExtra(key);
            }
        } catch (Exception e) {
            LogUtils.warn(log, "获取会话额外数据失败: key=" + key, e);
        }
        return null;
    }

    /**
     * 设置当前会话的额外数据
     *
     * @param key   数据键
     * @param value 数据值
     */
    public static void setExtra(String key, Object value) {
        try {
            if (StpUtil.isLogin()) {
                StpUtil.getSession().set(key, value);
                LogUtils.debug(log, "设置会话额外数据成功: key={}", key);
            }
        } catch (Exception e) {
            LogUtils.error(log, "设置会话额外数据失败: key=" + key, e);
        }
    }

    /**
     * 删除当前会话的额外数据
     *
     * @param key 数据键
     */
    public static void removeExtra(String key) {
        try {
            if (StpUtil.isLogin()) {
                StpUtil.getSession().delete(key);
                LogUtils.debug(log, "删除会话额外数据成功: key={}", key);
            }
        } catch (Exception e) {
            LogUtils.error(log, "删除会话额外数据失败: key=" + key, e);
        }
    }

    /**
     * 用户上下文快照类
     * 用于在异步任务中传递用户信息
     */
    public record UserContextSnapshot(Long userId, String username, String fullname, Long partnerId, Long orgId) {
    }
}
