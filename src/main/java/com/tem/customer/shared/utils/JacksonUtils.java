package com.tem.customer.shared.utils;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;

/**
 * Json utils implement by Jackson.
 *
 * <AUTHOR>
 * @since 2025-05-29
 */
@Slf4j
public final class JacksonUtils {

    private JacksonUtils() {
    }

    /**
     * -- GETTER --
     *  Get ObjectMapper instance for advanced usage.
     *
     */
    @Getter
    static ObjectMapper mapper = new ObjectMapper();
    /**
     * -- GETTER --
     *  Get pretty print ObjectMapper instance.
     *
     */
    @Getter
     static ObjectMapper prettyMapper = new ObjectMapper();

    /*
        Ignore unknown properties during deserialization
        Only serialize non-null properties
     */
    static {
        // 创建自定义模块处理时间类型序列化和空字符串转null
        SimpleModule customModule = new SimpleModule();

        // LocalDateTime序列化器
        customModule.addSerializer(LocalDateTime.class, new JsonSerializer<>() {
            @Override
            public void serialize(LocalDateTime value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
                gen.writeString(value.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            }
        });

        // LocalDate序列化器
        customModule.addSerializer(LocalDate.class, new JsonSerializer<>() {
            @Override
            public void serialize(LocalDate value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
                gen.writeString(value.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            }
        });

        // LocalTime序列化器
        customModule.addSerializer(LocalTime.class, new JsonSerializer<>() {
            @Override
            public void serialize(LocalTime value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
                gen.writeString(value.format(DateTimeFormatter.ofPattern("HH:mm:ss")));
            }
        });

        // ZonedDateTime序列化器
        customModule.addSerializer(ZonedDateTime.class, new JsonSerializer<>() {
            @Override
            public void serialize(ZonedDateTime value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
                gen.writeString(value.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss XXX")));
            }
        });

        // 添加String类型的反序列化器，将空字符串转换为null
        customModule.addDeserializer(String.class, new JsonDeserializer<>() {
            @Override
            public String deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
                String value = p.getValueAsString();
                return (value != null && value.trim().isEmpty()) ? null : value;
            }
        });

        // 配置普通mapper
        configureMapper(mapper, customModule, false);

        // 配置美化输出mapper
        configureMapper(prettyMapper, customModule, true);
    }

    /**
     * 配置ObjectMapper
     */
    private static void configureMapper(ObjectMapper objectMapper, SimpleModule customModule, boolean prettyPrint) {
        objectMapper.registerModule(new JavaTimeModule());
        objectMapper.registerModule(customModule);
        objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        objectMapper.disable(SerializationFeature.FAIL_ON_EMPTY_BEANS);
        objectMapper.setDateFormat(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
        objectMapper.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));
        objectMapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        objectMapper.enable(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT);

        if (prettyPrint) {
            objectMapper.enable(SerializationFeature.INDENT_OUTPUT);
        }
    }

    /**
     * Object to json string.
     *
     * @param obj obj
     * @return json string
     * @throws RuntimeException if transfer failed
     */
    public static String toJson(Object obj) {
        try {
            return mapper.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            String className = obj != null ? obj.getClass().getName() : "null";
            throw new RuntimeException("JSON serialization failed: " + className, e);
        }
    }

    /**
     * Object to json string with pretty print.
     *
     * @param obj obj
     * @return pretty formatted json string
     * @throws RuntimeException if transfer failed
     */
    public static String toPrettyJson(Object obj) {
        try {
            return prettyMapper.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            String className = obj != null ? obj.getClass().getName() : "null";
            throw new RuntimeException("JSON serialization failed: " + className, e);
        }
    }

    /**
     * Object to json string byte array.
     *
     * @param obj obj
     * @return json string byte array
     * @throws RuntimeException if transfer failed
     */
    public static byte[] toJsonBytes(Object obj) {
        try {
            return mapper.writeValueAsBytes(obj);
        } catch (JsonProcessingException e) {
            String className = obj != null ? obj.getClass().getName() : "null";
            throw new RuntimeException("JSON serialization failed: " + className, e);
        }
    }

    /**
     * Json string deserialize to Object.
     *
     * @param json json string
     * @param cls  class of object
     * @param <T>  General type
     * @return object
     * @throws RuntimeException if deserialize failed
     */
    public static <T> T toObj(byte[] json, Class<T> cls) {
        try {
            return mapper.readValue(json, cls);
        } catch (Exception e) {
            throw new RuntimeException("JSON deserialization failed: " + cls.getName(), e);
        }
    }

    /**
     * Json string deserialize to Object.
     *
     * @param json json string
     * @param cls  class of object
     * @param <T>  General type
     * @return object
     * @throws RuntimeException if deserialize failed
     */
    public static <T> T toObj(String json, Class<T> cls) {
        try {
            return mapper.readValue(json, cls);
        } catch (IOException e) {
            throw new RuntimeException("JSON deserialization failed: " + cls.getName(), e);
        }
    }

    /**
     * Json string deserialize to Object.
     *
     * @param json          json string
     * @param typeReference {@link TypeReference} of object
     * @param <T>           General type
     * @return object
     * @throws RuntimeException if deserialize failed
     */
    public static <T> T toObj(String json, TypeReference<T> typeReference) {
        try {
            return mapper.readValue(json, typeReference);
        } catch (IOException e) {
            throw new RuntimeException("JSON deserialization failed", e);
        }
    }

    /**
     * Json string deserialize to Jackson {@link JsonNode}.
     *
     * @param json json string
     * @return {@link JsonNode}
     * @throws RuntimeException if deserialize failed
     */
    public static JsonNode toObj(String json) {
        try {
            return mapper.readTree(json);
        } catch (IOException e) {
            throw new RuntimeException("JSON deserialization failed", e);
        }
    }

    public static Boolean isJson(String str) {
        if (str == null) {
            throw new IllegalArgumentException("Input string cannot be null");
        }
        if (str.trim().isEmpty()) {
            return false;
        }
        try {
            mapper.readTree(str);
            return true;
        } catch (IOException e) {
            return false;
        }
    }

    // ==================== 性能优化方法 ====================

    /**
     * Convert object to another type using Jackson's convertValue.
     * This is more efficient than serialize-deserialize cycle.
     *
     * @param fromValue source object
     * @param toValueType target type
     * @param <T> target type
     * @return converted object
     * @throws RuntimeException if conversion failed
     */
    public static <T> T convertValue(Object fromValue, Class<T> toValueType) {
        try {
            return mapper.convertValue(fromValue, toValueType);
        } catch (IllegalArgumentException e) {
            throw new RuntimeException("Object conversion failed: " +
                (fromValue != null ? fromValue.getClass().getName() : "null") +
                " -> " + toValueType.getName(), e);
        }
    }

    /**
     * Convert object to another type using TypeReference.
     *
     * @param fromValue source object
     * @param toValueTypeRef target type reference
     * @param <T> target type
     * @return converted object
     * @throws RuntimeException if conversion failed
     */
    public static <T> T convertValue(Object fromValue, TypeReference<T> toValueTypeRef) {
        try {
            return mapper.convertValue(fromValue, toValueTypeRef);
        } catch (IllegalArgumentException e) {
            throw new RuntimeException("Object conversion failed: " +
                (fromValue != null ? fromValue.getClass().getName() : "null"), e);
        }
    }

    /**
     * Deep copy object using JSON serialization/deserialization.
     *
     * @param obj source object
     * @param clazz target class
     * @param <T> target type
     * @return deep copied object
     * @throws RuntimeException if copy failed
     */
    public static <T> T deepCopy(Object obj, Class<T> clazz) {
        try {
            String json = mapper.writeValueAsString(obj);
            return mapper.readValue(json, clazz);
        } catch (IOException e) {
            throw new RuntimeException("Deep copy failed: " +
                (obj != null ? obj.getClass().getName() : "null"), e);
        }
    }

    // ==================== 高级功能方法 ====================

    /**
     * Merge two JSON objects. The second object will override values in the first.
     *
     * @param json1 first JSON string
     * @param json2 second JSON string
     * @return merged JSON string
     * @throws RuntimeException if merge failed
     */
    public static String mergeJson(String json1, String json2) {
        try {
            JsonNode node1 = mapper.readTree(json1);
            JsonNode node2 = mapper.readTree(json2);
            JsonNode merged = merge(node1, node2);
            return mapper.writeValueAsString(merged);
        } catch (IOException e) {
            throw new RuntimeException("JSON merge failed", e);
        }
    }

    /**
     * Merge two JsonNode objects.
     */
    private static JsonNode merge(JsonNode mainNode, JsonNode updateNode) {
        if (updateNode == null || updateNode.isNull()) {
            return mainNode;
        }
        if (mainNode == null || mainNode.isNull()) {
            return updateNode;
        }

        if (mainNode.isObject() && updateNode.isObject()) {
            ObjectNode merged = mapper.createObjectNode();

            // Add all fields from main node
            for (Map.Entry<String, JsonNode> entry : mainNode.properties()) {
                merged.set(entry.getKey(), entry.getValue());
            }

            // Merge/override with update node fields
            for (Map.Entry<String, JsonNode> entry : updateNode.properties()) {
                String fieldName = entry.getKey();
                JsonNode updateValue = entry.getValue();

                if (merged.has(fieldName)) {
                    JsonNode mergedValue = merge(merged.get(fieldName), updateValue);
                    merged.set(fieldName, mergedValue);
                } else {
                    merged.set(fieldName, updateValue);
                }
            }

            return merged;
        } else {
            // For non-object nodes, update node takes precedence
            return updateNode;
        }
    }

    /**
     * Get value from JSON using JSON path.
     *
     * @param json JSON string
     * @param path JSON path (e.g., "/user/name", "/items/0/id")
     * @return JsonNode at the specified path, null if not found
     * @throws RuntimeException if parsing failed
     */
    public static JsonNode getValueByPath(String json, String path) {
        try {
            JsonNode rootNode = mapper.readTree(json);
            return rootNode.at(path);
        } catch (IOException e) {
            throw new RuntimeException("Failed to get value by path: " + path, e);
        }
    }

    /**
     * Get string value from JSON using JSON path.
     *
     * @param json JSON string
     * @param path JSON path
     * @return string value, null if not found or not a text node
     */
    public static String getStringByPath(String json, String path) {
        JsonNode node = getValueByPath(json, path);
        return node != null && node.isTextual() ? node.asText() : null;
    }

    /**
     * Get integer value from JSON using JSON path.
     *
     * @param json JSON string
     * @param path JSON path
     * @return integer value, null if not found or not a number
     */
    public static Integer getIntByPath(String json, String path) {
        JsonNode node = getValueByPath(json, path);
        return node != null && node.isNumber() ? node.asInt() : null;
    }

    /**
     * Get long value from JSON using JSON path.
     *
     * @param json JSON string
     * @param path JSON path
     * @return long value, null if not found or not a number
     */
    public static Long getLongByPath(String json, String path) {
        JsonNode node = getValueByPath(json, path);
        return node != null && node.isNumber() ? node.asLong() : null;
    }

    /**
     * Get boolean value from JSON using JSON path.
     *
     * @param json JSON string
     * @param path JSON path
     * @return boolean value, null if not found or not a boolean
     */
    public static Boolean getBooleanByPath(String json, String path) {
        JsonNode node = getValueByPath(json, path);
        return node != null && node.isBoolean() ? node.asBoolean() : null;
    }

    /**
     * Find first node by field name (recursive search).
     *
     * @param json JSON string
     * @param fieldName field name to search
     * @return first JsonNode with the specified field name, null if not found
     */
    public static JsonNode findValueByFieldName(String json, String fieldName) {
        try {
            JsonNode rootNode = mapper.readTree(json);
            return rootNode.findValue(fieldName);
        } catch (IOException e) {
            throw new RuntimeException("Failed to find value by field name: " + fieldName, e);
        }
    }

    /**
     * Find all nodes by field name (recursive search).
     *
     * @param json JSON string
     * @param fieldName field name to search
     * @return list of JsonNodes with the specified field name
     */
    public static List<JsonNode> findValuesByFieldName(String json, String fieldName) {
        try {
            JsonNode rootNode = mapper.readTree(json);
            return rootNode.findValues(fieldName);
        } catch (IOException e) {
            throw new RuntimeException("Failed to find values by field name: " + fieldName, e);
        }
    }

    // ==================== 创建JSON方法 ====================

    /**
     * Create empty ObjectNode.
     *
     * @return empty ObjectNode
     */
    public static ObjectNode createObjectNode() {
        return mapper.createObjectNode();
    }

    /**
     * Create empty ArrayNode.
     *
     * @return empty ArrayNode
     */
    public static ArrayNode createArrayNode() {
        return mapper.createArrayNode();
    }

    /**
     * Create ObjectNode from Map.
     *
     * @param map source map
     * @return ObjectNode
     */
    public static ObjectNode createObjectNode(Map<String, Object> map) {
        return mapper.valueToTree(map);
    }

    /**
     * Create ArrayNode from List.
     *
     * @param list source list
     * @return ArrayNode
     */
    public static ArrayNode createArrayNode(List<Object> list) {
        return mapper.valueToTree(list);
    }

    // ==================== 工具方法 ====================

    /**
     * Check if a JsonNode is null or missing.
     *
     * @param node JsonNode to check
     * @return true if null or missing
     */
    public static boolean isNullOrMissing(JsonNode node) {
        return node == null || node.isNull() || node.isMissingNode();
    }

    /**
     * Check if a string represents a valid JSON array.
     *
     * @param str string to check
     * @return true if valid JSON array
     */
    public static boolean isJsonArray(String str) {
        if (str == null || str.trim().isEmpty()) {
            return false;
        }
        try {
            JsonNode node = mapper.readTree(str);
            return node.isArray();
        } catch (IOException e) {
            return false;
        }
    }

    /**
     * Check if a string represents a valid JSON object.
     *
     * @param str string to check
     * @return true if valid JSON object
     */
    public static boolean isJsonObject(String str) {
        if (str == null || str.trim().isEmpty()) {
            return false;
        }
        try {
            JsonNode node = mapper.readTree(str);
            return node.isObject();
        } catch (IOException e) {
            return false;
        }
    }
}
