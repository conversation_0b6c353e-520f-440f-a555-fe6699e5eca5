package com.tem.customer.shared.utils;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import jakarta.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 线程池工具类
 * 提供统一的线程池访问接口和监控功能
 *
 * <AUTHOR>
 * @since 2025-05-29
 */
@Slf4j
@Component
public class ThreadPoolUtil {

    private final ExecutorService threadPoolExecutor;

    private static ThreadPoolUtil instance;

    public ThreadPoolUtil(ExecutorService threadPoolExecutor) {
        this.threadPoolExecutor = threadPoolExecutor;
    }

    @PostConstruct
    public void init() {
        instance = this;
        log.info("ThreadPoolUtil 初始化完成");
    }

    /**
     * 获取线程池执行器
     */
    public static ExecutorService getExecutor() {
        return instance.threadPoolExecutor;
    }

    /**
     * 提交任务（自动传递TraceId）
     */
    public static Future<?> submit(Runnable task) {
        return getExecutor().submit(TraceIdContext.wrapWithTraceId(task));
    }

    /**
     * 提交有返回值的任务（自动传递TraceId）
     */
    public static <T> Future<T> submit(Callable<T> task) {
        return getExecutor().submit(TraceIdContext.wrapWithTraceId(task));
    }

    /**
     * 执行任务（无返回值，自动传递TraceId）
     */
    public static void execute(Runnable task) {
        getExecutor().execute(TraceIdContext.wrapWithTraceId(task));
    }

    /**
     * 提交任务（不传递TraceId，使用原始任务）
     */
    public static Future<?> submitRaw(Runnable task) {
        return getExecutor().submit(task);
    }

    /**
     * 提交有返回值的任务（不传递TraceId，使用原始任务）
     */
    public static <T> Future<T> submitRaw(Callable<T> task) {
        return getExecutor().submit(task);
    }

    /**
     * 执行任务（无返回值，不传递TraceId，使用原始任务）
     */
    public static void executeRaw(Runnable task) {
        getExecutor().execute(task);
    }

    /**
     * 使用指定TraceId提交任务
     */
    public static Future<?> submitWithTraceId(Runnable task, String traceId) {
        return getExecutor().submit(TraceIdContext.wrapWithTraceId(task, traceId));
    }

    /**
     * 使用指定TraceId提交有返回值的任务
     */
    public static <T> Future<T> submitWithTraceId(Callable<T> task, String traceId) {
        return getExecutor().submit(TraceIdContext.wrapWithTraceId(task, traceId));
    }

    /**
     * 使用指定TraceId执行任务
     */
    public static void executeWithTraceId(Runnable task, String traceId) {
        getExecutor().execute(TraceIdContext.wrapWithTraceId(task, traceId));
    }

    /**
     * 获取线程池状态信息
     */
    public static ThreadPoolStatus getThreadPoolStatus() {
        ExecutorService executor = getExecutor();
        ThreadPoolExecutor tpe = extractThreadPoolExecutor(executor);

        if (tpe != null) {
            return ThreadPoolStatus.builder()
                    .poolName("DEFAULT")
                    .corePoolSize(tpe.getCorePoolSize())
                    .maximumPoolSize(tpe.getMaximumPoolSize())
                    .activeCount(tpe.getActiveCount())
                    .poolSize(tpe.getPoolSize())
                    .queueSize(tpe.getQueue().size())
                    .completedTaskCount(tpe.getCompletedTaskCount())
                    .taskCount(tpe.getTaskCount())
                    .isShutdown(tpe.isShutdown())
                    .isTerminated(tpe.isTerminated())
                    .build();
        }
        return null;
    }

    /**
     * 从ExecutorService中提取ThreadPoolExecutor
     * 支持从TTL包装器中提取原始的ThreadPoolExecutor
     */
    private static ThreadPoolExecutor extractThreadPoolExecutor(ExecutorService executor) {
        if (executor instanceof ThreadPoolExecutor) {
            return (ThreadPoolExecutor) executor;
        }

        // 处理TTL包装的情况
        try {
            String className = executor.getClass().getName();
            if (className.contains("ExecutorServiceTtlWrapper") || className.contains("TtlExecutorService")) {
                // 尝试获取executorService字段
                try {
                    java.lang.reflect.Field field = executor.getClass().getDeclaredField("executorService");
                    field.setAccessible(true);
                    Object wrappedExecutor = field.get(executor);

                    if (wrappedExecutor instanceof ThreadPoolExecutor) {
                        return (ThreadPoolExecutor) wrappedExecutor;
                    }
                } catch (NoSuchFieldException e) {
                    // executorService字段不存在，尝试遍历所有字段
                    java.lang.reflect.Field[] fields = executor.getClass().getDeclaredFields();
                    for (java.lang.reflect.Field field : fields) {
                        field.setAccessible(true);
                        Object value = field.get(executor);

                        if (value instanceof ThreadPoolExecutor) {
                            return (ThreadPoolExecutor) value;
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.warn("无法从TTL包装器中提取ThreadPoolExecutor: {}", e.getMessage());
        }

        return null;
    }

    /**
     * 线程池状态信息
     */
    @lombok.Builder
    @lombok.Data
    public static class ThreadPoolStatus {
        private String poolName;
        private int corePoolSize;
        private int maximumPoolSize;
        private int activeCount;
        private int poolSize;
        private int queueSize;
        private long completedTaskCount;
        private long taskCount;
        private boolean isShutdown;
        private boolean isTerminated;

        /**
         * 获取队列使用率
         */
        public double getQueueUsageRate() {
            if (queueSize == 0) {
                return 0.0;
            }
            return (double) queueSize / (queueSize + maximumPoolSize);
        }

        /**
         * 获取线程池使用率
         */
        public double getPoolUsageRate() {
            if (maximumPoolSize == 0) {
                return 0.0;
            }
            return (double) activeCount / maximumPoolSize;
        }
    }

    @PreDestroy
    public void destroy() {
        log.info("ThreadPoolUtil 开始销毁...");
    }

    /**
     * 自定义线程工厂
     */
    public static class BenefitsThreadFactory implements ThreadFactory {

        private final String namePrefix;
        private final AtomicInteger threadNumber = new AtomicInteger(1);
        private final int threadPriority;
        private final boolean isDaemon;

        public BenefitsThreadFactory(String namePrefix) {
            this(namePrefix, Thread.NORM_PRIORITY, false);
        }

        @SuppressWarnings("all")
        public BenefitsThreadFactory(String namePrefix, int threadPriority, boolean isDaemon) {
            this.namePrefix = namePrefix;
            this.threadPriority = threadPriority;
            this.isDaemon = isDaemon;
        }

        @Override
        public Thread newThread(@NotNull Runnable runnable) {

            Thread thread = new Thread(runnable, namePrefix + "-" + threadNumber.getAndIncrement());

            if (thread.isDaemon() != isDaemon) {
                thread.setDaemon(isDaemon);
            }

            if (thread.getPriority() != threadPriority) {
                thread.setPriority(threadPriority);
            }

            return thread;
        }
    }
}
