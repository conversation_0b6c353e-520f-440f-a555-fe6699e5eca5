package com.tem.customer.shared.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;
import java.util.Optional;

/**
 * Web工具类
 * 提供HTTP请求相关的工具方法
 *
 * <AUTHOR>
 * @since 2025-06-23
 */
@Slf4j
public class WebUtil {

    /**
     * 获取当前HTTP请求对象
     *
     * @return HttpServletRequest对象，如果不在Web环境中则返回null
     */
    public static HttpServletRequest getCurrentRequest() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            return attributes != null ? attributes.getRequest() : null;
        } catch (Exception e) {
            log.debug("获取当前请求对象失败，可能不在Web环境中", e);
            return null;
        }
    }

    /**
     * 获取当前HTTP请求对象（Optional包装）
     *
     * @return Optional包装的HttpServletRequest对象
     */
    public static Optional<HttpServletRequest> getCurrentRequestOptional() {
        return Optional.ofNullable(getCurrentRequest());
    }

    /**
     * 获取客户端IP地址
     *
     * @return 客户端IP地址，如果获取失败则返回"unknown"
     */
    public static String getClientIpAddress() {
        HttpServletRequest request = getCurrentRequest();
        if (request == null) {
            return "unknown";
        }

        return getClientIpAddress(request);
    }

    /**
     * 获取客户端IP地址
     *
     * @param request HTTP请求对象
     * @return 客户端IP地址
     */
    public static String getClientIpAddress(HttpServletRequest request) {
        if (request == null) {
            return "unknown";
        }

        String ip = null;

        // 检查各种代理头
        String[] headers = {
            "X-Forwarded-For",
            "X-Real-IP", 
            "Proxy-Client-IP",
            "WL-Proxy-Client-IP",
            "HTTP_CLIENT_IP",
            "HTTP_X_FORWARDED_FOR"
        };

        for (String header : headers) {
            ip = request.getHeader(header);
            if (isValidIp(ip)) {
                // X-Forwarded-For可能包含多个IP，取第一个
                if (ip.contains(",")) {
                    ip = ip.split(",")[0].trim();
                }
                break;
            }
        }

        // 如果代理头都没有，则使用getRemoteAddr
        if (!isValidIp(ip)) {
            ip = request.getRemoteAddr();
        }

        // 处理IPv6的localhost
        if ("0:0:0:0:0:0:0:1".equals(ip)) {
            ip = "127.0.0.1";
        }

        return StringUtils.isNotBlank(ip) ? ip : "unknown";
    }

    /**
     * 获取用户代理信息
     *
     * @return 用户代理字符串，如果获取失败则返回"unknown"
     */
    public static String getUserAgent() {
        HttpServletRequest request = getCurrentRequest();
        if (request == null) {
            return "unknown";
        }

        String userAgent = request.getHeader("User-Agent");
        return StringUtils.isNotBlank(userAgent) ? userAgent : "unknown";
    }

    /**
     * 获取请求URI
     *
     * @return 请求URI，如果获取失败则返回"unknown"
     */
    public static String getRequestUri() {
        HttpServletRequest request = getCurrentRequest();
        if (request == null) {
            return "unknown";
        }

        String uri = request.getRequestURI();
        return StringUtils.isNotBlank(uri) ? uri : "unknown";
    }

    /**
     * 获取请求方法
     *
     * @return 请求方法（GET、POST等），如果获取失败则返回"unknown"
     */
    public static String getRequestMethod() {
        HttpServletRequest request = getCurrentRequest();
        if (request == null) {
            return "unknown";
        }

        String method = request.getMethod();
        return StringUtils.isNotBlank(method) ? method : "unknown";
    }

    /**
     * 获取请求的完整URL
     *
     * @return 完整URL，如果获取失败则返回"unknown"
     */
    public static String getRequestUrl() {
        HttpServletRequest request = getCurrentRequest();
        if (request == null) {
            return "unknown";
        }

        StringBuilder url = new StringBuilder(request.getRequestURL().toString());
        String queryString = request.getQueryString();

        if (StringUtils.isNotBlank(queryString)) {
            url.append("?").append(queryString);
        }

        return url.toString();
    }

    /**
     * 获取请求参数字符串
     *
     * @return 请求参数字符串，如果获取失败则返回空字符串
     */
    public static String getQueryString() {
        HttpServletRequest request = getCurrentRequest();
        if (request == null) {
            return "";
        }

        String queryString = request.getQueryString();
        return StringUtils.isNotBlank(queryString) ? queryString : "";
    }

    /**
     * 检查IP地址是否有效
     *
     * @param ip IP地址
     * @return 如果IP地址有效返回true，否则返回false
     */
    private static boolean isValidIp(String ip) {
        return StringUtils.isNotBlank(ip) && 
               !"unknown".equalsIgnoreCase(ip) && 
               !"null".equalsIgnoreCase(ip);
    }
}
