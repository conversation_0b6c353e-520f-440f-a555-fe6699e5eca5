package com.tem.customer.shared.common;

import com.iplatform.common.exception.BizResultCode;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 响应码枚举
 *
 * <AUTHOR>
 * @since 2025-05-29
 */
@Getter
@AllArgsConstructor
public enum ResultCode implements BizResultCode {

    // ========== 通用响应码 ==========
    SUCCESS("200", "操作成功"),
    CREATED("201", "创建成功"),
    ACCEPTED("202", "请求已接受"),
    NO_CONTENT("204", "无内容"),

    // ========== 客户端错误 4xx ==========
    BAD_REQUEST("400", "请求参数错误"),
    UNAUTHORIZED("401", "未授权"),
    FORBIDDEN("403", "禁止访问"),
    NOT_FOUND("404", "资源不存在"),
    METHOD_NOT_ALLOWED("405", "请求方法不允许"),
    NOT_ACCEPTABLE("406", "不可接受的请求"),
    REQUEST_TIMEOUT("408", "请求超时"),
    CONFLICT("409", "资源冲突"),
    GONE("410", "资源已删除"),
    PRECONDITION_FAILED("412", "前置条件失败"),
    PAYLOAD_TOO_LARGE("413", "请求体过大"),
    UNSUPPORTED_MEDIA_TYPE("415", "不支持的媒体类型"),
    TOO_MANY_REQUESTS("429", "请求过于频繁"),

    // ========== 服务器错误 5xx ==========
    INTERNAL_SERVER_ERROR("500", "服务器内部错误"),
    NOT_IMPLEMENTED("501", "功能未实现"),
    BAD_GATEWAY("502", "网关错误"),
    SERVICE_UNAVAILABLE("503", "服务不可用"),
    GATEWAY_TIMEOUT("504", "网关超时"),

    // ========== 业务错误码 1xxx ==========
    BUSINESS_ERROR("1000", "业务处理失败"),
    VALIDATION_ERROR("1001", "数据验证失败"),
    DUPLICATE_ERROR("1002", "数据重复"),
    DATA_NOT_FOUND("1003", "数据不存在"),
    DATA_EXPIRED("1004", "数据已过期"),
    OPERATION_NOT_ALLOWED("1005", "操作不被允许"),
    OPERATION_FAILED("1006", "操作失败"),
    SYSTEM_ERROR("1007", "系统错误"),

    // ========== 用户相关错误码 2xxx ==========
    USER_NOT_FOUND("2001", "用户不存在"),
    USER_DISABLED("2002", "用户已被禁用"),
    USER_LOCKED("2003", "用户已被锁定"),
    USERNAME_EXISTS("2004", "用户名已存在"),
    EMAIL_EXISTS("2005", "邮箱已存在"),
    PHONE_EXISTS("2006", "手机号已存在"),
    PASSWORD_ERROR("2007", "密码错误"),
    PASSWORD_WEAK("2008", "密码强度不足"),
    OLD_PASSWORD_ERROR("2009", "原密码错误"),

    // ========== 认证授权错误码 3xxx ==========
    TOKEN_INVALID("3001", "Token无效"),
    TOKEN_EXPIRED("3002", "Token已过期"),
    TOKEN_MISSING("3003", "Token缺失"),
    PERMISSION_DENIED("3004", "权限不足"),
    ROLE_NOT_FOUND("3005", "角色不存在"),
    LOGIN_REQUIRED("3006", "需要登录"),
    LOGIN_FAILED("3007", "登录失败"),
    LOGOUT_FAILED("3008", "登出失败"),
    ACCOUNT_LOCKED("3009", "账户已锁定"),
    ACCOUNT_EXPIRED("3010", "账户已过期"),
    AUTH_FAILED("3011", "认证失败"),

    // ========== 文件相关错误码 4xxx ==========
    FILE_NOT_FOUND("4001", "文件不存在"),
    FILE_TOO_LARGE("4002", "文件过大"),
    FILE_TYPE_NOT_SUPPORTED("4003", "文件类型不支持"),
    FILE_UPLOAD_FAILED("4004", "文件上传失败"),
    FILE_DOWNLOAD_FAILED("4005", "文件下载失败"),

    // ========== 数据库相关错误码 5xxx ==========
    DATABASE_ERROR("5001", "数据库操作失败"),
    DATA_INTEGRITY_VIOLATION("5002", "数据完整性约束违反"),
    DUPLICATE_KEY_ERROR("5003", "主键或唯一键冲突"),
    FOREIGN_KEY_CONSTRAINT("5004", "外键约束违反"),
    CONNECTION_TIMEOUT("5005", "数据库连接超时"),

    // ========== 外部服务错误码 6xxx ==========
    EXTERNAL_SERVICE_ERROR("6001", "外部服务调用失败"),
    EXTERNAL_SERVICE_TIMEOUT("6002", "外部服务调用超时"),
    EXTERNAL_SERVICE_UNAVAILABLE("6003", "外部服务不可用"),

    // ========== 缓存相关错误码 7xxx ==========
    CACHE_ERROR("7001", "缓存操作失败"),
    CACHE_KEY_NOT_FOUND("7002", "缓存键不存在"),
    CACHE_EXPIRED("7003", "缓存已过期"),

    // ========== 消息队列相关错误码 8xxx ==========
    MESSAGE_SEND_FAILED("8001", "消息发送失败"),
    MESSAGE_CONSUME_FAILED("8002", "消息消费失败"),
    QUEUE_NOT_FOUND("8003", "队列不存在"),

    // ========== 配置相关错误码 9xxx ==========
    CONFIG_ERROR("9001", "配置错误"),
    CONFIG_NOT_FOUND("9002", "配置不存在"),
    CONFIG_INVALID("9003", "配置无效"),


    // ========== 云客服 16xxx ==========
    CS_USER_BIND_NO("16500", "用户信息未绑定"),
    QUERY_DATA_NULL("161403", "获取:%s对应数据为空"),
    PARAM_NULL_ERROR("161404", "接口传参为空:%s为空"),


    ;

    /**
     * 响应码
     */
    private final String code;

    /**
     * 响应消息
     */
    private final String msg;

    /**
     * 实现BizResultCode接口的getCode()方法
     *
     * @return 字符串类型的响应码
     */
    @Override
    public String getCode() {
        return this.code;
    }

    /**
     * 根据code获取ResultCode
     */
    public static ResultCode getByCode(Integer code) {
        if (code == null) {
            return INTERNAL_SERVER_ERROR;
        }
        return getByCode(String.valueOf(code));
    }

    /**
     * 根据字符串code获取ResultCode
     */
    public static ResultCode getByCode(String code) {
        if (code == null) {
            return INTERNAL_SERVER_ERROR;
        }
        for (ResultCode resultCode : values()) {
            if (resultCode.code.equals(code)) {
                return resultCode;
            }
        }
        return INTERNAL_SERVER_ERROR;
    }

    /**
     * 判断是否为成功码
     */
    public boolean isSuccess() {
        try {
            int codeValue = Integer.parseInt(this.code);
            return codeValue >= 200 && codeValue < 300;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 判断是否为客户端错误
     */
    public boolean isClientError() {
        try {
            int codeValue = Integer.parseInt(this.code);
            return codeValue >= 400 && codeValue < 500;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 判断是否为服务器错误
     */
    public boolean isServerError() {
        try {
            int codeValue = Integer.parseInt(this.code);
            return codeValue >= 500 && codeValue < 600;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 判断是否为业务错误
     */
    public boolean isBusinessError() {
        try {
            int codeValue = Integer.parseInt(this.code);
            return codeValue >= 1000 && codeValue < 10000;
        } catch (NumberFormatException e) {
            return false;
        }
    }
}
