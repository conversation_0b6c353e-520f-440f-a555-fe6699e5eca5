package com.tem.customer.shared.common;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;

/**
 * 统一响应结果类
 * 用于封装API接口的返回结果
 *
 * <AUTHOR>
 * @since 2025-06-19
 */
@Data
@Accessors(chain = true)
public class Result<T> implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 响应码
     */
    private String code;

    /**
     * 响应消息
     */
    private String message;

    /**
     * 响应数据
     */
    private T data;

    /**
     * 时间戳
     */
    private Long timestamp;

    /**
     * 请求追踪ID
     */
    private String traceId;

    /**
     * 私有构造函数
     */
    private Result() {
        this.timestamp = System.currentTimeMillis();
    }

    /**
     * 成功响应（无数据）
     */
    public static <T> Result<T> success() {
        return success(null);
    }

    /**
     * 成功响应（带数据）
     */
    public static <T> Result<T> success(T data) {
        return success(ResultCode.SUCCESS.getMsg(), data);
    }

    /**
     * 成功响应（自定义消息和数据）
     */
    public static <T> Result<T> success(String message, T data) {
        Result<T> result = new Result<>();
        result.setCode(ResultCode.SUCCESS.getCode())
              .setMessage(message)
              .setData(data);
        return result;
    }

    /**
     * 失败响应（使用ResultCode）
     */
    public static <T> Result<T> error(ResultCode resultCode) {
        return error(resultCode.getCode(), resultCode.getMsg());
    }

    /**
     * 失败响应（自定义错误码和消息）
     */
    public static <T> Result<T> error(String code, String message) {
        Result<T> result = new Result<>();
        result.setCode(code)
              .setMessage(message);
        return result;
    }

    /**
     * 失败响应（自定义消息，使用默认错误码）
     */
    public static <T> Result<T> error(String message) {
        return error(ResultCode.BUSINESS_ERROR.getCode(), message);
    }

    /**
     * 根据boolean值返回成功或失败
     */
    public static <T> Result<T> result(boolean success) {
        return success ? success() : error(ResultCode.OPERATION_FAILED);
    }

    /**
     * 根据boolean值返回成功或失败（带数据）
     */
    public static <T> Result<T> result(boolean success, T data) {
        return success ? success(data) : error(ResultCode.OPERATION_FAILED);
    }

    /**
     * 根据boolean值返回成功或失败（自定义消息）
     */
    public static <T> Result<T> result(boolean success, String successMessage, String errorMessage) {
        return success ? success(successMessage, null) : error(errorMessage);
    }

    /**
     * 判断是否成功
     */
    public boolean isSuccess() {
        return ResultCode.SUCCESS.getCode().equals(this.code);
    }

    /**
     * 设置追踪ID
     */
    public Result<T> traceId(String traceId) {
        this.traceId = traceId;
        return this;
    }
}
