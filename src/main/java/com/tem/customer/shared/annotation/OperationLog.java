package com.tem.customer.shared.annotation;

import com.tem.customer.shared.enums.BusinessType;
import com.tem.customer.shared.enums.OperationType;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 操作日志注解
 * 用于标记需要记录操作日志的方法
 *
 * <AUTHOR>
 * @since 2025-06-23
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface OperationLog {

    /**
     * 业务类型
     */
    BusinessType businessType() default BusinessType.OTHER;

    /**
     * 操作类型
     */
    OperationType operationType() default OperationType.OTHER;

    /**
     * 操作描述
     */
    String description() default "";

    /**
     * 是否记录请求参数
     */
    boolean recordParams() default false;

    /**
     * 是否记录返回结果
     */
    boolean recordResult() default false;

    /**
     * 是否异步记录日志
     */
    boolean async() default true;

    /**
     * 业务ID的SpEL表达式
     * 例如: "#dto.partnerId" 或 "#result.data.id"
     */
    String businessIdExpression() default "";

    /**
     * 目标企业ID的SpEL表达式
     * 例如: "#dto.partnerId" 或 "#partnerId"
     */
    String targetPartnerIdExpression() default "";
}
