package com.tem.customer.shared.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 七鱼CRM认证注解
 * 标记需要进行七鱼CRM认证的接口方法
 * 
 * <AUTHOR>
 * @since 2025-07-17
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface QiyuCrmAuth {

    /**
     * 是否必须认证
     * 
     * @return true-必须认证，false-可选认证
     */
    boolean required() default true;

    /**
     * 认证描述
     * 
     * @return 认证描述信息
     */
    String description() default "";
}
