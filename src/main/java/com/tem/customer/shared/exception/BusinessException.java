package com.tem.customer.shared.exception;

import com.tem.customer.shared.common.ResultCode;
import lombok.Getter;

import java.io.Serial;

/**
 * 业务异常类
 * 用于处理业务逻辑中的异常情况
 *
 * <AUTHOR>
 * @since 2025-06-19
 */
@Getter
public class BusinessException extends RuntimeException {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 错误码
     */
    private final String code;

    /**
     * 错误消息
     */
    private final String message;

    /**
     * 详细错误信息
     */
    private final String detail;

    /**
     * 构造函数 - 使用ResultCode
     *
     * @param resultCode 结果码枚举
     */
    public BusinessException(ResultCode resultCode) {
        super(resultCode.getMsg());
        this.code = resultCode.getCode();
        this.message = resultCode.getMsg();
        this.detail = null;
    }

    /**
     * 构造函数 - 使用ResultCode和自定义消息
     *
     * @param resultCode 结果码枚举
     * @param message    自定义错误消息
     */
    public BusinessException(ResultCode resultCode, String message) {
        super(message);
        this.code = resultCode.getCode();
        this.message = message;
        this.detail = null;
    }

    /**
     * 构造函数 - 使用ResultCode、自定义消息和详细信息
     *
     * @param resultCode 结果码枚举
     * @param message    自定义错误消息
     * @param detail     详细错误信息
     */
    public BusinessException(ResultCode resultCode, String message, String detail) {
        super(message);
        this.code = resultCode.getCode();
        this.message = message;
        this.detail = detail;
    }

    /**
     * 构造函数 - 使用ResultCode和原因异常
     *
     * @param resultCode 结果码枚举
     * @param cause      原因异常
     */
    public BusinessException(ResultCode resultCode, Throwable cause) {
        super(resultCode.getMsg(), cause);
        this.code = resultCode.getCode();
        this.message = resultCode.getMsg();
        this.detail = cause.getMessage();
    }

    /**
     * 构造函数 - 使用ResultCode、自定义消息和原因异常
     *
     * @param resultCode 结果码枚举
     * @param message    自定义错误消息
     * @param cause      原因异常
     */
    public BusinessException(ResultCode resultCode, String message, Throwable cause) {
        super(message, cause);
        this.code = resultCode.getCode();
        this.message = message;
        this.detail = cause.getMessage();
    }

    /**
     * 构造函数 - 直接使用错误码和消息
     *
     * @param code    错误码
     * @param message 错误消息
     */
    public BusinessException(String code, String message) {
        super(message);
        this.code = code;
        this.message = message;
        this.detail = null;
    }

    /**
     * 构造函数 - 使用错误码、消息和详细信息
     *
     * @param code    错误码
     * @param message 错误消息
     * @param detail  详细错误信息
     */
    public BusinessException(String code, String message, String detail) {
        super(message);
        this.code = code;
        this.message = message;
        this.detail = detail;
    }

    /**
     * 构造函数 - 使用ResultCode、自定义消息、详细信息和原因异常
     *
     * @param resultCode 结果码枚举
     * @param message    自定义错误消息
     * @param detail     详细错误信息
     * @param cause      原因异常
     */
    public BusinessException(ResultCode resultCode, String message, String detail, Throwable cause) {
        super(message, cause);
        this.code = resultCode.getCode();
        this.message = message;
        this.detail = detail;
    }

    /**
     * 创建数据不存在异常
     *
     * @param resourceName 资源名称
     * @return BusinessException
     */
    public static BusinessException dataNotFound(String resourceName) {
        return new BusinessException(ResultCode.DATA_NOT_FOUND, resourceName + "不存在");
    }

    /**
     * 创建数据重复异常
     *
     * @param resourceName 资源名称
     * @return BusinessException
     */
    public static BusinessException dataDuplicate(String resourceName) {
        return new BusinessException(ResultCode.DUPLICATE_ERROR, resourceName + "已存在");
    }

    /**
     * 创建操作不允许异常
     *
     * @param operation 操作名称
     * @return BusinessException
     */
    public static BusinessException operationNotAllowed(String operation) {
        return new BusinessException(ResultCode.OPERATION_NOT_ALLOWED, operation + "操作不被允许");
    }

    /**
     * 创建参数验证异常
     *
     * @param paramName 参数名称
     * @return BusinessException
     */
    public static BusinessException validationError(String paramName) {
        return new BusinessException(ResultCode.VALIDATION_ERROR, paramName + "参数验证失败");
    }

    /**
     * 创建通用业务异常 - 便捷方法
     *
     * @param message 错误消息
     * @return BusinessException
     */
    public static BusinessException error(String message) {
        return new BusinessException(ResultCode.BUSINESS_ERROR, message);
    }

    public static BusinessException error(ResultCode message) {
        return new BusinessException(ResultCode.BUSINESS_ERROR);
    }

    /**
     * 创建通用业务异常 - 带详细信息
     *
     * @param message 错误消息
     * @param detail  详细错误信息
     * @return BusinessException
     */
    public static BusinessException error(String message, String detail) {
        return new BusinessException(ResultCode.BUSINESS_ERROR, message, detail);
    }

    /**
     * 创建通用业务异常 - 带异常原因
     *
     * @param message 错误消息
     * @param cause   原因异常
     * @return BusinessException
     */
    public static BusinessException error(String message, Throwable cause) {
        return new BusinessException(ResultCode.BUSINESS_ERROR, message, cause);
    }

    /**
     * 创建通用业务异常 - 完整版本
     *
     * @param message 错误消息
     * @param detail  详细错误信息
     * @param cause   原因异常
     * @return BusinessException
     */
    public static BusinessException error(String message, String detail, Throwable cause) {
        return new BusinessException(ResultCode.BUSINESS_ERROR, message, detail, cause);
    }

    @Override
    public String getMessage() {
        return this.message;
    }

    @Override
    public String toString() {
        return "BusinessException{" +
                "code=" + code +
                ", message='" + message + '\'' +
                ", detail='" + detail + '\'' +
                '}';
    }
}
