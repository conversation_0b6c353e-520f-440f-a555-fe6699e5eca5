package com.tem.customer.shared.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2025-05-29
 */
@Getter
public enum RedisConnectionType {

    /**
     * S:单节点部署方式
     * SC:哨兵部署方式
     * C:集群方式
     * masterslave:主从部署方式
     */
    STANDALONE("S", "单节点部署方式"),
    SENTINEL("SC", "哨兵部署方式"),
    CLUSTER("C", "集群方式"),
    MASTERSLAVE("masterslave", "主从部署方式");

    private final String type;

    private final String msg;

    RedisConnectionType(String type, String msg) {
        this.type = type;
        this.msg = msg;
    }

    public static RedisConnectionType fromType(String type) {
        for (RedisConnectionType value : RedisConnectionType.values()) {
            if (value.getType().equals(type)) {
                return value;
            }
        }
        throw new IllegalArgumentException("unknown type: " + type);
    }

}
