package com.tem.customer.shared.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 操作类型枚举
 * 用于标识操作日志的操作类型
 *
 * <AUTHOR>
 * @since 2025-06-23
 */
@Getter
@AllArgsConstructor
public enum OperationType {

    /**
     * 新增操作
     */
    CREATE("CREATE", "新增"),

    /**
     * 修改操作
     */
    UPDATE("UPDATE", "修改"),

    /**
     * 删除操作
     */
    DELETE("DELETE", "删除"),

    /**
     * 查看操作
     */
    VIEW("VIEW", "查看"),

    /**
     * 导出操作
     */
    EXPORT("EXPORT", "导出"),

    /**
     * 导入操作
     */
    IMPORT("IMPORT", "导入"),

    /**
     * 上传操作
     */
    UPLOAD("UPLOAD", "上传"),

    /**
     * 下载操作
     */
    DOWNLOAD("DOWNLOAD", "下载"),

    /**
     * 登录操作
     */
    LOGIN("LOGIN", "登录"),

    /**
     * 登出操作
     */
    LOGOUT("LOGOUT", "登出"),

    /**
     * 其他操作
     */
    OTHER("OTHER", "其他");

    /**
     * 操作类型代码
     */
    private final String code;

    /**
     * 操作类型描述
     */
    private final String description;

    /**
     * 根据代码获取操作类型
     *
     * @param code 操作类型代码
     * @return 操作类型枚举，如果未找到则返回OTHER
     */
    public static OperationType fromCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return OTHER;
        }
        
        for (OperationType type : OperationType.values()) {
            if (type.getCode().equals(code.trim())) {
                return type;
            }
        }
        return OTHER;
    }

    /**
     * 检查是否为有效的操作类型代码
     *
     * @param code 操作类型代码
     * @return 如果是有效代码返回true，否则返回false
     */
    public static boolean isValidCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return false;
        }
        
        for (OperationType type : OperationType.values()) {
            if (type.getCode().equals(code.trim())) {
                return true;
            }
        }
        return false;
    }
}
