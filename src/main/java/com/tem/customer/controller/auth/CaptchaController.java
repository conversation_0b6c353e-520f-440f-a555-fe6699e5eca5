package com.tem.customer.controller.auth;

import cn.dev33.satoken.annotation.SaIgnore;
import com.iplatform.common.ResponseDto;
import com.iplatform.common.utils.LogUtils;
import com.tem.customer.model.dto.SendSmsCodeDTO;
import com.tem.customer.model.vo.CaptchaVO;
import com.tem.customer.service.auth.CaptchaService;
import com.tem.customer.service.auth.LoginAttemptService;
import com.tem.customer.shared.common.Result;
import com.tem.sso.api.SSOService;
import com.tem.sso.dto.VerDto;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.UUID;

/**
 * 验证码控制器
 * 提供图形验证码和短信验证码相关功能
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@SaIgnore
@RestController
@RequestMapping("/api/cx/customer/admin/auth")
public class CaptchaController {


    @Value("${customer.tem.partnerId:194}")
    private Long partnerId;

    @Value("${customer.tem.tmcId:194}")
    private Long tmcId;
    /**
     * SSO服务，用于发送短信验证码
     */
    @DubboReference(timeout = 10000, retries = 0, check = false)
    private SSOService ssoService;

    /**
     * 登录尝试管理服务
     */
    private final LoginAttemptService loginAttemptService;

    /**
     * 图形验证码服务
     */
    private final CaptchaService captchaService;

    public CaptchaController(LoginAttemptService loginAttemptService, CaptchaService captchaService) {
        this.loginAttemptService = loginAttemptService;
        this.captchaService = captchaService;
    }

    /**
     * 获取图形验证码
     *
     * @param request HTTP请求对象，用于获取客户端IP
     * @return 图形验证码信息
     */
    @GetMapping("/captcha")
    public Result<CaptchaVO> getCaptcha(HttpServletRequest request) {
        try {
            // 1. 检查获取频率限制
            String clientIp = getClientIpAddress(request);
            if (!captchaService.checkCaptchaRateLimit(clientIp)) {
                LogUtils.warn(log, "获取图形验证码频率超限: clientIp={}", clientIp);
                return Result.error("获取验证码过于频繁，请稍后再试");
            }

            // 2. 生成验证码
            String captchaKey = UUID.randomUUID().toString();
            String captchaImage = captchaService.generateCaptchaImage(captchaKey);

            CaptchaVO captchaVO = new CaptchaVO();
            captchaVO.setCaptchaKey(captchaKey);
            captchaVO.setCaptchaImage(captchaImage);
            captchaVO.setExpireTime(300); // 5分钟过期

            LogUtils.info(log, "生成图形验证码: captchaKey={}, clientIp={}", captchaKey, clientIp);

            return Result.success(captchaVO);

        } catch (Exception e) {
            LogUtils.error(log, "生成图形验证码异常", e);
            return Result.error("生成验证码失败");
        }
    }

    /**
     * 发送手机验证码
     * 在发送前验证图形验证码和账户锁定状态
     *
     * @param sendSmsCodeDTO 发送短信验证码请求参数
     * @return 发送结果
     */
    @PostMapping("/sms/send")
    public Result<Void> sendSmsCode(@Valid @RequestBody SendSmsCodeDTO sendSmsCodeDTO) {
        try {
            String mobile = sendSmsCodeDTO.getMobile();
            String captcha = sendSmsCodeDTO.getCaptcha();
            String captchaKey = sendSmsCodeDTO.getCaptchaKey();

            LogUtils.info(log, "发送手机验证码请求: mobile={}, captchaKey={}", mobile, captchaKey);

            // 1. 检查账户是否被锁定
            if (loginAttemptService.isAccountLocked(mobile)) {
                long remainingTime = loginAttemptService.getLockRemainingTime(mobile);
                String message = remainingTime > 0 ?
                        String.format("账户已锁定，请%d分钟后再试", remainingTime / 60) :
                        "账户已锁定，请稍后再试";
                LogUtils.warn(log, "发送手机验证码失败: mobile={}, reason=账户已锁定", mobile);
                return Result.error(message);
            }

            // 2. 验证图形验证码
            if (!captchaService.validateCaptcha(captchaKey, captcha)) {
                LogUtils.warn(log, "发送手机验证码失败: mobile={}, reason=图形验证码错误", mobile);
                return Result.error("图形验证码错误");
            }

            // 3. 检查发送频率限制
            CaptchaService.SmsRateLimitResult rateLimitResult = captchaService.checkSmsRateLimit(mobile);
            if (!rateLimitResult.allowed()) {
                LogUtils.warn(log, "发送手机验证码失败: mobile={}, reason={}", mobile, rateLimitResult.message());
                return Result.error(rateLimitResult.message());
            }

            // 4. 发送短信验证码
            boolean sendResult = sendSmsCodeToMobile(mobile);
            if (!sendResult) {
                LogUtils.warn(log, "发送手机验证码失败: mobile={}, reason=短信发送失败", mobile);
                return Result.error("短信发送失败，请稍后重试");
            }

            LogUtils.info(log, "发送手机验证码成功: mobile={}", mobile);
            return Result.success();

        } catch (Exception e) {
            LogUtils.error(log, "发送手机验证码异常: mobile=" + sendSmsCodeDTO.getMobile(), e);
            return Result.error("发送验证码失败");
        }
    }


    /**
     * 发送短信验证码到手机
     * 使用现有的SSO服务发送验证码
     */
    private boolean sendSmsCodeToMobile(String mobile) {
        try {
            LogUtils.info(log, "调用SSO服务发送短信验证码: mobile={}", mobile);

            // 调用SSO服务发送验证码
            VerDto build = VerDto.builder()
                    .partnerId(partnerId)
                    .tmcId(tmcId)
                    .signName(null)
                    .identifier(mobile)
                    .build();
            ResponseDto<Void> response = ssoService.sendVerCode(build);

            if (response.isSuccess()) {
                LogUtils.info(log, "SSO服务发送短信验证码成功: mobile={}", mobile);
                return true;
            } else {
                LogUtils.warn(log, "SSO服务发送短信验证码失败: mobile={}, code={}, msg={}",
                        mobile, response.getCode(), response.getMsg());
                return false;
            }

        } catch (Exception e) {
            LogUtils.error(log, "调用SSO服务发送短信验证码异常: mobile=" + mobile, e);
            return false;
        }
    }

    /**
     * 获取客户端IP地址
     * 支持代理服务器环境下的真实IP获取
     *
     * @param request HTTP请求对象
     * @return 客户端IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        // 尝试从X-Forwarded-For头获取（代理环境）
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            // X-Forwarded-For可能包含多个IP，取第一个
            return xForwardedFor.split(",")[0].trim();
        }

        // 尝试从X-Real-IP头获取（Nginx代理）
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }

        // 尝试从Proxy-Client-IP头获取
        String proxyClientIp = request.getHeader("Proxy-Client-IP");
        if (proxyClientIp != null && !proxyClientIp.isEmpty() && !"unknown".equalsIgnoreCase(proxyClientIp)) {
            return proxyClientIp;
        }

        // 尝试从WL-Proxy-Client-IP头获取（WebLogic）
        String wlProxyClientIp = request.getHeader("WL-Proxy-Client-IP");
        if (wlProxyClientIp != null && !wlProxyClientIp.isEmpty() && !"unknown".equalsIgnoreCase(wlProxyClientIp)) {
            return wlProxyClientIp;
        }

        // 最后使用getRemoteAddr()获取
        String remoteAddr = request.getRemoteAddr();

        // 处理IPv6本地地址
        if ("0:0:0:0:0:0:0:1".equals(remoteAddr) || "::1".equals(remoteAddr)) {
            return "127.0.0.1";
        }

        return remoteAddr != null ? remoteAddr : "unknown";
    }
}
