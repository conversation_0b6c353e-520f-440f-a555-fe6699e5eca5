package com.tem.customer.controller.auth;

import cn.dev33.satoken.annotation.SaIgnore;
import cn.dev33.satoken.stp.StpUtil;
import com.iplatform.common.ResponseDto;
import com.iplatform.common.router.context.TrafficTagContext;
import com.iplatform.common.router.util.TrafficUtil;
import com.iplatform.common.utils.LogUtils;
import com.tem.customer.model.dto.LoginDTO;
import com.tem.customer.model.vo.LoginVO;
import com.tem.customer.model.vo.UserInfoVO;
import com.tem.customer.service.auth.LoginAttemptService;
import com.tem.customer.shared.common.Result;
import com.tem.platform.api.UserService;
import com.tem.platform.api.dto.UserDto;
import com.tem.sso.api.SSOService;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 认证控制器
 * 提供基于Sa-Token的用户登录、注销、用户信息查询等功能
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@SaIgnore
@RestController
@RequestMapping("/api/cx/customer/admin/auth")
public class AuthController {


    @DubboReference(timeout = 10000, retries = 0, check = false)
    private UserService userService;

    @DubboReference(timeout = 10000, retries = 0, check = false)
    private SSOService ssoService;

    private final LoginAttemptService loginAttemptService;

    public AuthController(LoginAttemptService loginAttemptService) {
        this.loginAttemptService = loginAttemptService;
    }

    /**
     * 用户登录
     * 支持手机号+密码+手机验证码的登录方式
     * 图形验证码在发送短信验证码时已验证
     *
     * @param loginDTO 登录请求参数
     * @return 登录结果，包含Token信息
     */
    @PostMapping("/login")
    public Result<LoginVO> login(@Valid @RequestBody LoginDTO loginDTO) {
        String mobile = loginDTO.getMobile();

        try {
            LogUtils.info(log, "用户登录请求: mobile={}", mobile);

            // 1. 检查账户是否被锁定
            if (loginAttemptService.isAccountLocked(mobile)) {
                long remainingTime = loginAttemptService.getLockRemainingTime(mobile);
                String message = remainingTime > 0 ?
                        String.format("账户已锁定，请%d分钟后再试", remainingTime / 60) :
                        "账户已锁定，请稍后再试";
                LogUtils.warn(log, "用户登录失败: mobile={}, reason=账户已锁定", mobile);
                return Result.error(message);
            }

            // 2. 验证手机验证码
            if (!validateSmsCode(mobile, loginDTO.getSmsCode())) {
                LogUtils.warn(log, "用户登录失败: mobile={}, reason=手机验证码错误", mobile);
                return Result.error("手机验证码错误");
            }

            // 3. 验证密码
            ResponseDto<UserDto> userDtoResponseDto = userService.verifyUser(mobile, loginDTO.getPassword(), 1);
            if (userDtoResponseDto.isNotSuccess() || null == userDtoResponseDto.getData()) {
                // 记录密码错误
                loginAttemptService.recordLoginFailure(mobile);

                int remainingAttempts = loginAttemptService.getRemainingAttempts(mobile);
                String errorMessage = remainingAttempts > 0 ?
                        String.format("密码错误，还可尝试%d次", remainingAttempts) :
                        "密码错误次数过多，账户已锁定2小时";

                LogUtils.warn(log, "用户登录失败: mobile={}, reason=密码错误, remainingAttempts={}",
                        mobile, remainingAttempts);
                return Result.error(errorMessage);
            }
            UserDto user = userDtoResponseDto.getData();

            // 5. 登录成功，清除失败记录
            loginAttemptService.clearLoginFailures(mobile);

            // 6. Sa-Token登录
            Long userId = user.getId();
            StpUtil.login(userId);


            // 7. 构建登录响应
            LoginVO loginVO = buildLoginVO(user);
            //补充在途版本信息
            TrafficUtil.routeByUserId(userId);
            String tag = TrafficTagContext.getTag();
            loginVO.setZaituVer(tag);

            LogUtils.info(log, "用户登录成功: userId={}, mobile={}, partnerId={}",
                    userId, mobile, user.getPartnerId());

            return Result.success(loginVO);

        } catch (Exception e) {
            LogUtils.error(log, "用户登录异常: mobile=" + mobile, e);
            return Result.error("登录失败，请稍后重试");
        }
    }

    /**
     * 用户注销
     *
     * @return 注销结果
     */
    @PostMapping("/logout")
    public Result<Void> logout() {
        try {
            Object loginId = StpUtil.getLoginIdDefaultNull();

            // Sa-Token注销
            StpUtil.logout();


            LogUtils.info(log, "用户注销成功: userId={}", loginId);
            return Result.success();

        } catch (Exception e) {
            LogUtils.error(log, "用户注销异常", e);
            return Result.error("注销失败");
        }
    }

    /**
     * 获取当前登录用户信息
     *
     * @return 用户信息
     */
    @GetMapping("/userInfo")
    public Result<UserInfoVO> getUserInfo() {
        try {
            // 检查Sa-Token登录状态
            if (!StpUtil.isLogin()) {
                return Result.error("用户未登录");
            }

            Long userId = StpUtil.getLoginIdAsLong();

            // 从Dubbo服务获取用户详细信息
            ResponseDto<UserDto> userResponse = userService.getUser(userId);
            if (userResponse.isNotSuccess() || userResponse.getData() == null) {
                return Result.error("获取用户信息失败");
            }

            UserDto userDto = userResponse.getData();
            UserInfoVO userInfoVO = buildUserInfoVO(userDto);

            return Result.success(userInfoVO);

        } catch (Exception e) {
            LogUtils.error(log, "获取用户信息异常", e);
            return Result.error("获取用户信息失败");
        }
    }

    /**
     * 检查登录状态
     *
     * @return 登录状态
     */
    @GetMapping("/checkLogin")
    public Result<Boolean> checkLogin() {
        boolean isLogin = StpUtil.isLogin();
        return Result.success(isLogin);
    }

    /**
     * 获取Token信息
     *
     * @return Token详细信息
     */
    @GetMapping("/tokenInfo")
    public Result<Object> getTokenInfo() {
        if (!StpUtil.isLogin()) {
            return Result.error("用户未登录");
        }
        return Result.success(StpUtil.getTokenInfo());
    }


    /**
     * 构建登录响应VO
     */
    private LoginVO buildLoginVO(UserDto userDto) {
        LoginVO loginVO = new LoginVO();
        loginVO.setAccessToken(StpUtil.getTokenValue());
        loginVO.setTokenType("Bearer");
        loginVO.setExpiresIn(StpUtil.getTokenTimeout());
        loginVO.setUserId(userDto.getId());
        loginVO.setUsername(userDto.getUsername());
        loginVO.setPartnerId(userDto.getPartnerId());
        loginVO.setLoginTime(System.currentTimeMillis());
        return loginVO;
    }

    /**
     * 构建用户信息VO
     */
    private UserInfoVO buildUserInfoVO(UserDto userDto) {
        UserInfoVO userInfoVO = new UserInfoVO();
        userInfoVO.setUserId(userDto.getId());
        userInfoVO.setUsername(userDto.getUsername());
        userInfoVO.setEmail(userDto.getEmail());
        userInfoVO.setMobile(userDto.getMobile());
        userInfoVO.setPartnerId(userDto.getPartnerId());
        userInfoVO.setLoginTime(System.currentTimeMillis());
        userInfoVO.setLastActiveTime(System.currentTimeMillis());


        return userInfoVO;
    }


    /**
     * 验证手机验证码
     * 使用SSO服务验证短信验证码
     *
     * @param mobile  手机号
     * @param smsCode 手机验证码
     * @return 验证结果
     */
    private boolean validateSmsCode(String mobile, String smsCode) {
        try {
            if (StringUtils.isBlank(mobile) || StringUtils.isBlank(smsCode)) {
                return false;
            }

            LogUtils.info(log, "调用SSO服务验证手机验证码: mobile={}, smsCode={}", mobile, smsCode);

            // 调用SSO服务验证短信验证码
            ResponseDto<Void> response = ssoService.authVerCode(mobile, smsCode);

            if (response.isSuccess()) {
                LogUtils.info(log, "SSO服务验证手机验证码成功: mobile={}", mobile);
                return true;
            } else {
                LogUtils.warn(log, "SSO服务验证手机验证码失败: mobile={}, code={}, msg={}",
                        mobile, response.getCode(), response.getMsg());
                return false;
            }

        } catch (Exception e) {
            LogUtils.error(log, "调用SSO服务验证手机验证码异常: mobile=" + mobile, e);
            return false;
        }
    }
}
