package com.tem.customer.controller.system;

import com.tem.customer.shared.common.Result;
import com.tem.customer.shared.common.ResultCode;
import com.tem.customer.service.system.ImageService;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 图片上传控制器
 * {@code @Author:} fumouren
 * {@code @CreateTime:} 2025-06-20 11:31
 * {@code @Description:} 提供图片上传功能的API接口
 */
@Slf4j
@RestController
@RequestMapping("/api/cx/customer/admin/upload")
@RequiredArgsConstructor
@Validated
public class ImageController {

    private final ImageService imageService;


    @Value("${tem.imgserver.host}")
    private String temImgServerHost;

    /**
     * 上传图片
     *
     * @param file 图片文件
     * @return 上传结果，包含文件访问信息
     */
    @PostMapping("/image")
    public Result<Map<String, Object>> uploadImage(@RequestParam("file") @NotNull MultipartFile file) {
        try {
            // 基本文件验证
            if (file.isEmpty()) {
                return Result.error(ResultCode.BAD_REQUEST.getCode(), "上传文件不能为空");
            }

            // 文件类型验证
            String contentType = file.getContentType();
            if (contentType == null || !contentType.startsWith("image/")) {
                return Result.error(ResultCode.FILE_TYPE_NOT_SUPPORTED.getCode(), "只支持图片文件上传");
            }

            // 文件大小验证（10MB限制）
            long maxSize = 10 * 1024 * 1024; // 10MB
            if (file.getSize() > maxSize) {
                return Result.error(ResultCode.FILE_TOO_LARGE.getCode(), "文件大小不能超过10MB");
            }

            // 调用服务层上传图片
            String fileKey = imageService.uploadImage(file);

            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            String fileUrl = temImgServerHost + fileKey;
            result.put("fileUrl", fileUrl);
            result.put("fileName", file.getOriginalFilename());
            result.put("fileSize", file.getSize());
            result.put("contentType", contentType);

            return Result.success("图片上传成功", result);

        } catch (IOException e) {
            log.error("图片上传IO异常，文件名: {}", file.getOriginalFilename(), e);
            return Result.error(ResultCode.FILE_UPLOAD_FAILED.getCode(), "图片上传失败: " + e.getMessage());
        } catch (IllegalArgumentException e) {
            log.warn("图片上传参数错误，文件名: {}, 错误: {}", file.getOriginalFilename(), e.getMessage());
            return Result.error(ResultCode.BAD_REQUEST.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("图片上传异常，文件名: {}", file.getOriginalFilename(), e);
            return Result.error(ResultCode.FILE_UPLOAD_FAILED.getCode(), "图片上传失败，请稍后重试");
        }
    }

    /**
     * 批量上传图片
     *
     * @param files 图片文件列表，最多9张
     * @return 批量上传结果，包含成功和失败的文件信息
     */
    @PostMapping("/images")
    public Result<Map<String, Object>> uploadImages(@RequestParam("files") @NotNull List<MultipartFile> files) {
        try {
            // 验证文件数量
            if (files.isEmpty()) {
                return Result.error(ResultCode.BAD_REQUEST.getCode(), "上传文件列表不能为空");
            }

            if (files.size() > 9) {
                return Result.error(ResultCode.BAD_REQUEST.getCode(), "一次最多只能上传9张图片");
            }

            // 预验证所有文件
            List<String> validationErrors = new ArrayList<>();
            for (int i = 0; i < files.size(); i++) {
                MultipartFile file = files.get(i);
                String error = validateSingleFile(file, i + 1);
                if (error != null) {
                    validationErrors.add(error);
                }
            }

            // 如果有验证错误，直接返回
            if (!validationErrors.isEmpty()) {
                return Result.error(ResultCode.BAD_REQUEST.getCode(),
                    "文件验证失败: " + String.join("; ", validationErrors));
            }

            // 调用服务层批量上传
            Map<String, Object> uploadResult = imageService.uploadImages(files);

            // 处理返回结果，添加完整的URL信息
            processUploadResult(uploadResult);

            // 根据上传结果确定响应
            int successCount = (Integer) uploadResult.get("successCount");
            int failCount = (Integer) uploadResult.get("failCount");
            int totalCount = (Integer) uploadResult.get("totalCount");

            String message;
            if (failCount == 0) {
                message = String.format("批量上传成功，共上传%d张图片", successCount);
            } else if (successCount == 0) {
                message = String.format("批量上传失败，%d张图片均上传失败", totalCount);
            } else {
                message = String.format("批量上传完成，成功%d张，失败%d张", successCount, failCount);
            }

            return Result.success(message, uploadResult);

        } catch (IllegalArgumentException e) {
            log.warn("批量图片上传参数错误: {}", e.getMessage());
            return Result.error(ResultCode.BAD_REQUEST.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("批量图片上传异常", e);
            return Result.error(ResultCode.FILE_UPLOAD_FAILED.getCode(), "批量图片上传失败，请稍后重试");
        }
    }

    /**
     * 验证单个文件
     */
    private String validateSingleFile(MultipartFile file, int index) {
        if (file == null || file.isEmpty()) {
            return String.format("第%d个文件为空", index);
        }

        // 文件类型验证
        String contentType = file.getContentType();
        if (contentType == null || !contentType.startsWith("image/")) {
            return String.format("第%d个文件(%s)不是图片格式", index, file.getOriginalFilename());
        }

        // 文件大小验证（10MB限制）
        long maxSize = 10 * 1024 * 1024; // 10MB
        if (file.getSize() > maxSize) {
            return String.format("第%d个文件(%s)大小超过10MB限制", index, file.getOriginalFilename());
        }

        return null;
    }

    /**
     * 处理上传结果，为成功的文件添加完整URL
     */
    @SuppressWarnings("unchecked")
    private void processUploadResult(Map<String, Object> uploadResult) {
        List<Map<String, Object>> successFiles = (List<Map<String, Object>>) uploadResult.get("successFiles");

        if (successFiles != null) {
            for (Map<String, Object> fileInfo : successFiles) {
                String fileKey = (String) fileInfo.get("fileKey");
                if (fileKey != null) {
                    String fileUrl = temImgServerHost + fileKey;
                    fileInfo.put("fileUrl", fileUrl);
                }
            }
        }
    }
}
