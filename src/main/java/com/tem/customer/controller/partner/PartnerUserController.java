package com.tem.customer.controller.partner;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.iplatform.common.Config;
import com.iplatform.common.ResponseDto;
import com.iplatform.common.utils.LogUtils;
import com.tem.customer.model.convert.UserListConverter;
import com.tem.customer.model.vo.common.SpecialLoginVO;
import com.tem.customer.model.vo.common.UserListResponseVO;
import com.tem.customer.model.vo.common.UserListVO;
import com.tem.customer.model.vo.common.UserOrderInfoVO;
import com.tem.customer.service.partner.PartnerUserService;
import com.tem.customer.shared.common.Constant;
import com.tem.customer.shared.common.Result;
import com.tem.customer.shared.common.ResultCode;
import com.tem.customer.shared.exception.BusinessException;
import com.tem.customer.shared.utils.UserContextUtil;
import com.tem.otapub.share.api.ProvidersRelationshipService;
import com.tem.otapub.share.dto.ProvidersRelationshipDto;
import com.tem.platform.api.ConfigService;
import com.tem.platform.api.PartnerService;
import com.tem.platform.api.PermissionService;
import com.tem.platform.api.UserService;
import com.tem.platform.api.dto.PartnerChannelConfigDto;
import com.tem.platform.api.dto.UserBaseInfo;
import com.tem.platform.api.dto.UserDto;
import com.tem.pss.api.ServerSkillService;
import com.tem.pss.dto.serverSkill.ServerSkillResponseDto;
import com.tem.sso.api.SSOService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.util.UriComponentsBuilder;

import javax.validation.constraints.NotNull;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 企业用户控制器
 *
 * <AUTHOR>
 * @since 2025-07-04
 */
@Slf4j
@RestController
@RequestMapping("/api/cx/customer/admin/partner-users")
@RequiredArgsConstructor
@Validated
public class PartnerUserController {

    private final PartnerUserService partnerUserService;

    @DubboReference(timeout = 10000, retries = 0, check = false)
    private ProvidersRelationshipService providersRelationshipService;

    @DubboReference(timeout = 10000, retries = 0, check = false)
    private UserService userService;

    @DubboReference(timeout = 10000, retries = 0, check = false)
    private ServerSkillService serverSkillService;

    @DubboReference(timeout = 10000, retries = 0, check = false)
    private PartnerService partnerService;

    @DubboReference(timeout = 10000, retries = 0, check = false)
    private ConfigService configService;

    @DubboReference(timeout = 10000, retries = 0, check = false)
    private SSOService ssoService;

    @DubboReference(timeout = 10000, retries = 0, check = false)
    private PermissionService permissionService;


    /**
     * 查询企业用户列表
     *
     * <p>功能说明：</p>
     * <ul>
     *   <li>根据关键字搜索企业用户信息</li>
     *   <li>获取用户的备注数量统计</li>
     *   <li>检查当前用户的操作权限</li>
     *   <li>返回完整的用户列表和权限信息</li>
     * </ul>
     *
     * @param keyword 关键字搜索（可选），支持用户姓名、手机号、邮箱模糊搜索
     * @return 包含用户列表、权限信息和服务技能的完整响应
     */
    @GetMapping("/search")
    public Result<UserListResponseVO> listUsers(@RequestParam() String keyword) {
        try {
            LogUtils.info(log, "开始查询企业用户列表，关键字: {}", keyword);
            if (StringUtils.isBlank(keyword)) {
                LogUtils.info(log, "关键字为空，直接返回空列表");
                return Result.success(new UserListResponseVO());
            }

            // 获取当前用户的企业ID（TMC用户）
            Long tmcId = UserContextUtil.getCurrentUserPartnerId();

            // 获取企业关系信息并解析
            RelationshipInfo relationshipInfo = getRelationshipInfo(tmcId);

            // 搜索用户信息
            List<UserBaseInfo> userList = searchUsers(tmcId, relationshipInfo, keyword);

            // 转换为VO对象并设置VIP标识
            List<UserListVO> userVoList = convertToUserVoList(userList);

            // 填充备注数量信息
            fillRemarkCounts(userVoList);

            // 构建完整响应
            UserListResponseVO response = buildUserListResponse(userVoList, tmcId);

            LogUtils.info(log, "企业用户列表查询完成，关键字: {}, 返回用户数量: {}, 响应: {}", keyword, userVoList.size(), response);
            return Result.success(response);

        } catch (BusinessException e) {
            LogUtils.warn(log, "查询企业用户列表业务异常: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            LogUtils.error(log, "查询企业用户列表系统异常，关键字: {}", keyword, e);
            throw BusinessException.error("查询用户列表失败");
        }
    }


    /**
     * 代客登录
     *
     * <p>功能说明：</p>
     * <ul>
     *   <li>验证用户信息和参数有效性</li>
     *   <li>获取用户基础信息和渠道配置</li>
     *   <li>生成登录令牌和相关URL</li>
     *   <li>构建完整的登录响应信息</li>
     * </ul>
     *
     * @param partnerId 企业ID，必填
     * @param userId    用户ID，必填
     * @param h5        是否H5版本，可选，默认false
     * @param service   服务URL，可选，用于手工录单
     * @return 代客登录响应信息
     */
    @RequestMapping("/special/login")
    public Result<SpecialLoginVO> specialLogin(Long partnerId, Long userId, Boolean h5, String service) {
        try {
            LogUtils.info(log, "开始代客登录处理，企业ID: {}, 用户ID: {}, H5模式: {}, 服务URL: {}",
                    partnerId, userId, h5, service);

            // 参数验证
            validateSpecialLoginParams(partnerId, userId);

            // 获取用户信息
            ResponseDto<UserDto> userResponse = getUserBaseInfoPartnerId(partnerId, userId);
            if (userResponse == null || !userResponse.isSuccess() || userResponse.getData() == null) {
                LogUtils.warn(log, "获取用户信息失败，企业ID: {}, 用户ID: {}, 响应: {}", partnerId, userId, userResponse);
                throw BusinessException.error("用户信息不存在");
            }
            UserDto userInfo = userResponse.getData();

            // 获取渠道配置
            PartnerChannelConfigDto channelConfig = null;
            try {
                ResponseDto<PartnerChannelConfigDto> channelResponse = getPartnerChannelConfigDto(userInfo.getPartnerId());
                if (channelResponse != null && channelResponse.isSuccess()) {
                    channelConfig = channelResponse.getData();
                }
            } catch (Exception e) {
                LogUtils.warn(log, "获取渠道配置异常，企业ID: {}", userInfo.getPartnerId(), e);
                // 渠道配置异常不影响主流程，继续执行
            }

            // 构建登录响应
            SpecialLoginVO loginResponse = buildSpecialLoginResponse(userInfo, channelConfig, h5, service);

            LogUtils.info(log, "代客登录处理完成，用户ID: {}, 用户姓名: {}, 企业ID: {}",
                    userId, userInfo.getFullname(), userInfo.getPartnerId());

            return Result.success(loginResponse);

        } catch (BusinessException e) {
            LogUtils.warn(log, "代客登录业务异常，企业ID: {}, 用户ID: {}, 错误: {}", partnerId, userId, e.getMessage());
            throw e;
        } catch (Exception e) {
            LogUtils.error(log, "代客登录系统异常，企业ID: {}, 用户ID: {}", partnerId, userId, e);
            throw BusinessException.error("代客登录处理失败");
        }
    }

    /**
     * 查询用户订单信息
     *
     * <p>功能说明：</p>
     * <ul>
     *   <li>获取指定用户的订单信息列表</li>
     *   <li>查询近3个月的订单数据</li>
     *   <li>过滤已取消和无效订单</li>
     *   <li>按业务类型处理订单详情</li>
     *   <li>按出行时间倒序排序，最多返回20条</li>
     * </ul>
     *
     * @param partnerId 企业ID，必填
     * @param userId    用户ID，必填
     * @return 用户订单信息列表
     */
    @GetMapping("/orders")
    public Result<List<UserOrderInfoVO>> getUserOrders(@RequestParam @NotNull Long partnerId,
                                                       @RequestParam @NotNull Long userId) {
        try {
            LogUtils.info(log, "开始查询用户订单信息，企业ID: {}, 用户ID: {}", partnerId, userId);

            // 调用Service层处理业务逻辑
            List<UserOrderInfoVO> orderList = partnerUserService.getUserOrders(partnerId, userId);

            LogUtils.info(log, "用户订单信息查询完成，企业ID: {}, 用户ID: {}, 返回订单数量: {}",
                    partnerId, userId, orderList.size());
            return Result.success(orderList);

        } catch (BusinessException e) {
            LogUtils.warn(log, "查询用户订单信息业务异常，企业ID: {}, 用户ID: {}, 错误: {}", partnerId, userId, e.getMessage());
            throw e;
        } catch (Exception e) {
            LogUtils.error(log, "查询用户订单信息系统异常，企业ID: {}, 用户ID: {}", partnerId, userId, e);
            throw BusinessException.error("查询用户订单信息失败");
        }
    }


    /**
     * 获取企业关系信息
     *
     * @param tmcId 企业ID
     * @return 关系信息对象
     */
    private RelationshipInfo getRelationshipInfo(Long tmcId) {
        List<ProvidersRelationshipDto> relationships = providersRelationshipService
                .getRelationshipsByTmcId(tmcId).getData();

        if (CollectionUtils.isEmpty(relationships)) {
            LogUtils.info(log, "获取企业关系信息，tmcId: {}, 关系数量: {}", tmcId, 0);
            return new RelationshipInfo(Collections.emptyList(), Collections.emptyList());
        }

        List<Long> partnerIds = relationships.stream()
                .filter(relationship -> relationship.getGrade() == Constant.PartnerUser.RelationshipGrade.PARTNER_LEVEL)
                .map(ProvidersRelationshipDto::getScope)
                .map(Long::parseLong)
                .collect(Collectors.toList());

        List<String> channels = relationships.stream()
                .filter(relationship -> relationship.getGrade() == Constant.PartnerUser.RelationshipGrade.CHANNEL_LEVEL)
                .map(ProvidersRelationshipDto::getScope)
                .collect(Collectors.toList());

        LogUtils.info(log, "获取企业关系信息，tmcId: {}, 关系数量: {}", tmcId, relationships.size());
        return new RelationshipInfo(partnerIds, channels);
    }

    /**
     * 搜索用户信息
     *
     * @param tmcId            企业ID
     * @param relationshipInfo 关系信息
     * @param keyword          搜索关键字
     * @return 用户基础信息列表
     */
    private List<UserBaseInfo> searchUsers(Long tmcId, RelationshipInfo relationshipInfo, String keyword) {
        LogUtils.info(log, "搜索用户信息，tmcId: {}, 企业IDs: {}, 渠道: {}, 关键字: {}", tmcId, relationshipInfo.partnerIds(),
                relationshipInfo.channels(), keyword);
        try {
            ResponseDto<List<UserBaseInfo>> response = userService.searchUser(relationshipInfo.channels(), tmcId, relationshipInfo.partnerIds(), keyword);
            if (response == null || !response.isSuccess() || response.getData() == null) {
                LogUtils.warn(log, "搜索用户信息失败，响应: {}", response);
                return Collections.emptyList();
            }
            List<UserBaseInfo> userList = response.getData();

            LogUtils.info(log, "搜索用户成功，返回用户数量: {}, 用户列表: {}", userList != null ? userList.size() : 0, userList);
            return userList != null ? userList : Collections.emptyList();
        } catch (Exception e) {
            LogUtils.warn(log, "搜索用户信息失败", e);
            return Collections.emptyList();
        }
    }

    /**
     * 转换为VO对象列表并设置VIP标识
     * 使用MapStruct进行类型安全的转换
     *
     * @param userList 用户基础信息列表
     * @return 用户VO列表
     */
    private List<UserListVO> convertToUserVoList(List<UserBaseInfo> userList) {
        LogUtils.info(log, "开始转换用户列表，原始数据数量: {}", userList != null ? userList.size() : 0);

        if (userList == null || userList.isEmpty()) {
            return Collections.emptyList();
        }

        // 打印原始数据的详细信息
        LogUtils.info(log, "原始用户数据示例: {}", userList.getFirst());

        List<UserListVO> userVoList;
        // 使用MapStruct进行转换，处理类型不匹配问题
        userVoList = UserListConverter.INSTANCE.toUserListVOList(userList);
        LogUtils.info(log, "MapStruct转换成功");

        // 打印转换后的数据
        LogUtils.info(log, "转换后用户列表数量: {}", userVoList != null ? userVoList.size() : 0);
        if (userVoList != null && !userVoList.isEmpty()) {
            LogUtils.info(log, "转换后用户数据示例: {}", userVoList.getFirst());
        }

        return userVoList;
    }

    /**
     * 手动转换UserBaseInfo列表为UserListVO列表
     * 用于处理MapStruct和TransformUtils都转换失败的情况
     *
     * @param userList 用户基础信息列表
     * @return 用户VO列表
     */
    private List<UserListVO> manualConvertToUserVoList(List<UserBaseInfo> userList) {
        LogUtils.info(log, "开始手动转换用户列表，数量: {}", userList != null ? userList.size() : 0);

        if (userList == null || userList.isEmpty()) {
            return Collections.emptyList();
        }

        List<UserListVO> userVoList = new ArrayList<>();

        for (UserBaseInfo userBaseInfo : userList) {
            try {
                UserListVO userVo = new UserListVO();

                // 基础字段映射
                userVo.setUserId(userBaseInfo.getUserId());
                userVo.setUserName(userBaseInfo.getUserName());
                userVo.setMobile(userBaseInfo.getMobile());
                userVo.setEmail(userBaseInfo.getEmail());
                userVo.setPartnerId(userBaseInfo.getPartnerId());
                userVo.setDeptPath(userBaseInfo.getDeptPath());
                userVo.setType(userBaseInfo.getType());
                userVo.setStatus(userBaseInfo.getStatus());
                userVo.setVipLevel(userBaseInfo.getVipLevel());

                // 处理vip字段的类型转换：boolean -> Integer
                userVo.setVip(userBaseInfo.isVip() ? 1 : 0);

                userVoList.add(userVo);

                LogUtils.debug(log, "手动转换用户成功: userId={}, userName={}, vip={}",
                        userVo.getUserId(), userVo.getUserName(), userVo.getVip());

            } catch (Exception e) {
                LogUtils.error(log, "手动转换用户失败: {}", userBaseInfo, e);
                // 继续处理下一个用户，不中断整个转换过程
            }
        }

        LogUtils.info(log, "手动转换完成，成功转换用户数量: {}", userVoList.size());
        return userVoList;
    }

    /**
     * 填充备注数量信息
     *
     * @param userVoList 用户VO列表
     */
    private void fillRemarkCounts(List<UserListVO> userVoList) {
        if (CollectionUtils.isEmpty(userVoList)) {
            return;
        }

        List<Long> partnerIds = userVoList.stream()
                .map(UserListVO::getPartnerId)
                .distinct()
                .collect(Collectors.toList());

        List<Long> userIds = userVoList.stream()
                .map(UserListVO::getUserId)
                .collect(Collectors.toList());

        LogUtils.info(log, "获取备注数量，企业IDs: {}, 用户IDs: {}", partnerIds, userIds);

        Map<Long, Integer> partnerTagCounts = getCountPartnerTag(partnerIds);
        Map<Long, Integer> userTagCounts = getCountUserTag(userIds);

        // 填充备注数量
        userVoList.forEach(userVo -> {
            if (partnerTagCounts != null) {
                userVo.setPartnerCount(partnerTagCounts.get(userVo.getPartnerId()));
            }
            if (userTagCounts != null) {
                userVo.setUserCount(userTagCounts.get(userVo.getUserId()));
            }
        });
    }

    /**
     * 构建完整的用户列表响应
     *
     * @param userVoList 用户VO列表
     * @param tmcId      企业ID
     * @return 完整响应对象
     */
    private UserListResponseVO buildUserListResponse(List<UserListVO> userVoList, Long tmcId) {
        UserListResponseVO response = new UserListResponseVO();
        response.setUserList(userVoList);

        // 获取当前用户信息
        Long currentUserId = UserContextUtil.getCurrentUserId();
        String currentUserFullname = UserContextUtil.getCurrentUserFullname();

        // 获取服务技能信息
        ResponseDto<ServerSkillResponseDto> serverSkillResponse = findServerSkill(currentUserId);
        ServerSkillResponseDto serverSkillDto = null;
        if (serverSkillResponse != null && serverSkillResponse.isSuccess()) {
            serverSkillDto = serverSkillResponse.getData();
        }
        UserListResponseVO.ServerSkillVO serverSkillVo = new UserListResponseVO.ServerSkillVO(
                serverSkillDto, currentUserId, tmcId, currentUserFullname);

        // 检查权限
        ResponseDto<Integer> integerResponseDto = permissionService.checkTrans(currentUserId, Constant.PartnerUser.Permission.SERVICE_ORDER_RESOURCE);

        ResponseDto<Integer> manualOrderResponse = permissionService.checkTrans(currentUserId, Constant.PartnerUser.Permission.SERVICE_ORDER_RESOURCE);

        Integer customerProxyPermission = (integerResponseDto != null && integerResponseDto.isSuccess()) ? integerResponseDto.getData() : null;
        Integer manualOrderPermission = (manualOrderResponse != null && manualOrderResponse.isSuccess()) ? manualOrderResponse.getData() : null;
        LogUtils.info(log, "权限检查完成，代客下单权限: {}, 手工录单权限: {}", customerProxyPermission, manualOrderPermission);

        // 设置响应数据
        response.setCustomerProxyKill(customerProxyPermission);
        response.setManualKill(manualOrderPermission);
        response.setServerSkill(serverSkillVo);
        response.setWww(Config.getString("tem.root"));

        return response;
    }

    /**
     * 根据客服ID查询技能信息
     *
     * @param userId 用户ID
     * @return 服务技能响应
     */
    private ResponseDto<ServerSkillResponseDto> findServerSkill(Long userId) {
        try {
            return serverSkillService.findServerSkill(userId);
        } catch (Exception e) {
            LogUtils.warn(log, "获取服务技能信息失败", e);
            return ResponseDto.error(ResultCode.INTERNAL_SERVER_ERROR);
        }
    }


    /**
     * 获取企业备注数量统计
     *
     * @param partnerIds 企业ID列表
     * @return 企业ID与备注数量的映射，获取失败时返回null
     */
    private Map<Long, Integer> getCountPartnerTag(List<Long> partnerIds) {
        if (CollectionUtils.isEmpty(partnerIds)) {
            return Collections.emptyMap();
        }

        try {
            Map<Long, Integer> result = partnerService.countPartnerTag(partnerIds).getData();
            LogUtils.debug(log, "获取企业备注数量成功，企业数量: {}", partnerIds.size());
            return result;
        } catch (Exception e) {
            LogUtils.warn(log, "获取企业备注数量失败", e);
            return null;
        }
    }

    /**
     * 获取用户备注数量统计
     *
     * @param userIds 用户ID列表
     * @return 用户ID与备注数量的映射，获取失败时返回null
     */
    private Map<Long, Integer> getCountUserTag(List<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return Collections.emptyMap();
        }

        try {
            Map<Long, Integer> result = userService.countUserTag(userIds).getData();
            LogUtils.debug(log, "获取用户备注数量成功，用户数量: {}", userIds.size());
            return result;
        } catch (Exception e) {
            LogUtils.warn(log, "获取用户备注数量失败", e);
            return null;
        }
    }

    /**
     * 验证代客登录参数
     *
     * @param partnerId 企业ID
     * @param userId    用户ID
     */
    private void validateSpecialLoginParams(Long partnerId, Long userId) {
        if (partnerId == null) {
            throw BusinessException.error("企业ID不能为空");
        }
        if (userId == null) {
            throw BusinessException.error("用户ID不能为空");
        }
    }


    /**
     * 构建代客登录响应
     *
     * @param userInfo      用户信息
     * @param channelConfig 渠道配置
     * @param h5            是否H5模式
     * @param service       服务URL
     * @return 登录响应对象
     */
    private SpecialLoginVO buildSpecialLoginResponse(UserDto userInfo, PartnerChannelConfigDto channelConfig,
                                                     Boolean h5, String service) {
        // 获取配置URL
        String loginUrl = Config.getString("loginUrl");
        String serviceUrl = Config.getString("autoLoginService");

        // 获取登出相关URL
        String[] logoutUrls = getLogoutUrls();
        List<String> logoutRedirectUrls = getLogoutRedirectUrls();

        // 处理登出重定向URL
        Long userId = userInfo.getId();
        if (CollectionUtils.isNotEmpty(logoutRedirectUrls)) {
            logoutRedirectUrls = logoutRedirectUrls.stream()
                    .map(url -> url + "?" + Constant.PartnerUser.SpecialLogin.USER_ID_PARAM + "=" + userId)
                    .collect(Collectors.toList());
        }

        // 获取当前客服用户ID
        Long currentUserId = UserContextUtil.getCurrentUserId();

        LogUtils.info(log, "构建登录响应，客服ID: {}, 用户ID: {}, 用户姓名: {}, 企业ID: {}, 渠道配置: {}",
                currentUserId, userId, userInfo.getFullname(), userInfo.getPartnerId(), channelConfig);

        // 生成登录令牌
        JSONObject tokenData = new JSONObject();
        tokenData.put(Constant.PartnerUser.SpecialLogin.SERVICE_ID_DATA_KEY, currentUserId);
        String authToken = getLoginAuthToken(userId, tokenData);
        if (StringUtils.isBlank(authToken)) {
            LogUtils.warn(log, "生成登录令牌为空，用户ID: {}, 客服ID: {}", userId, currentUserId);
            throw BusinessException.error("获取登录令牌失败");
        }

        // 获取H5域名
        String h5Url = Optional.ofNullable(configService.getPartnerChannelConfigDto(userInfo.getPartnerId()))
                .map(ResponseDto::getData)
                .map(PartnerChannelConfigDto::getDomainH5)
                .filter(StringUtils::isNotEmpty)
                .map(domain -> "https://" + domain)
                .orElse(Constant.PartnerUser.SpecialLogin.DEFAULT_H5_DOMAIN);

        // 构建自动登录URL
        String targetLoginUrl = BooleanUtils.isTrue(h5) ?
                UriComponentsBuilder.fromUriString(h5Url).path(Constant.PartnerUser.SpecialLogin.H5_LOGIN_PATH).toUriString() : loginUrl;
        String finalServiceUrl = StringUtils.isNotBlank(service) ? service : serviceUrl;
        String autoLoginUrl = getAutoLoginUrl(logoutRedirectUrls, targetLoginUrl, authToken, finalServiceUrl);

        return SpecialLoginVO.success(authToken, loginUrl, finalServiceUrl, logoutUrls, h5Url, autoLoginUrl);
    }

    /**
     * 关系信息内部类
     * 用于封装企业关系数据
     */
    private record RelationshipInfo(List<Long> partnerIds, List<String> channels) {

        private RelationshipInfo(List<Long> partnerIds, List<String> channels) {
            this.partnerIds = partnerIds != null ? partnerIds : Collections.emptyList();
            this.channels = channels != null ? channels : Collections.emptyList();
        }
    }


    /**
     * 根据企业ID和用户ID获取用户基础信息
     *
     * @param partnerId 企业ID
     * @param userId    用户ID
     * @return 用户信息响应
     */
    public ResponseDto<UserDto> getUserBaseInfoPartnerId(Long partnerId, Long userId) {
        try {
            if (userId == null) {
                LogUtils.warn(log, "获取用户基础信息失败，用户ID为空，企业ID: {}", partnerId);
                return ResponseDto.error(ResultCode.BAD_REQUEST);
            }

            ResponseDto<UserDto> response = userService.getUserBaseInfo(partnerId, userId);
            LogUtils.debug(log, "获取用户基础信息响应，企业ID: {}, 用户ID: {}, 成功: {}",
                    partnerId, userId, response != null && response.isSuccess());
            return response;
        } catch (Exception e) {
            LogUtils.error(log, "获取用户基础信息异常，企业ID: {}, 用户ID: {}", partnerId, userId, e);
            return ResponseDto.error(ResultCode.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 根据企业ID获取渠道配置信息
     *
     * @param partnerId 企业ID
     * @return 渠道配置响应
     */
    public ResponseDto<PartnerChannelConfigDto> getPartnerChannelConfigDto(Long partnerId) {
        try {
            ResponseDto<PartnerChannelConfigDto> response = configService.getPartnerChannelConfigDto(partnerId);
            LogUtils.debug(log, "获取渠道配置响应，企业ID: {}, 成功: {}",
                    partnerId, response != null && response.isSuccess());
            return response;
        } catch (Exception e) {
            LogUtils.error(log, "获取渠道配置异常，企业ID: {}", partnerId, e);
            return ResponseDto.error(ResultCode.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 获取登出URL数组
     *
     * @return 登出URL数组
     */
    public String[] getLogoutUrls() {
        try {
            ResponseDto<String[]> response = ssoService.getLogoutUrls();
            if (response != null && response.isSuccess()) {
                return response.getData();
            }
            LogUtils.warn(log, "获取登出URL失败，响应: {}", response);
            return new String[0];
        } catch (Exception e) {
            LogUtils.error(log, "获取登出URL异常", e);
            return new String[0];
        }
    }

    /**
     * 获取登出重定向URL列表
     *
     * @return 登出重定向URL列表
     */
    public List<String> getLogoutRedirectUrls() {
        try {
            ResponseDto<List<String>> response = ssoService.getLogoutRedirectUrls();
            if (response != null && response.isSuccess()) {
                return response.getData();
            }
            LogUtils.warn(log, "获取登出重定向URL失败，响应: {}", response);
            return Collections.emptyList();
        } catch (Exception e) {
            LogUtils.error(log, "获取登出重定向URL异常", e);
            return Collections.emptyList();
        }
    }

    /**
     * 获取登录认证令牌
     *
     * @param userId 用户ID
     * @param data   令牌数据
     * @return 认证令牌
     */
    public String getLoginAuthToken(Long userId, JSONObject data) {
        try {
            ResponseDto<String> response = ssoService.getLoginAuthToken(userId, data);
            if (response != null && response.isSuccess()) {
                return response.getData();
            }
            LogUtils.warn(log, "获取登录认证令牌失败，用户ID: {}, 响应: {}", userId, response);
            return null;
        } catch (Exception e) {
            LogUtils.error(log, "获取登录认证令牌异常，用户ID: {}", userId, e);
            return null;
        }
    }

    private String getAutoLoginUrl(List<String> logoutRedirectUrlList,
                                   String loginUrl, String alp, String serviceUrl) {

        List<String> allUrl = Lists.newArrayList(logoutRedirectUrlList);
        String s = UriComponentsBuilder.fromUriString(loginUrl)
                .queryParam("alp", alp)
                .queryParam("service", serviceUrl)
                .encode(StandardCharsets.UTF_8)
                .toUriString();

        allUrl.addFirst(s);

        String redirectUrl = allUrl.stream()
                .filter(StringUtils::isNotEmpty)
                .reduce((v1, v2) -> String.format("%s&redirectUrl=%s", v2, URLEncoder.encode(v1, StandardCharsets.UTF_8)))
                .orElse("");

        LogUtils.info(log, "redirectUrl: {}", redirectUrl);
        return redirectUrl;
    }


}
