package com.tem.customer.controller.partner;

import com.tem.customer.shared.annotation.OperationLog;
import com.tem.customer.shared.common.Result;
import com.tem.customer.shared.common.ResultCode;
import com.tem.customer.repository.entity.PartnerNote;
import com.tem.customer.model.convert.PartnerNoteConverter;
import com.tem.customer.model.dto.partner.PartnerNoteDTO;
import com.tem.customer.model.vo.partner.PartnerNoteVO;
import com.tem.customer.shared.enums.BusinessType;
import com.tem.customer.shared.enums.OperationType;
import com.tem.customer.shared.exception.BusinessException;
import com.tem.customer.service.partner.PartnerNoteService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * 企业备注控制器
 *
 * <AUTHOR>
 * @since 2025-06-19
 */
@Slf4j
@RestController
@RequestMapping("/api/cx/customer/admin/partner-notes")
@RequiredArgsConstructor
@Validated
public class PartnerNoteController {

    private final PartnerNoteService partnerNoteService;

    /**
     * 根据企业ID查询备注列表
     *
     * @param partnerId 企业ID
     * @return 备注列表
     */
    @GetMapping("/partner/{partnerId}")
    public Result<List<PartnerNoteVO>> listByPartnerId(@PathVariable @NotNull Long partnerId) {
        List<PartnerNote> notes = partnerNoteService.listByPartnerId(partnerId);
        List<PartnerNoteVO> voList = PartnerNoteConverter.INSTANCE.toVOList(notes);
        return Result.success(voList);
    }

    /**
     * 根据备注ID查询备注详情
     *
     * @param id 备注ID
     * @return 备注详情
     */
    @GetMapping("/{id}")
    public Result<PartnerNoteVO> getById(@PathVariable @NotNull Long id) {
        PartnerNote note = partnerNoteService.getById(id);
        if (note == null) {
            throw BusinessException.dataNotFound("备注");
        }
        PartnerNoteVO vo = PartnerNoteConverter.INSTANCE.toVO(note);
        return Result.success(vo);
    }

    /**
     * 添加企业备注
     *
     * @param dto 备注信息
     * @return 添加结果
     */
    @PostMapping
    @OperationLog(businessType = BusinessType.PARTNER_NOTE,
                  operationType = OperationType.CREATE,
                  description = "添加企业备注",
                  businessIdExpression = "#result.data.id",
                  targetPartnerIdExpression = "#dto.partnerId")
    public Result<PartnerNoteVO> add(@RequestBody @Valid PartnerNoteDTO dto) {
        // 添加调试日志
        log.info("接收到添加备注请求 - DTO: {}", dto);

        // 检查企业备注数量是否超过限制
        if (partnerNoteService.isExceedLimit(dto.getPartnerId())) {
            throw new BusinessException(ResultCode.OPERATION_NOT_ALLOWED, "企业备注数量已达到最大限制（8条）");
        }

        PartnerNote entity = PartnerNoteConverter.INSTANCE.toEntity(dto);
        PartnerNote savedEntity = partnerNoteService.addPartnerNote(entity);

        if (savedEntity == null) {
            throw new BusinessException(ResultCode.OPERATION_FAILED, "添加备注失败");
        }

        PartnerNoteVO vo = PartnerNoteConverter.INSTANCE.toVO(savedEntity);
        return Result.success("添加备注成功", vo);
    }

    /**
     * 调试接口 - 测试JSON解析
     * 临时接口，用于调试JSON解析问题
     *
     * @param dto 备注信息
     * @return 解析结果
     */
    @PostMapping("/debug")
    public Result<PartnerNoteDTO> debugJsonParsing(@RequestBody PartnerNoteDTO dto) {
        log.info("调试JSON解析 - 接收到的DTO: {}", dto);
        log.info("调试JSON解析 - partnerId: {}, title: {}, content: {}",
                dto.getPartnerId(), dto.getTitle(), dto.getContent());
        return Result.success("JSON解析成功", dto);
    }

    /**
     * 更新企业备注
     *
     * @param id 备注ID
     * @param dto 备注信息
     * @return 更新结果
     */
    @PutMapping("/{id}")
    @OperationLog(businessType = BusinessType.PARTNER_NOTE,
                  operationType = OperationType.UPDATE,
                  description = "更新企业备注",
                  businessIdExpression = "#id",
                  targetPartnerIdExpression = "#dto.partnerId")
    public Result<PartnerNoteVO> update(@PathVariable @NotNull Long id, @RequestBody @Valid PartnerNoteDTO dto) {
        // 检查备注是否存在
        PartnerNote existingNote = partnerNoteService.getById(id);
        if (existingNote == null) {
            throw BusinessException.dataNotFound("备注");
        }

        dto.setId(id);
        PartnerNote entity = PartnerNoteConverter.INSTANCE.toEntity(dto);
        boolean success = partnerNoteService.updatePartnerNote(entity);

        if (!success) {
            throw new BusinessException(ResultCode.OPERATION_FAILED, "更新备注失败");
        }

        PartnerNoteVO vo = PartnerNoteConverter.INSTANCE.toVO(entity);
        return Result.success("更新备注成功", vo);
    }

    /**
     * 删除企业备注
     *
     * @param id 备注ID
     * @return 删除结果
     */
    @DeleteMapping("/{id}")
    @OperationLog(businessType = BusinessType.PARTNER_NOTE,
                  operationType = OperationType.DELETE,
                  description = "删除企业备注",
                  businessIdExpression = "#id",
                  targetPartnerIdExpression = "#existingNote.partnerId")
    public Result<Void> delete(@PathVariable @NotNull Long id) {
        // 检查备注是否存在
        PartnerNote existingNote = partnerNoteService.getById(id);
        if (existingNote == null) {
            throw BusinessException.dataNotFound("备注");
        }

        boolean success = partnerNoteService.deletePartnerNote(id);
        if (!success) {
            throw new BusinessException(ResultCode.OPERATION_FAILED, "删除备注失败");
        }

        return Result.success("删除备注成功", null);
    }

    /**
     * 调整备注排序位置
     * 将指定备注移动到目标位置，自动调整其他备注的排序值
     *
     * @param id 备注ID
     * @param position 目标位置（1-based，1表示第一位）
     * @return 调整结果
     */
    @PutMapping("/{id}/sort")
    @OperationLog(businessType = BusinessType.PARTNER_NOTE,
                  operationType = OperationType.UPDATE,
                  description = "调整企业备注排序位置",
                  businessIdExpression = "#id",
                  targetPartnerIdExpression = "#existingNote.partnerId")
    public Result<Void> updateSortPosition(@PathVariable @NotNull Long id, @RequestParam @NotNull Integer position) {
        // 检查备注是否存在
        PartnerNote existingNote = partnerNoteService.getById(id);
        if (existingNote == null) {
            throw BusinessException.dataNotFound("备注");
        }

        // 验证位置参数
        if (position < 1) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "位置参数必须大于0");
        }

        // 调整排序位置
        boolean success = partnerNoteService.updateSortPosition(id, position);
        if (!success) {
            throw new BusinessException(ResultCode.OPERATION_FAILED, "调整排序位置失败");
        }

        return Result.success("调整排序位置成功", null);
    }

    /**
     * 统计企业备注数量
     *
     * @param partnerId 企业ID
     * @return 备注数量
     */
    @GetMapping("/partner/{partnerId}/count")
    public Result<Integer> countByPartnerId(@PathVariable @NotNull Long partnerId) {
        int count = partnerNoteService.countByPartnerId(partnerId);
        return Result.success(count);
    }

    /**
     * 检查企业备注数量是否超过限制
     *
     * @param partnerId 企业ID
     * @return 检查结果
     */
    @GetMapping("/partner/{partnerId}/limit-check")
    public Result<Boolean> checkLimit(@PathVariable @NotNull Long partnerId) {
        try {
            boolean isExceedLimit = partnerNoteService.isExceedLimit(partnerId);
            return Result.success(!isExceedLimit); // 返回是否可以继续添加
        } catch (Exception e) {
            log.error("检查企业备注数量限制失败，企业ID: {}", partnerId, e);
            return Result.error(ResultCode.BUSINESS_ERROR.getCode(), "检查数量限制失败");
        }
    }
}
