package com.tem.customer.controller.partner;

import com.iplatform.common.Page;
import com.iplatform.common.ResponseDto;
import com.iplatform.common.utils.LogUtils;
import com.tem.customer.shared.common.Result;
import com.tem.customer.shared.exception.BusinessException;
import com.tem.customer.shared.utils.UserContextUtil;
import com.tem.platform.api.PartnerService;
import com.tem.platform.api.dto.PartnerDto;
import com.tem.platform.api.dto.UserDto;
import com.tem.platform.biz.api.AdminPartnerSearchService;
import com.tem.platform.biz.condition.AdminPartnerQueryCondition;
import com.tem.platform.biz.enums.AdminTmcPartnerEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 企业管理控制器
 *
 * <p>主要功能：</p>
 * <ul>
 *   <li>企业列表查询</li>
 *   <li>TMC企业管理</li>
 *   <li>渠道企业管理</li>
 *   <li>权限控制和数据过滤</li>
 * </ul>
 *
 * <p>权限说明：</p>
 * <ul>
 *   <li>超级管理员：可查看所有TMC和企业</li>
 *   <li>TMC管理员：可查看所属TMC下的企业</li>
 *   <li>普通用户：根据权限查看对应数据</li>
 * </ul>
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Slf4j
@RestController
@RequestMapping("/api/cx/customer/admin/partner")
@RequiredArgsConstructor
@Validated
public class PartnerController {


    /**
     * 企业状态常量
     */
    private static final class PartnerStatus {
        /**
         * 正常状态
         */
        static final Integer NORMAL = 0;
        /**
         * 已停用状态
         */
        static final Integer DISABLED = 1;
    }


    /**
     * 状态标识前缀
     */
    private static final String DISABLED_PREFIX = "【已停用】-";

    @DubboReference(timeout = 10000, retries = 0, check = false)
    private PartnerService partnerService;

    @DubboReference(timeout = 10000, retries = 0, check = false)
    private AdminPartnerSearchService adminPartnerSearchService;


    /**
     * 查询企业列表
     *
     * <p>功能说明：</p>
     * <ul>
     *   <li>支持按企业类型筛选（企业、TMC、渠道）</li>
     *   <li>支持关键字搜索（企业名称、编码）</li>
     *   <li>支持分页查询</li>
     *   <li>根据用户权限过滤数据</li>
     *   <li>自动标记已停用企业</li>
     * </ul>
     *
     * @param pageNo      页码（从1开始）
     * @param pageSize    页大小
     * @param key         搜索关键字（企业名称或编码）
     * @param partnerType 企业类型：0-企业，1-TMC，2-渠道，null-全部
     * @return 企业列表分页结果
     */
    @RequestMapping(value = "/list")
    @ResponseBody
    public Result<Page<PartnerDto>> list(Integer pageNo, Integer pageSize, String key, Integer partnerType) {
        try {
            LogUtils.info(log, "开始查询企业列表，页码: {}, 页大小: {}, 关键字: {}, 企业类型: {}",
                    pageNo, pageSize, key, partnerType);

            // 构建查询参数
            AdminPartnerQueryCondition adminPartnerQueryCondition = buildQueryDTO(key, partnerType);

            // 执行查询
            ResponseDto<List<PartnerDto>> allPartner = adminPartnerSearchService.searchAllPartner(adminPartnerQueryCondition);
            if (allPartner.isNotSuccess()) {
                LogUtils.info(log, "企业查询失败, message:{}", allPartner.getMsg());
                Page<PartnerDto> emptyPage = new Page<>(0, 10, new ArrayList<>(), 0);
                return Result.success(emptyPage);
            }

            List<PartnerDto> allData = allPartner.getData();
            if (allData == null) {
                allData = new ArrayList<>();
            }

            // 根据关键字过滤数据
            List<PartnerDto> filteredData = filterByKey(allData, key);

            // 处理企业状态标记（为已停用企业添加标识前缀）
            processPartnerStatus(filteredData);

            // 封装分页结果
            Page<PartnerDto> partnerDtoPage = createPageResult(filteredData, pageNo, pageSize);

            LogUtils.info(log, "企业列表查询完成，原始记录数: {}, 过滤后记录数: {}, 分页封装完成",
                    allData.size(), filteredData.size());
            return Result.success(partnerDtoPage);

        } catch (BusinessException e) {
            LogUtils.error(log, "企业列表查询业务异常: {}", e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            LogUtils.error(log, "企业列表查询系统异常", e);
            throw BusinessException.error("查询企业列表失败，请稍后重试");
        }
    }


    /**
     * 根据企业id查询企业详情
     */
    @RequestMapping(value = "select")
    @ResponseBody
    public Result<List<PartnerDto>> getPartnersByIds(String partnerIds) {
        if (StringUtils.isEmpty(partnerIds)) {
            return Result.success(new ArrayList<>());
        }
        List<Long> idsLong = new ArrayList<>();
        String[] ids = partnerIds.split(",");
        for (String id : ids) {
            idsLong.add(Long.valueOf(id));
        }
        ResponseDto<List<PartnerDto>> responseDto = partnerService.findByBpIds(idsLong);
        if (responseDto.isSuccess()) {
            return Result.success(responseDto.getData());
        }
        return Result.error(responseDto.getMsg() != null ? responseDto.getMsg() : "查询失败");
    }


    /**
     * 构建查询参数DTO
     *
     * @param key         搜索关键字
     * @param partnerType 企业类型
     * @return 查询参数DTO
     */
    private AdminPartnerQueryCondition buildQueryDTO(String key, Integer partnerType) {
        AdminPartnerQueryCondition adminPartnerQueryCondition = new AdminPartnerQueryCondition();
        // 设置TMC ID（权限控制）
        UserDto currentUser = UserContextUtil.getCurrentUser();
        LogUtils.info(log, "当前用户信息: {}", currentUser);
        if (null == currentUser) {
            adminPartnerQueryCondition.setUserId(null);
            adminPartnerQueryCondition.setTmcId(null);
        } else {
            adminPartnerQueryCondition.setUserId(currentUser.getId());
            adminPartnerQueryCondition.setTmcId(currentUser.getPartnerId());
        }
        
        AdminTmcPartnerEnum adminTmcPartnerEnum;
        if (0 == partnerType) {
            adminTmcPartnerEnum = AdminTmcPartnerEnum.PARTNER;
        } else {
            adminTmcPartnerEnum = AdminTmcPartnerEnum.TMC;
        }
        adminPartnerQueryCondition.setKeyword(key);
        adminPartnerQueryCondition.setPartnerType(adminTmcPartnerEnum);
        return adminPartnerQueryCondition;
    }


    /**
     * 处理企业状态标记
     * 为已停用的企业添加标识前缀
     *
     * @param partnerList 企业列表
     */
    private void processPartnerStatus(List<PartnerDto> partnerList) {
        if (CollectionUtils.isEmpty(partnerList)) {
            return;
        }

        for (PartnerDto partnerDto : partnerList) {
            if (Objects.equals(partnerDto.getStatus(), PartnerStatus.DISABLED)) {
                // 为已停用企业添加标识
                if (StringUtils.isNotBlank(partnerDto.getName())) {
                    partnerDto.setName(DISABLED_PREFIX + partnerDto.getName());
                }
                if (StringUtils.isNotBlank(partnerDto.getAlias())) {
                    partnerDto.setAlias(DISABLED_PREFIX + partnerDto.getAlias());
                }
            }
        }

        LogUtils.debug(log, "企业状态标记处理完成");
    }

    /**
     * 根据关键字过滤企业数据
     * 支持按企业名称、别名、编码进行模糊搜索
     *
     * @param partnerList 企业列表
     * @param key         搜索关键字
     * @return 过滤后的企业列表
     */
    private List<PartnerDto> filterByKey(List<PartnerDto> partnerList, String key) {
        if (CollectionUtils.isEmpty(partnerList)) {
            return new ArrayList<>();
        }

        // 如果关键字为空，返回所有数据
        if (StringUtils.isBlank(key)) {
            return partnerList;
        }

        String searchKey = key.trim().toLowerCase();
        List<PartnerDto> filteredList = new ArrayList<>();

        for (PartnerDto partner : partnerList) {
            if (matchesSearchKey(partner, searchKey)) {
                filteredList.add(partner);
            }
        }

        LogUtils.debug(log, "关键字过滤完成，原始数据: {} 条，过滤后: {} 条",
                partnerList.size(), filteredList.size());
        return filteredList;
    }

    /**
     * 检查企业是否匹配搜索关键字
     *
     * @param partner   企业信息
     * @param searchKey 搜索关键字（已转为小写）
     * @return 是否匹配
     */
    private boolean matchesSearchKey(PartnerDto partner, String searchKey) {
        // 检查企业名称
        if (StringUtils.isNotBlank(partner.getName()) &&
            partner.getName().toLowerCase().contains(searchKey)) {
            return true;
        }

        return false;
    }

    /**
     * 创建分页结果
     * 将查询到的所有数据按照分页参数进行切割，封装成Page对象
     *
     * @param allData  所有数据
     * @param pageNo   页码（从1开始）
     * @param pageSize 页大小
     * @return 分页结果
     */
    private Page<PartnerDto> createPageResult(List<PartnerDto> allData, Integer pageNo, Integer pageSize) {
        // 确保 allData 不为 null
        if (allData == null) {
            allData = new ArrayList<>();
        }
        
        // 设置默认值
        int currentPage = pageNo != null && pageNo > 0 ? pageNo : 1;
        int size = pageSize != null && pageSize > 0 ? pageSize : 10;

        // 计算总数
        int total = allData.size();

        // 计算起始位置
        int start = (currentPage - 1) * size;

        // 获取当前页数据
        List<PartnerDto> pageData;
        if (start >= total) {
            // 如果起始位置超过总数，返回空列表
            pageData = new ArrayList<>();
        } else {
            int end = Math.min(start + size, total);
            pageData = allData.subList(start, end);
        }

        // 确保 pageData 不为 null
        if (pageData == null) {
            pageData = new ArrayList<>();
        }

        return new Page<>(start, size, pageData, total);
    }


}
