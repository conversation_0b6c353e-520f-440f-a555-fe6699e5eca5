package com.tem.customer.controller.wechat;

import com.iplatform.common.utils.LogUtils;
import com.tem.customer.model.dto.wechat.WechatGroupDetailRequest;
import com.tem.customer.model.dto.wechat.WechatGroupDetailResponse;
import com.tem.customer.model.vo.wechat.WechatGroupDetailVO;
import com.tem.customer.service.wechat.WechatApiService;
import com.tem.customer.shared.common.Result;
import com.tem.customer.shared.converter.WechatGroupDetailConverter;
import jakarta.validation.constraints.NotBlank;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;


/**
 * 企业微信群控制器
 *
 * <AUTHOR>
 * @since 2025-07-31
 */
@Slf4j
@RestController
@RequestMapping("/api/cx/customer/admin/wechat/groups")
@RequiredArgsConstructor
@Validated
public class WechatGroupController {

    private final WechatApiService wechatApiService;

    /**
     * 获取客户群详情
     *
     * @param chatId 客户群ID
     * @return 群详情
     */
    @GetMapping("/{chatId}/detail")
    public Result<WechatGroupDetailVO> getGroupDetail(@PathVariable @NotBlank String chatId) {
        LogUtils.info(log, "获取企业微信群详情，群ID: {}", chatId);

        WechatGroupDetailResponse response = wechatApiService.getGroupDetail(chatId);
        WechatGroupDetailVO vo = WechatGroupDetailConverter.INSTANCE.toVO(response);

        LogUtils.info(log, "获取企业微信群详情成功，群ID: {}, 群名: {}, 成员数: {}",
                chatId, vo.getName(), vo.getMemberCount());

        return Result.success(vo);
    }

    /**
     * 获取客户群详情（带参数）
     *
     * @param chatId   客户群ID
     * @param needName 是否需要返回群成员的名字，0-不返回；1-返回，默认1
     * @return 群详情
     */
    @GetMapping("/{chatId}/detail/params")
    public Result<WechatGroupDetailVO> getGroupDetailWithParams(
            @PathVariable @NotBlank String chatId,
            @RequestParam(value = "needName", defaultValue = "1") Integer needName) {

        LogUtils.info(log, "获取企业微信群详情（带参数），群ID: {}, needName: {}", chatId, needName);

        WechatGroupDetailResponse response = wechatApiService.getGroupDetail(chatId, needName);
        WechatGroupDetailVO vo = WechatGroupDetailConverter.INSTANCE.toVO(response);

        LogUtils.info(log, "获取企业微信群详情成功，群ID: {}, 群名: {}, 成员数: {}",
                chatId, vo.getName(), vo.getMemberCount());

        return Result.success(vo);
    }

    /**
     * 获取客户群详情（POST方式）
     *
     * @param request 群详情请求
     * @return 群详情
     */
    @PostMapping("/detail")
    public Result<WechatGroupDetailVO> getGroupDetailByPost(@RequestBody @Valid WechatGroupDetailRequest request) {
        LogUtils.info(log, "获取企业微信群详情（POST），请求参数: {}", request);

        WechatGroupDetailResponse response = wechatApiService.getGroupDetail(request);
        WechatGroupDetailVO vo = WechatGroupDetailConverter.INSTANCE.toVO(response);

        LogUtils.info(log, "获取企业微信群详情成功，群ID: {}, 群名: {}, 成员数: {}",
                request.getChatId(), vo.getName(), vo.getMemberCount());

        return Result.success(vo);
    }

    /**
     * 获取Access Token（调试用）
     *
     * @return Access Token
     */
    @GetMapping("/access-token")
    public Result<String> getAccessToken() {
        LogUtils.info(log, "获取企业微信Access Token");

        String accessToken = wechatApiService.getAccessToken();

        LogUtils.info(log, "获取企业微信Access Token成功");
        return Result.success(accessToken);
    }

    /**
     * 刷新Access Token（调试用）
     *
     * @return 新的Access Token
     */
    @PostMapping("/access-token/refresh")
    public Result<String> refreshAccessToken() {
        LogUtils.info(log, "刷新企业微信Access Token");

        String accessToken = wechatApiService.refreshAccessToken();

        LogUtils.info(log, "刷新企业微信Access Token成功");
        return Result.success(accessToken);
    }
}
