package com.tem.customer.controller.third;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Objects;
import com.google.common.collect.Lists;
import com.iplatform.common.ResponseDto;
import com.iplatform.common.utils.LogUtils;
import com.iplatform.common.web.ResponseVO;
import com.tem.customer.model.from.UserAndStandardForm;
import com.tem.customer.service.third.CsService;
import com.tem.customer.shared.common.Result;
import com.tem.customer.shared.common.ResultCode;
import com.tem.customer.shared.utils.UserContextUtil;
import com.tem.errand.butler.api.HotelStandardService;
import com.tem.errand.butler.model.dto.standard.HotelStandardItemDto;
import com.tem.errand.quark.api.PartnerRankService;
import com.tem.platform.api.DistrictService;
import com.tem.platform.api.UserOpenIdService;
import com.tem.platform.api.UserService;
import com.tem.platform.api.condition.DistrictQueryCondition;
import com.tem.platform.api.dto.DistrictDto;
import com.tem.platform.api.dto.UserDto;
import com.tem.platform.api.dto.UserOpenIdDto;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 云客服数据请求接口
 *
 * <AUTHOR>
 * {@code @date} 创建时间：2020年5月7日 下午8:44:28
 */
@Controller
@RequestMapping("/api/cx/customer/admin/cs")
public class CsController {

    private final Logger logger = LoggerFactory.getLogger(CsController.class);

    @Autowired
    private UserService userService;
    @DubboReference(timeout = 10000, retries = 0, check = false)
    private DistrictService districtService;
    @Autowired
    private CsService csService;
    @DubboReference(timeout = 10000, retries = 0, check = false)
    private UserOpenIdService userOpenIdService;
    @DubboReference
    private PartnerRankService partnerRankService;
    @DubboReference
    private HotelStandardService hotelStandardService;

    //获取参数userId
    private Long getUserId() {
        Long userId = UserContextUtil.getLongValue("userId");
        String openId = UserContextUtil.getStringValue("openId");
        if (userId == null && openId != null) {
            ResponseDto<UserOpenIdDto> response = userOpenIdService.getUserByOpenId(openId);
            if (response != null && response.isSuccess() && response.getData() != null) {
                UserOpenIdDto userOpenIdDto = response.getData();
                userId = userOpenIdDto.getUserid();
            }
        }
        return userId;
    }


    /**
     * 用户和差标基本信息接口
     *
     * @return ResponseVO<UserAndStandardForm> 数据参考UserAndStandardForm对象
     * {@code @catalog} 云客服页面文档
     * {@code @title} 用户和差标基本信息
     * {@code @description} 用户和差标基本信息接口
     * {@code @method} get
     * {@code @url} /cs/getUserAndStandardInfo.json
     * {@code @return_param} userAndStandardForm UserAndStandardForm 用户和差标基本信息对象数据信息
     * {@code @remark}
     * {@code @number} 1
     */
    @RequestMapping(value = "getUserAndStandardInfo")
    @ResponseBody
    public ResponseVO<UserAndStandardForm> getUserAndStandardInfo() {
        Long userId = this.getUserId();
        String openId = UserContextUtil.getStringValue("openId");
        LogUtils.info(logger, "云客服，获取用户和差标基本信息，userId:{},openId:{}，当前客服操作人员：{}", userId, openId, 123);
        if (userId == null) {
            // 用户身份未绑定
            if (StringUtils.isNotEmpty(openId)) {
                return ResponseVO.error(ResultCode.CS_USER_BIND_NO);
            }
            return ResponseVO.error(ResultCode.BAD_REQUEST);
        }
        UserAndStandardForm form = csService.getUserAndStandardInfo(userId);
        return ResponseVO.success(form);
    }

    /**
     * 查询国内行程区划信息接口
     *
     * @param keyword 必选 String 关键字参数
     * @return {"code":"0","msg":"成功","data":[{"id":10081,"nameCn":"上海市","parentName":"上海直辖市"}],"notSuccess":false,"success":true}
     * {@code @catalog} 云客服页面文档
     * {@code @title} 关键字模糊匹配查询国内行程区划信息
     * {@code @description} 查询国内行程区划信息接口
     * {@code @method} get
     * {@code @url} /cs/queryDistrictByKeyword.json
     * {@code @return_param} id Integer 城市id
     * {@code @return_param} nameCn String 城市名称
     * {@code @return_param} parentName String 上级城市名称
     * {@code @remark} 注：返回json样例数据，仅仅展示了部分主要数据
     * {@code @number} 2
     */
    @RequestMapping(value = "queryDistrictByKeyword")
    @ResponseBody
    public ResponseVO<List<DistrictDto>> queryDistrictByKeyword(String keyword) {
        LogUtils.info(logger, "云客服，queryDistrictByKeyword，入参:{}", keyword);
        if (StringUtils.isBlank(keyword)) {
            return ResponseVO.error(ResultCode.PARAM_NULL_ERROR);
        }
        DistrictQueryCondition query = new DistrictQueryCondition();
        query.setPath("1,2");
        query.setLevels(Lists.newArrayList("3", "4"));
        query.setKeyword(keyword);
        List<DistrictDto> list = districtService.findDistrictDtoLimitByParam(query).getData();
        if (CollectionUtils.isNotEmpty(list)) {
            Set<Integer> idSet = new HashSet<>();
            list.forEach(l -> idSet.add(l.getpId()));
            Map<Integer, DistrictDto> map = districtService.findMapByIds(new ArrayList<>(idSet)).getData();
            list.forEach(l -> {
                if (map.get(l.getpId()) != null) {
                    l.setParentName(map.get(l.getpId()).getNameCn());
                }
            });
        }
        return ResponseVO.success(list);
    }

    /**
     * 酒店差标信息接口
     *
     * @param cityId 必选 Long 城市id
     * @return {"code":"0","msg":"成功","data":[{"month":"全年","money":300,"agreementMoney":250}],"notSuccess":false,"success":true}
     * {@code @catalog} 云客服页面文档
     * {@code @title} 酒店差标信息
     * {@code @description} 酒店差标信息接口
     * {@code @method} get
     * {@code @url} /cs/getHotelStandardInfo.json
     * {@code @return_param} month string 月份：1月，2月……，全年
     * {@code @return_param} money Double 普通酒店差标金额
     * {@code @return_param} agreementMoney Double 协议酒店差标金额
     * {@code @remark}
     * {@code @number} 3
     */
    @RequestMapping(value = "getHotelStandardInfo")
    @ResponseBody
    public ResponseVO<List<UserAndStandardForm.HotelStandardItemInfo>> getHotelStandardInfo(Long cityId) {
        Long userId = this.getUserId();
        LogUtils.info(logger, "云客服，获取根据城市查询酒店差标基本信息，入参userId:{}，cityId：{}", userId, cityId);
        if (userId == null || cityId == null) {
            return ResponseVO.error(ResultCode.PARAM_NULL_ERROR);
        }
        ResponseDto<UserDto> userResponse = userService.getUserBaseInfo(userId);
        if (userResponse == null || !userResponse.isSuccess() || userResponse.getData() == null) {
            LogUtils.warn(logger, "获取用户信息失败，userId: {}, 响应: {}", userId, userResponse);
            return ResponseVO.error(ResultCode.DATA_NOT_FOUND);
        }
        UserDto userDto = userResponse.getData();
        // 【差旅管控老接口】String empLevelCode = userDataService.getEmpLevel(userDto.getPartnerId(), userDto.getId()).getData();
        String empLevelCode = partnerRankService.getEmpRank(userDto.getPartnerId(), userDto.getId()).getData();
        LogUtils.info(logger, "云客服，获取根据城市查询酒店差标基本信息，入参userId:{}，cityId：{}, 【差旅管控新接口PartnerRankService#getEmpRank(java.lang.Long, java.lang.Long)】empLevelCode: {}", userId, cityId, empLevelCode);
        // 【差旅管控老接口】
        //TravelRuleDto travelRuleDto = userDataService.getTravelRule(userDto.getPartnerId(), empLevelCode, null, userDto.getId()).getData();
        // 【差旅管控新接口】
        Long dCityId = districtService.getParentCityid(cityId, 3).getData();
        List<HotelStandardItemDto> hotelStandardItemDtos = hotelStandardService.getHotelStandardInfo(userDto.getPartnerId(), empLevelCode, dCityId, userDto.getId()).getData();
        LogUtils.info(logger, "云客服，获取根据城市查询酒店差标基本信息，入参userId:{}，cityId：{}, empLevelCode: {},【差旅管控新接口HotelStandardService.getHotelStandardInfo】 hotelStandardItemDtos: {}"
                , userId, cityId, empLevelCode, JSON.toJSONString(hotelStandardItemDtos));
        List<UserAndStandardForm.HotelStandardItemInfo> infoList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(hotelStandardItemDtos)) {
            for (HotelStandardItemDto standardItemDto : hotelStandardItemDtos) {
                UserAndStandardForm.HotelStandardItemInfo info = new UserAndStandardForm.HotelStandardItemInfo();
                Double money = standardItemDto.getMoney();
                Double agreementMoney = standardItemDto.getAgreementMoney();
                info.setMonth(standardItemDto.getMonth() + "月");
                info.setMoney(money);
                info.setAgreementMoney(agreementMoney);
                infoList.add(info);
            }
            boolean notSame = false;
            if (CollectionUtils.isNotEmpty(infoList)) {
                for (int i = 0; i < infoList.size() - 2; i++) {
                    UserAndStandardForm.HotelStandardItemInfo info = infoList.get(i);
                    UserAndStandardForm.HotelStandardItemInfo nextInfo = infoList.get(i + 1);
                    if (!Objects.equal(info.getMoney(), nextInfo.getMoney())) {
                        notSame = true;
                        break;
                    }
                    if (!Objects.equal(info.getAgreementMoney(), nextInfo.getAgreementMoney())) {
                        notSame = true;
                        break;
                    }
                }
                if (!notSame) {
                    List<UserAndStandardForm.HotelStandardItemInfo> allList = new ArrayList<>();
                    UserAndStandardForm.HotelStandardItemInfo sameItem = infoList.getFirst();
                    sameItem.setMonth("全年");
                    allList.add(sameItem);
                    return ResponseVO.success(allList);
                }
            }

        }
        return ResponseVO.success(infoList);
    }


    /**
     * 订单信息接口
     *
     * @return {"code":"0","msg":"成功","data":[{"id":115624845528192,"bizType":"HOTEL","totalAmount":970,"orderShowStatus":"已退订","hotelName":"上海天禧嘉福璞缇客酒店","hotelAddress":"虹许路358号","trip":null,"insuranceName":null,"serviceName":null,"travelStartTime":1590508800000,"userName":"王文杰","stayDays":1},{"id":105630431301760,"bizType":"FLIGHT","totalAmount":1680,"orderShowStatus":"已出票","hotelName":null,"hotelAddress":null,"trip":"北京-上海","insuranceName":null,"serviceName":null,"travelStartTime":1589199600000,"userName":"蔡伟超我","stayDays":null},{"id":125551031969920,"bizType":"TRAIN","totalAmount":-16.5,"orderShowStatus":"已退票","hotelName":null,"hotelAddress":null,"trip":"上海虹桥-昆山南","insuranceName":null,"serviceName":null,"travelStartTime":1588371180000,"userName":"蔡伟超我","stayDays":null},{"id":145542203714432,"bizType":"GENERAL","totalAmount":448,"orderShowStatus":"已完成","hotelName":null,"hotelAddress":null,"trip":null,"insuranceName":null,"serviceName":"团签","travelStartTime":1586448000000,"userName":"蔡伟超我","stayDays":null},{"id":155149179449473,"bizType":"INSURANCE","totalAmount":30,"orderShowStatus":"待付款","hotelName":null,"hotelAddress":null,"trip":null,"insuranceName":"个人航空意外险方案二","serviceName":null,"travelStartTime":1583295900000,"userName":"蔡伟超","stayDays":null},{"id":175151757963392,"bizType":"INTL_FLIGHT","totalAmount":754,"orderShowStatus":"出票中","hotelName":null,"hotelAddress":null,"trip":"上海-大阪","insuranceName":null,"serviceName":null,"travelStartTime":1583228400000,"userName":"蔡伟超","stayDays":null}],"notSuccess":false,"success":true}
     * {@code @catalog} 云客服页面文档
     * {@code @title} 订单信息列表
     * {@code @description} 订单信息接口
     * {@code @method} get
     * {@code @url} /cs/getOrderInfo.json
     * {@code @return_param} id long 订单号
     * {@code @return_param} bizType string 业务类型（10-机票/11-酒店/12-火车/14-需求单/15-保险/17-国际机票）
     * {@code @return_param} totalAmount int 订单总金额
     * {@code @return_param} orderShowStatus string 订单状态
     * {@code @return_param} hotelName string 酒店名称
     * {@code @return_param} trip string 行程信息
     * {@code @return_param} insuranceName string 保险产品名称
     * {@code @return_param} serviceName string 服务品类名称（通用）
     * {@code @return_param} travelStartTime string 出发日期（保障日期、入住日期）
     * {@code @return_param} userName string 出行人（被保险人）
     * {@code @return_param} stayDays int 入住日期
     * {@code @remark}
     * {@code @number} 4
     */
    @RequestMapping(value = "/getOrderInfo", method = RequestMethod.GET)
    @ResponseBody
    public Result getOrderInfo() {
        Long userId = this.getUserId();
        if (userId == null) {
            return Result.error(ResultCode.PARAM_NULL_ERROR);
        }
        ResponseDto orderInfo = csService.getOrderInfo(userId);
        Object data = orderInfo.getData();
        return Result.success(orderInfo);
    }


}
