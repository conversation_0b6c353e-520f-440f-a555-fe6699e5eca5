package com.tem.customer.service.partner;

import com.google.common.collect.Maps;
import com.iplatform.common.OrderBizType;
import com.iplatform.common.ResponseDto;
import com.iplatform.common.utils.DateUtils;
import com.iplatform.common.utils.LogUtils;
import com.tem.customer.model.vo.common.UserOrderInfoVO;
import com.tem.customer.shared.exception.BusinessException;
import com.tem.oms.api.OrderService;
import com.tem.oms.dto.OrderDto;
import com.tem.oms.enums.ShowStatusEnum;
import com.tem.platform.api.UserService;
import com.tem.platform.api.dto.UserDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.BiConsumer;
import java.util.function.BinaryOperator;
import java.util.stream.Collectors;

/**
 * 企业用户服务实现类
 * 提供企业用户相关的业务功能，主要包括用户订单信息查询等
 *
 * <AUTHOR>
 * @since 2025-07-04
 */
@Slf4j
@Service("partnerUserService")
public class PartnerUserServiceImpl implements PartnerUserService {

    // ==================== 业务常量定义 ====================

    /**
     * 已取消订单状态
     */
    private static final String ORDER_STATUS_CANCELED = "CANCELED";

    /**
     * 查询订单的时间范围（月数）
     */
    private static final int QUERY_MONTHS_RANGE = 3;

    /**
     * 返回订单的最大数量限制
     */
    private static final int MAX_ORDER_LIMIT = 20;

    /**
     * 金额转换基数（分转元）
     */
    private static final BigDecimal AMOUNT_CONVERSION_BASE = new BigDecimal(100);

    // ==================== 行程信息处理器定义 ====================

    /**
     * 行程信息拼接累加器
     * 行程信息显示规则：
     * - 连续行程：上海->北京，北京->天津 显示为 上海-北京-天津
     * - 非连续行程：上海->北京，天津->杭州 显示为 上海-北京，天津-杭州
     */
    private static final BinaryOperator<String> TRIP_ACCUMULATOR = (currentTrip, nextTrip) -> {
        if (StringUtils.isEmpty(currentTrip)) {
            return nextTrip;
        }

        // 解析当前行程的最后一段
        String[] currentSegments = StringUtils.split(currentTrip, ",");
        String[] lastSegmentParts = StringUtils.split(currentSegments[currentSegments.length - 1], "-");
        String lastDestination = lastSegmentParts[lastSegmentParts.length - 1];

        // 解析下一段行程的起点
        String[] nextSegmentParts = StringUtils.split(nextTrip, "-");
        String nextOrigin = nextSegmentParts[0];

        // 如果连续，则合并；否则用逗号分隔
        if (StringUtils.equals(lastDestination, nextOrigin)) {
            return currentTrip + "-" + nextSegmentParts[nextSegmentParts.length - 1];
        }
        return currentTrip + "," + nextTrip;
    };

    // ==================== 订单类型处理器定义 ====================

    /**
     * 国内机票订单处理器
     */
    private static final BiConsumer<UserOrderInfoVO, OrderDto> FLIGHT_PROCESSOR = (orderVO, orderDto) -> {
        Optional.ofNullable(orderDto.getOrderDetail())
                .flatMap(orderDetail -> Optional.ofNullable(orderDetail.getFlightOrderDetailDtos()))
                .ifPresent(flightDetails -> {
                    String tripInfo = flightDetails.stream()
                            .map(flight -> String.format("%s-%s", flight.getFromCityName(), flight.getToCityName()))
                            .reduce("", TRIP_ACCUMULATOR);
                    orderVO.setTrip(tripInfo);
                });

        Optional.ofNullable(orderDto.getOrderShowStatus())
                .ifPresent(status -> orderVO.setOrderShowStatus(
                        ShowStatusEnum.valueOf(status).getFlightMessage()));
    };

    /**
     * 国际机票订单处理器
     */
    private static final BiConsumer<UserOrderInfoVO, OrderDto> INTL_FLIGHT_PROCESSOR = (orderVO, orderDto) -> {
        Optional.ofNullable(orderDto.getOrderDetail())
                .flatMap(orderDetail -> Optional.ofNullable(orderDetail.getIntlFlightOrderDetailDtos()))
                .ifPresent(intlFlightDetails -> {
                    String tripInfo = intlFlightDetails.stream()
                            .map(flight -> String.format("%s-%s", flight.getFromCityName(), flight.getToCityName()))
                            .reduce("", TRIP_ACCUMULATOR);
                    orderVO.setTrip(tripInfo);
                });

        Optional.ofNullable(orderDto.getOrderShowStatus())
                .ifPresent(status -> orderVO.setOrderShowStatus(
                        ShowStatusEnum.valueOf(status).getIntFlightMessage()));
    };

    /**
     * 火车票订单处理器
     */
    private static final BiConsumer<UserOrderInfoVO, OrderDto> TRAIN_PROCESSOR = (orderVO, orderDto) -> {
        Optional.ofNullable(orderDto.getOrderDetail())
                .flatMap(orderDetail -> Optional.ofNullable(orderDetail.getTrainOrderDetailDtos()))
                .flatMap(trainDetails -> trainDetails.stream().findFirst())
                .ifPresent(trainDetail -> orderVO.setTrip(
                        String.format("%s-%s", trainDetail.getFromStation(), trainDetail.getArriveStation())));

        Optional.ofNullable(orderDto.getOrderShowStatus())
                .ifPresent(status -> orderVO.setOrderShowStatus(
                        ShowStatusEnum.valueOf(status).getTrainMessage()));
    };

    /**
     * 酒店订单处理器
     */
    private static final BiConsumer<UserOrderInfoVO, OrderDto> HOTEL_PROCESSOR = (orderVO, orderDto) -> {
        Optional.ofNullable(orderDto.getOrderDetail())
                .flatMap(orderDetail -> Optional.ofNullable(orderDetail.getHotelOrderDetailDto()))
                .ifPresent(hotelDetail -> {
                    orderVO.setHotelName(hotelDetail.getHotelName());
                    orderVO.setHotelAddress(hotelDetail.getHotelAddress());

                    // 计算入住天数
                    int stayDays = DateUtils.getDaysBetween(
                            hotelDetail.getCheckInDate(), hotelDetail.getCheckOutDate());
                    orderVO.setStayDays(stayDays);
                });

        Optional.ofNullable(orderDto.getOrderShowStatus())
                .ifPresent(status -> orderVO.setOrderShowStatus(
                        ShowStatusEnum.valueOf(status).getHotelMessage()));
    };

    /**
     * 保险订单处理器
     */
    private static final BiConsumer<UserOrderInfoVO, OrderDto> INSURANCE_PROCESSOR = (orderVO, orderDto) -> {
        Optional.ofNullable(orderDto.getInsuranceProductInfoDtos())
                .flatMap(insuranceProducts -> insuranceProducts.stream().findFirst())
                .ifPresent(insuranceProduct -> orderVO.setInsuranceName(insuranceProduct.getInsuranceName()));

        Optional.ofNullable(orderDto.getOrderShowStatus())
                .ifPresent(status -> orderVO.setOrderShowStatus(
                        ShowStatusEnum.valueOf(status).getInsuranceMessage()));
    };

    /**
     * 通用服务订单处理器
     */
    private static final BiConsumer<UserOrderInfoVO, OrderDto> GENERAL_PROCESSOR = (orderVO, orderDto) -> {
        Optional.ofNullable(orderDto.getGeneralProductInfoDto())
                .ifPresent(generalProduct -> orderVO.setServiceName(generalProduct.getServiceName()));

        Optional.ofNullable(orderDto.getOrderShowStatus())
                .ifPresent(status -> orderVO.setOrderShowStatus(
                        ShowStatusEnum.valueOf(status).getGeneralMessage()));
    };

    /**
     * 订单类型处理器映射表
     * 根据订单业务类型选择对应的处理器进行数据转换
     */
    private static final Map<OrderBizType, BiConsumer<UserOrderInfoVO, OrderDto>> ORDER_PROCESSORS = Maps.newHashMap();

    static {
        ORDER_PROCESSORS.put(OrderBizType.FLIGHT, FLIGHT_PROCESSOR);
        ORDER_PROCESSORS.put(OrderBizType.INTL_FLIGHT, INTL_FLIGHT_PROCESSOR);
        ORDER_PROCESSORS.put(OrderBizType.TRAIN, TRAIN_PROCESSOR);
        ORDER_PROCESSORS.put(OrderBizType.HOTEL, HOTEL_PROCESSOR);
        ORDER_PROCESSORS.put(OrderBizType.INSURANCE, INSURANCE_PROCESSOR);
        ORDER_PROCESSORS.put(OrderBizType.GENERAL, GENERAL_PROCESSOR);
    }

    // ==================== 外部服务依赖 ====================

    @DubboReference(timeout = 10000, retries = 0, check = false)
    private UserService userService;

    @DubboReference(timeout = 10000, retries = 0, check = false)
    private OrderService orderService;

    // ==================== 公共业务方法 ====================

    /**
     * 查询用户订单信息
     *
     * @param partnerId 企业ID
     * @param userId    用户ID
     * @return 用户订单信息列表
     */
    @Override
    public List<UserOrderInfoVO> getUserOrders(Long partnerId, Long userId) {
        try {
            LogUtils.info(log, "开始查询用户订单信息，企业ID: {}, 用户ID: {}", partnerId, userId);

            // 参数验证
            validateParameters(partnerId, userId);

            // 获取并验证用户信息
            UserDto userInfo = getUserAndValidate(partnerId, userId);

            // 查询订单信息
            List<UserOrderInfoVO> orderList = queryUserOrders(partnerId, userId);

            LogUtils.info(log, "用户订单信息查询完成，企业ID: {}, 用户ID: {}, 返回订单数量: {}",
                    partnerId, userId, orderList.size());
            return orderList;

        } catch (BusinessException e) {
            LogUtils.warn(log, "查询用户订单信息业务异常，企业ID: {}, 用户ID: {}, 错误: {}",
                    partnerId, userId, e.getMessage());
            throw e;
        } catch (Exception e) {
            LogUtils.error(log, "查询用户订单信息系统异常，企业ID: {}, 用户ID: {}", partnerId, userId, e);
            throw BusinessException.error("查询用户订单信息失败");
        }
    }

    // ==================== 私有业务方法 ====================

    /**
     * 验证输入参数
     *
     * @param partnerId 企业ID
     * @param userId    用户ID
     */
    private void validateParameters(Long partnerId, Long userId) {
        if (partnerId == null || userId == null) {
            throw BusinessException.error("企业ID和用户ID不能为空");
        }
    }

    /**
     * 获取用户信息并验证企业归属
     *
     * @param partnerId 企业ID
     * @param userId    用户ID
     * @return 用户信息
     */
    private UserDto getUserAndValidate(Long partnerId, Long userId) {
        // 获取用户基础信息
        ResponseDto<UserDto> userResponse = userService.getUserBaseInfo(userId);
        if (userResponse == null || !userResponse.isSuccess() || userResponse.getData() == null) {
            LogUtils.warn(log, "获取用户信息失败，企业ID: {}, 用户ID: {}, 响应: {}",
                    partnerId, userId, userResponse);
            throw BusinessException.error("用户信息不存在");
        }

        UserDto userInfo = userResponse.getData();

        // 验证用户是否属于指定企业
        if (!Objects.equals(userInfo.getPartnerId(), partnerId)) {
            LogUtils.warn(log, "用户不属于指定企业，用户企业ID: {}, 请求企业ID: {}",
                    userInfo.getPartnerId(), partnerId);
            throw BusinessException.error("用户不属于指定企业");
        }

        return userInfo;
    }

    /**
     * 查询用户订单信息
     *
     * @param partnerId 企业ID
     * @param userId    用户ID
     * @return 用户订单信息列表
     */
    private List<UserOrderInfoVO> queryUserOrders(Long partnerId, Long userId) {
        try {
            // 设置查询时间范围（近N个月）
            Calendar calendar = Calendar.getInstance();
            calendar.set(Calendar.MONTH, calendar.get(Calendar.MONTH) - QUERY_MONTHS_RANGE);

            LogUtils.info(log, "查询用户订单，企业ID: {}, 用户ID: {}, 开始时间: {}",
                    partnerId, userId, calendar.getTime());

            // 查询订单列表
            ResponseDto<List<OrderDto>> orderResponse = orderService.queryOrderListByCs(
                    partnerId, userId, calendar.getTime());

            if (orderResponse == null || !orderResponse.isSuccess() ||
                    CollectionUtils.isEmpty(orderResponse.getData())) {
                LogUtils.info(log, "未查询到用户订单数据，企业ID: {}, 用户ID: {}", partnerId, userId);
                return Collections.emptyList();
            }

            // 处理订单数据
            List<UserOrderInfoVO> orderList = processOrderData(orderResponse.getData());

            LogUtils.info(log, "用户订单数据处理完成，企业ID: {}, 用户ID: {}, 有效订单数量: {}",
                    partnerId, userId, orderList.size());
            return orderList;

        } catch (Exception e) {
            LogUtils.error(log, "查询用户订单异常，企业ID: {}, 用户ID: {}", partnerId, userId, e);
            return Collections.emptyList();
        }
    }

    /**
     * 处理订单数据，过滤、转换、排序并限制数量
     *
     * @param orderDtoList 原始订单数据列表
     * @return 处理后的订单VO列表
     */
    private List<UserOrderInfoVO> processOrderData(List<OrderDto> orderDtoList) {
        return orderDtoList.stream()
                .filter(this::isValidOrder)
                .map(this::convertToOrderVO)
                .filter(Objects::nonNull)
                .sorted((order1, order2) -> order2.getTravelStartTime().compareTo(order1.getTravelStartTime()))
                .limit(MAX_ORDER_LIMIT)
                .collect(Collectors.toList());
    }

    /**
     * 判断订单是否有效
     *
     * @param orderDto 订单DTO
     * @return 是否有效
     */
    private boolean isValidOrder(OrderDto orderDto) {
        // 过滤已取消的订单
        if (StringUtils.equals(orderDto.getOrderShowStatus(), ORDER_STATUS_CANCELED)) {
            return false;
        }

        // 过滤没有行程开始时间的订单
        return Objects.nonNull(orderDto.getTravelStartTime());
    }

    /**
     * 转换订单DTO为VO对象
     *
     * @param orderDto 订单DTO
     * @return 订单VO对象
     */
    private UserOrderInfoVO convertToOrderVO(OrderDto orderDto) {
        try {
            UserOrderInfoVO orderVO = new UserOrderInfoVO();

            // 设置基础信息
            orderVO.setId(orderDto.getId());
            orderVO.setTravelStartTime(orderDto.getTravelStartTime());

            // 设置业务类型
            setBizTypeInfo(orderVO, orderDto);

            // 设置订单金额（分转元）
            setOrderAmount(orderVO, orderDto);

            // 根据业务类型处理特定信息
            processOrderByType(orderVO, orderDto);

            // 设置乘客姓名
            Optional.ofNullable(orderDto.getOrderTravellerNames())
                    .ifPresent(orderVO::setUserName);

            return orderVO;
        } catch (Exception e) {
            LogUtils.warn(log, "转换订单DTO异常，订单ID: {}", orderDto.getId(), e);
            return null;
        }
    }

    /**
     * 设置业务类型信息
     *
     * @param orderVO  订单VO
     * @param orderDto 订单DTO
     */
    private void setBizTypeInfo(UserOrderInfoVO orderVO, OrderDto orderDto) {
        Integer bizTypeCode = orderDto.getBizType();
        OrderBizType orderBizType = getOrderBizTypeByCode(bizTypeCode);
        orderVO.setBizType(orderBizType != null ?
                String.valueOf(orderBizType.getCode()) : String.valueOf(bizTypeCode));
    }

    /**
     * 设置订单金额（分转元）
     *
     * @param orderVO  订单VO
     * @param orderDto 订单DTO
     */
    private void setOrderAmount(UserOrderInfoVO orderVO, OrderDto orderDto) {
        if (orderDto.getTotalAmount() != null) {
            BigDecimal amount = new BigDecimal(orderDto.getTotalAmount())
                    .divide(AMOUNT_CONVERSION_BASE);
            orderVO.setTotalAmount(amount);
        }
    }

    /**
     * 根据订单类型处理特定信息
     *
     * @param orderVO  订单VO
     * @param orderDto 订单DTO
     */
    private void processOrderByType(UserOrderInfoVO orderVO, OrderDto orderDto) {
        Integer bizTypeCode = orderDto.getBizType();
        OrderBizType orderBizType = getOrderBizTypeByCode(bizTypeCode);

        if (orderBizType != null && ORDER_PROCESSORS.containsKey(orderBizType)) {
            ORDER_PROCESSORS.get(orderBizType).accept(orderVO, orderDto);
        }
    }

    // ==================== 静态工具方法 ====================

    /**
     * 根据业务类型代码获取OrderBizType枚举
     *
     * @param bizTypeCode 业务类型代码
     * @return OrderBizType枚举，未找到时返回null
     */
    private static OrderBizType getOrderBizTypeByCode(Integer bizTypeCode) {
        if (bizTypeCode == null) {
            return null;
        }

        return Arrays.stream(OrderBizType.values())
                .filter(orderBizType -> orderBizType.getCode() == bizTypeCode)
                .findFirst()
                .orElse(null);
    }
}
