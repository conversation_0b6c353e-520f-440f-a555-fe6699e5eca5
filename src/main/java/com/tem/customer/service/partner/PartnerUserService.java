package com.tem.customer.service.partner;

import com.tem.customer.model.vo.common.UserOrderInfoVO;

import java.util.List;

/**
 * 企业用户服务接口
 *
 * <AUTHOR>
 * @since 2025-07-04
 */
public interface PartnerUserService {

    /**
     * 查询用户订单信息
     *
     * @param partnerId 企业ID
     * @param userId    用户ID
     * @return 用户订单信息列表
     */
    List<UserOrderInfoVO> getUserOrders(Long partnerId, Long userId);

}
