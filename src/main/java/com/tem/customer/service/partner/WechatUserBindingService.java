package com.tem.customer.service.partner;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tem.customer.repository.entity.WechatUserBinding;
import com.tem.customer.model.vo.partner.WechatUserBindingVO;

import java.util.List;

/**
 * 微信用户绑定关系服务接口
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
public interface WechatUserBindingService extends IService<WechatUserBinding> {

    /**
     * 根据企业ID查询绑定关系列表
     *
     * @param partnerId 企业ID
     * @return 绑定关系列表
     */
    List<WechatUserBinding> listByPartnerId(Long partnerId);

    /**
     * 根据企业ID和用户ID查询绑定关系
     *
     * @param partnerId 企业ID
     * @param userId 用户ID
     * @return 绑定关系列表
     */
    List<WechatUserBinding> listByPartnerIdAndUserId(Long partnerId, Long userId);


    /**
     * 根据UnionID查询绑定关系
     *
     * @param unionId 微信UnionID
     * @return 绑定关系
     */
    WechatUserBinding getByUnionId(String unionId);


    /**
     * 根据来源类型查询绑定关系列表
     *
     * @param partnerId 企业ID
     * @param sourceType 来源类型
     * @return 绑定关系列表
     */
    List<WechatUserBinding> listBySourceType(Long partnerId, String sourceType);

    /**
     * 根据企业ID统计绑定关系数量
     *
     * @param partnerId 企业ID
     * @return 绑定关系数量
     */
    int countByPartnerId(Long partnerId);

    /**
     * 根据企业ID统计有效绑定关系数量
     *
     * @param partnerId 企业ID
     * @return 有效绑定关系数量
     */
    int countValidByPartnerId(Long partnerId);

    /**
     * 创建微信用户绑定关系
     *
     * @param wechatUserBinding 微信用户绑定关系
     * @return 创建结果
     */
    boolean createWechatUserBinding(WechatUserBinding wechatUserBinding);

    /**
     * 更新微信用户绑定关系
     *
     * @param wechatUserBinding 微信用户绑定关系
     * @return 更新结果
     */
    boolean updateWechatUserBinding(WechatUserBinding wechatUserBinding);

    /**
     * 删除微信用户绑定关系
     *
     * @param id 记录ID
     * @return 删除结果
     */
    boolean deleteWechatUserBinding(Long id);

    /**
     * 启用/禁用微信用户绑定关系
     *
     * @param id 记录ID
     * @param status 状态：1-有效，0-无效
     * @return 操作结果
     */
    boolean updateStatus(Long id, Integer status);

    /**
     * 根据微信群ID查询绑定关系列表
     *
     * @param chatId 微信群ID
     * @return 绑定关系列表
     */
    List<WechatUserBinding> listByChatId(String chatId);

    /**
     * 根据企业ID和微信群ID查询绑定关系列表
     *
     * @param partnerId 企业ID
     * @param chatId 微信群ID
     * @return 绑定关系列表
     */
    List<WechatUserBinding> listByPartnerIdAndChatId(Long partnerId, String chatId);

    /**
     * 根据企业ID和用户ID查询用户所在的微信群绑定关系
     *
     * @param partnerId 企业ID
     * @param userId 用户ID
     * @return 绑定关系列表
     */
    List<WechatUserBinding> listGroupsByUserId(Long partnerId, Long userId);

    /**
     * 根据微信群ID和用户ID查询绑定关系
     *
     * @param chatId 微信群ID
     * @param userId 用户ID
     * @return 绑定关系
     */
    WechatUserBinding getByChatIdAndUserId(String chatId, Long userId);

    /**
     * 根据微信群ID统计绑定关系数量
     *
     * @param chatId 微信群ID
     * @return 绑定关系数量
     */
    int countByChatId(String chatId);

    /**
     * 根据微信群ID统计有效绑定关系数量
     *
     * @param chatId 微信群ID
     * @return 有效绑定关系数量
     */
    int countValidByChatId(String chatId);

    /**
     * 检查微信群和用户绑定关系是否已存在
     *
     * @param chatId 微信群ID
     * @param userId 用户ID
     * @param excludeId 排除的记录ID（更新时使用）
     * @return 是否存在
     */
    boolean existsByChatIdAndUserId(String chatId, Long userId, Long excludeId);

    /**
     * 创建群内人员绑定关系
     *
     * @param wechatUserBinding 绑定关系
     * @return 创建结果
     */
    boolean createGroupStaffBinding(WechatUserBinding wechatUserBinding);

    /**
     * 根据微信群ID查询绑定关系列表（包含群名称）
     *
     * @param chatId 微信群ID
     * @return 绑定关系VO列表
     */
    List<WechatUserBindingVO> listByChatIdWithGroupName(String chatId);

    /**
     * 根据企业ID和微信群ID查询绑定关系列表（包含群名称）
     *
     * @param partnerId 企业ID
     * @param chatId 微信群ID
     * @return 绑定关系VO列表
     */
    List<WechatUserBindingVO> listByPartnerIdAndChatIdWithGroupName(Long partnerId, String chatId);

    /**
     * 根据企业ID和用户ID查询用户所在的微信群绑定关系（包含群名称）
     *
     * @param partnerId 企业ID
     * @param userId 用户ID
     * @return 绑定关系VO列表
     */
    List<WechatUserBindingVO> listGroupsByUserIdWithGroupName(Long partnerId, Long userId);

    /**
     * 根据UnionID全局查询绑定关系（不限制企业ID）
     *
     * @param unionId 微信UnionID
     * @return 绑定关系
     */
    WechatUserBinding getByUnionIdGlobal(String unionId);


    /**
     * 根据UnionID查询绑定关系并填充完整信息
     *
     * @param unionId 微信UnionID
     * @return 填充完整信息的绑定关系VO
     */
    WechatUserBindingVO getByUnionIdWithFullInfo(String unionId);

    /**
     * 根据微信群ID和用户ID查询绑定关系并填充完整信息
     *
     * @param chatId 微信群ID
     * @param userId 用户ID
     * @return 填充完整信息的绑定关系VO
     */
    WechatUserBindingVO getByChatIdAndUserIdWithFullInfo(String chatId, Long userId);

    /**
     * 填充绑定关系列表的用户信息和企业信息
     *
     * @param bindings 绑定关系列表
     * @return 填充完整信息后的VO列表
     */
    List<WechatUserBindingVO> fillUserInfoList(List<WechatUserBinding> bindings);
}
