package com.tem.customer.service.qiyu;

import com.tem.customer.model.dto.qiyu.QiyuGroupInfoRequest;
import com.tem.customer.model.dto.qiyu.QiyuGroupInfoResponse;
import com.tem.customer.model.dto.qiyu.QiyuWechatUserInfoRequest;
import com.tem.customer.model.dto.qiyu.QiyuWechatUserInfoResponse;

/**
 * 七鱼CRM服务接口
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
public interface QiyuCrmService {

    /**
     * 获取微信生态用户信息
     *
     * @param request 微信生态用户信息请求
     * @return 微信用户信息响应
     */
    QiyuWechatUserInfoResponse getWechatUserInfo(QiyuWechatUserInfoRequest request);

    /**
     * 获取企微客服群聊信息
     *
     * @param request 群聊信息请求
     * @return 群聊信息响应
     */
    QiyuGroupInfoResponse getGroupInfo(QiyuGroupInfoRequest request);

}
