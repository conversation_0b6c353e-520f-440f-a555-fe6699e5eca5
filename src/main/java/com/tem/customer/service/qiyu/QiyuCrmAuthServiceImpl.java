package com.tem.customer.service.qiyu;

import com.iplatform.common.utils.LogUtils;
import com.tem.customer.infrastructure.config.QiyuCrmProperties;
import com.tem.customer.model.dto.qiyu.QiyuCrmRequest;
import com.tem.customer.shared.utils.QiyuCrmSignatureUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * 七鱼CRM认证服务实现
 * 
 * <AUTHOR>
 * @since 2025-07-17
 */
@Slf4j
@Service("qiyuCrmAuthService")
@RequiredArgsConstructor
public class QiyuCrmAuthServiceImpl implements QiyuCrmAuthService {

    private final QiyuTokenService qiyuTokenService;
    private final QiyuCrmProperties qiyuCrmProperties;

    /**
     * 验证七鱼CRM请求认证
     * 支持新版和老版认证方式
     *
     * @param request 七鱼CRM请求对象
     * @param requestBody 请求体内容（用于新版认证的MD5计算）
     * @param time URL参数中的时间戳（新版认证）
     * @param checksum URL参数中的校验和（新版认证）
     * @return 认证结果
     */
    @Override
    public boolean validateAuthentication(QiyuCrmRequest request, String requestBody, Long time, String checksum) {
        try {
            LogUtils.info(log, "开始验证七鱼CRM请求认证");

            // 优先尝试新版认证方式
            if (time != null && StringUtils.hasText(checksum)) {
                LogUtils.info(log, "使用新版认证方式验证");
                return validateNewAuthentication(requestBody, time, checksum);
            }

            // 回退到老版认证方式
            if (request != null && StringUtils.hasText(request.getAppid()) && StringUtils.hasText(request.getToken())) {
                LogUtils.info(log, "使用老版认证方式验证");
                return validateOldAuthentication(request);
            }

            LogUtils.warn(log, "七鱼CRM请求认证参数不完整");
            return false;

        } catch (Exception e) {
            LogUtils.error(log, "七鱼CRM请求认证验证异常", e);
            return false;
        }
    }

    /**
     * 验证新版认证方式
     * 使用URL参数中的time和checksum进行验证
     *
     * @param requestBody 请求体内容
     * @param time 时间戳
     * @param checksum 校验和
     * @return 验证结果
     */
    @Override
    public boolean validateNewAuthentication(String requestBody, Long time, String checksum) {
        try {
            LogUtils.info(log, "开始新版认证验证，时间戳: {}", time);

            // 验证参数完整性
            if (time == null || !StringUtils.hasText(checksum) || !StringUtils.hasText(requestBody)) {
                LogUtils.warn(log, "新版认证参数不完整");
                return false;
            }

            // 验证时间戳是否在容忍范围内
            var currentTime = System.currentTimeMillis() / 1000;
            var timeDiff = Math.abs(currentTime - time);
            if (timeDiff > qiyuCrmProperties.getAuth().getTimeTolerance()) {
                LogUtils.warn(log, "新版认证时间戳超出容忍范围，当前时间: {}, 请求时间: {}, 差异: {}秒",
                        currentTime, time, timeDiff);
                return false;
            }

            // 验证签名
            var isValid = QiyuCrmSignatureUtil.validateNewSignature(requestBody, time, checksum,
                    qiyuCrmProperties.getApp().getSecret());
            
            if (isValid) {
                LogUtils.info(log, "新版认证验证成功");
            } else {
                LogUtils.warn(log, "新版认证签名验证失败");
            }
            
            return isValid;

        } catch (Exception e) {
            LogUtils.error(log, "新版认证验证异常", e);
            return false;
        }
    }

    /**
     * 验证老版认证方式
     * 使用请求体中的appid和token进行验证
     *
     * @param request 七鱼CRM请求对象
     * @return 验证结果
     */
    @Override
    public boolean validateOldAuthentication(QiyuCrmRequest request) {
        try {
            LogUtils.info(log, "开始老版认证验证，AppId: {}", request.getAppid());

            // 验证参数完整性
            if (!StringUtils.hasText(request.getAppid()) || !StringUtils.hasText(request.getToken())) {
                LogUtils.warn(log, "老版认证参数不完整");
                return false;
            }

            // 验证AppId
            if (!qiyuCrmProperties.getApp().getKey().equals(request.getAppid())) {
                LogUtils.warn(log, "老版认证AppId验证失败");
                return false;
            }

            // 验证Token
            var isValid = validateToken(request.getAppid(), request.getToken());

            if (isValid) {
                LogUtils.info(log, "老版认证验证成功");
            } else {
                LogUtils.warn(log, "老版认证Token验证失败");
            }

            return isValid;

        } catch (Exception e) {
            LogUtils.error(log, "老版认证验证异常", e);
            return false;
        }
    }

    /**
     * 验证Token是否有效
     *
     * @param appid 应用ID
     * @param token Token值
     * @return 验证结果
     */
    @Override
    public boolean validateToken(String appid, String token) {
        try {
            LogUtils.info(log, "开始验证Token，AppId: {}", appid);

            // 验证参数
            if (!StringUtils.hasText(appid) || !StringUtils.hasText(token)) {
                LogUtils.warn(log, "Token验证参数不完整");
                return false;
            }

            // 验证AppId
            if (!qiyuCrmProperties.getApp().getKey().equals(appid)) {
                LogUtils.warn(log, "Token验证AppId不匹配");
                return false;
            }

            // 通过Token服务验证Token有效性
            var isValid = qiyuTokenService.validateToken(token);

            if (isValid) {
                LogUtils.info(log, "Token验证成功");
            } else {
                LogUtils.warn(log, "Token验证失败");
            }

            return isValid;

        } catch (Exception e) {
            LogUtils.error(log, "Token验证异常", e);
            return false;
        }
    }
}
