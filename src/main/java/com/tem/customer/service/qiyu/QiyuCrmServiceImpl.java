package com.tem.customer.service.qiyu;

import com.iplatform.common.ResponseDto;
import com.iplatform.common.utils.LogUtils;
import com.tem.customer.model.dto.qiyu.QiyuDataItem;
import com.tem.customer.model.dto.qiyu.QiyuGroupInfoRequest;
import com.tem.customer.model.dto.qiyu.QiyuGroupInfoResponse;
import com.tem.customer.model.dto.qiyu.QiyuWechatUserInfoRequest;
import com.tem.customer.model.dto.qiyu.QiyuWechatUserInfoResponse;
import com.tem.customer.repository.entity.PartnerWechatGroup;
import com.tem.customer.repository.entity.WechatUserBinding;
import com.tem.customer.service.partner.PartnerWechatGroupService;
import com.tem.customer.service.partner.WechatUserBindingService;
import com.tem.platform.api.UserService;
import com.tem.platform.api.dto.UserDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 七鱼CRM业务逻辑服务
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
@Slf4j
@Service("qiyuCrmService")
@RequiredArgsConstructor
public class QiyuCrmServiceImpl implements QiyuCrmService {

    /**
     * 默认用户级别
     */
    private static final Integer DEFAULT_USER_LEVEL = 1;

    /**
     * 参数错误消息
     */
    private static final String PARAM_ERROR_MESSAGE = "Missing required parameters: unionid and fromType";

    /**
     * 系统错误消息
     */
    private static final String SYSTEM_ERROR_MESSAGE = "System error";



    @DubboReference(timeout = 10000, retries = 0, check = false)
    private UserService userService;

    private final WechatUserBindingService wechatUserBindingService;

    private final PartnerWechatGroupService partnerWechatGroupService;

    /**
     * 获取微信生态用户信息
     *
     * @param request 微信生态用户信息请求
     * @return 微信用户信息响应
     */
    public QiyuWechatUserInfoResponse getWechatUserInfo(QiyuWechatUserInfoRequest request) {
        try {

            // 验证参数
            if (!request.validateWechatUserInfoParams()) {
                return QiyuWechatUserInfoResponse.paramError(PARAM_ERROR_MESSAGE);
            }

            // 根据fromType和unionid查找用户
            Long userId = findUserByWechatIdentifier(request);
            if (userId == null) {
                return buildUnboundUserResponse(request);
            }

            // 获取用户基本信息并构建响应
            return buildBoundUserResponse(userId, request);

        } catch (Exception e) {
            LogUtils.error(log, "获取七鱼CRM微信生态用户信息异常", e);
            return QiyuWechatUserInfoResponse.systemError(SYSTEM_ERROR_MESSAGE);
        }
    }

    /**
     * 构建未绑定用户响应
     *
     * @param request 微信用户信息请求
     * @return 未绑定用户响应
     */
    private QiyuWechatUserInfoResponse buildUnboundUserResponse(QiyuWechatUserInfoRequest request) {
        LogUtils.info(log, "未找到绑定用户，返回原始unionid值，FromType: {}, Unionid: {}",
                request.getFromType(), request.getUnionid());

        List<QiyuDataItem> userData = buildUnboundUserData(request);
        QiyuWechatUserInfoResponse response = QiyuWechatUserInfoResponse.successUnbound(userData);
        response.setUid(request.getUnionid());
        LogUtils.info(log, "返回未绑定用户信息，数据项数量: {}", userData.size());
        return response;
    }

    /**
     * 构建绑定用户响应
     *
     * @param userId  用户ID
     * @param request 微信用户信息请求
     * @return 绑定用户响应
     */
    private QiyuWechatUserInfoResponse buildBoundUserResponse(Long userId, QiyuWechatUserInfoRequest request) {
        // 获取用户基本信息
        ResponseDto<UserDto> userBaseInfo = userService.getUserBaseInfo(userId);
        if (userBaseInfo.isNotSuccess() || userBaseInfo.getData() == null) {
            LogUtils.warn(log, "根据用户ID获取用户信息失败，用户ID: {}", userId);
            return buildUnboundUserResponse(request);
        }

        UserDto userDto = userBaseInfo.getData();
        List<QiyuDataItem> userData = buildWechatUserData(userId, userDto, request);

        // 构建响应数据
        final String uid = request.getUnionid();
        final String name = userDto.getFullname();
        final String email = userDto.getEmail();
        final String mobile = userDto.getMobile();

        QiyuWechatUserInfoResponse response = QiyuWechatUserInfoResponse.success(
                uid, name, email, mobile, DEFAULT_USER_LEVEL, userData);

        LogUtils.info(log, "成功获取七鱼CRM微信生态用户信息，用户ID: {}, UID: {}, 数据项: {}",
                userId, uid, userData);
        return response;
    }

    /**
     * 获取企微客服群聊信息
     *
     * @param request 群聊信息请求
     * @return 群聊信息响应
     */
    public QiyuGroupInfoResponse getGroupInfo(QiyuGroupInfoRequest request) {
        try {

            // 验证参数
            if (!request.validateGroupInfoParams()) {
                return QiyuGroupInfoResponse.paramError(
                        "Missing required parameters: chatId and fromType");
            }

            Long partnerId = findPartnerByChatId(request.getChatId());

            // 构建群聊数据
            List<QiyuDataItem> groupData = buildGroupData(partnerId, request);

            // 设置默认级别
            Integer level = 1;

            QiyuGroupInfoResponse response = QiyuGroupInfoResponse.success(level, groupData);
            response.setUid(request.getChatId());

            LogUtils.info(log, "成功获取七鱼CRM企微群聊信息，群ID: {}, 数据项: {}", request.getChatId(), groupData);
            return response;

        } catch (Exception e) {
            LogUtils.error(log, "获取七鱼CRM企微群聊信息异常，群ID: {}", request.getChatId(), e);
            return QiyuGroupInfoResponse.systemError("System error");
        }
    }

    /**
     * 根据chatId查找绑定的企业ID
     *
     * @param chatId 企业微信群ID
     * @return 企业ID，如果未找到绑定关系则返回null
     */
    private Long findPartnerByChatId(String chatId) {
        if (!StringUtils.hasText(chatId)) {
            LogUtils.warn(log, "查询企业微信群绑定关系时，chatId为空");
            return null;
        }

        try {
            PartnerWechatGroup group = partnerWechatGroupService.getByChatId(chatId);
            if (group != null) {
                LogUtils.info(log, "根据chatId查询到企业微信群绑定关系，chatId: {}, partnerId: {}",
                        chatId, group.getPartnerId());
                return group.getPartnerId();
            } else {
                LogUtils.warn(log, "根据chatId未找到企业微信群绑定关系，chatId: {}", chatId);
                return null;
            }
        } catch (Exception e) {
            LogUtils.error(log, "根据chatId查询企业微信群绑定关系异常，chatId: {}", chatId, e);
            return null;
        }
    }


    /**
     * 构建微信生态用户数据
     *
     * @param userId  用户ID
     * @param userDto 用户信息
     * @param request 请求对象
     * @return 用户数据列表
     */
    private List<QiyuDataItem> buildWechatUserData(Long userId, UserDto userDto, QiyuWechatUserInfoRequest request) {
        List<QiyuDataItem> dataList = new ArrayList<>();

        // 按照示例格式构建数据项
        // 添加映射字段
        if (userDto.getFullname() != null) {
            dataList.add(QiyuDataItem.builder()
                    .key("real_name")
                    .value(userDto.getFullname())
                    .build());
        }

        if (userDto.getMobile() != null) {
            dataList.add(QiyuDataItem.builder()
                    .key("mobile_phone")
                    .value(userDto.getMobile())
                    .build());
        }

        if (userDto.getEmail() != null) {
            dataList.add(QiyuDataItem.builder()
                    .key("email")
                    .value(userDto.getEmail())
                    .build());
        }

        // 添加带索引的数据项
        dataList.add(QiyuDataItem.builder()
                .index(0)
                .key("account")
                .label("账号")
                .value(String.valueOf(userId))
                .build());


        if (request.getUnionid() != null) {
            dataList.add(QiyuDataItem.simpleWithCustomField("UnionID", request.getUnionid(), "UnionID", true));
        }

        // 其他请求参数
        if (request.getOpenid() != null) {
            dataList.add(QiyuDataItem.simple("openid", request.getOpenid(), "微信三方ID"));
        }

        if (request.getFromType() != null) {
            dataList.add(QiyuDataItem.simple("fromType", request.getFromType(), "来源类型"));
        }

        if (request.getWxworkUserId() != null) {
            dataList.add(QiyuDataItem.simple("wxworkUserId", request.getWxworkUserId(), "企微助手ID"));

        }

        if (request.getWxworkUserName() != null) {
            dataList.add(QiyuDataItem.simple("wxworkUserName", request.getWxworkUserName(), "企微助手名字"));
            dataList.add(QiyuDataItem.simpleWithCustomField("wxName", request.getWxworkUserName(), "微信号", true));
        }


        return dataList;
    }

    /**
     * 构建未绑定用户数据
     *
     * @param request 请求对象
     * @return 用户数据列表
     */
    private List<QiyuDataItem> buildUnboundUserData(QiyuWechatUserInfoRequest request) {
        List<QiyuDataItem> dataList = new ArrayList<>();

        if (request.getUnionid() != null) {
            dataList.add(QiyuDataItem.simpleWithCustomField("UnionID", request.getUnionid(), "UnionID", true));
        }
        if (request.getOpenid() != null) {
            dataList.add(QiyuDataItem.simple("openid", request.getOpenid(), "微信三方ID"));
        }

        if (request.getFromType() != null) {
            dataList.add(QiyuDataItem.simple("fromType", request.getFromType(), "来源类型"));
        }

        if (request.getWxworkUserId() != null) {
            dataList.add(QiyuDataItem.simple("wxworkUserId", request.getWxworkUserId(), "企微助手ID"));
        }

        if (request.getWxworkUserName() != null) {
            dataList.add(QiyuDataItem.simple("wxworkUserName", request.getWxworkUserName(), "企微助手名字"));
            dataList.add(QiyuDataItem.simpleWithCustomField("wxName", request.getWxworkUserName(), "微信号", true));
        }


        // 未绑定用户的默认信息
        dataList.add(QiyuDataItem.simple("status", "unbound", "绑定状态"));

        return dataList;
    }

    /**
     * 构建群聊数据
     *
     * @param partnerId 企业ID
     * @param request   请求对象
     * @return 群聊数据列表
     */
    private List<QiyuDataItem> buildGroupData(Long partnerId, QiyuGroupInfoRequest request) {
        List<QiyuDataItem> dataList = new ArrayList<>();

        // 添加群聊ID
        dataList.add(QiyuDataItem.simpleWithCustomField("chatId", request.getChatId(), "ChatID", true));

        return dataList;
    }

    /**
     * 根据微信标识符查找用户
     *
     * @param request 微信用户信息请求
     * @return 用户ID
     */
    private Long findUserByWechatIdentifier(QiyuWechatUserInfoRequest request) {
        try {
            final String fromType = request.getFromType();
            final String unionid = request.getUnionid();

            LogUtils.info(log, "开始根据微信标识符查找用户，FromType: {}, Unionid: {}", fromType, unionid);

            if (!StringUtils.hasText(unionid)) {
                LogUtils.warn(log, "Unionid为空，无法查找用户");
                return null;
            }

            // 根据fromType判断unionid的含义并查找绑定关系
            LogUtils.info(log, "微信生态渠道，使用UnionId查找用户: {}", unionid);
            WechatUserBinding binding = wechatUserBindingService.getByUnionIdGlobal(unionid);
            if(null == binding){
                LogUtils.info(log, "未找到绑定用户，使用UnionId查找用户: {}", unionid);
                return null;
            }
            return binding.getUserId();

        } catch (Exception e) {
            LogUtils.error(log, "根据微信标识符查找用户时发生异常，FromType: {}, Unionid: {}",
                    request.getFromType(), request.getUnionid(), e);
            return null;
        }
    }


}
