package com.tem.customer.service.qiyu;

import com.tem.customer.model.dto.qiyu.QiyuCrmRequest;

/**
 * 七鱼CRM认证服务接口
 * 
 * <AUTHOR>
 * @since 2025-07-17
 */
public interface QiyuCrmAuthService {

    /**
     * 验证七鱼CRM请求认证
     * 支持新版和老版认证方式
     *
     * @param request 七鱼CRM请求对象
     * @param requestBody 请求体内容（用于新版认证的MD5计算）
     * @param time URL参数中的时间戳（新版认证）
     * @param checksum URL参数中的校验和（新版认证）
     * @return 认证结果
     */
    boolean validateAuthentication(QiyuCrmRequest request, String requestBody, Long time, String checksum);

    /**
     * 验证新版认证方式
     * 使用URL参数中的time和checksum进行验证
     *
     * @param requestBody 请求体内容
     * @param time 时间戳
     * @param checksum 校验和
     * @return 验证结果
     */
    boolean validateNewAuthentication(String requestBody, Long time, String checksum);

    /**
     * 验证老版认证方式
     * 使用请求体中的appid和token进行验证
     *
     * @param request 七鱼CRM请求对象
     * @return 验证结果
     */
    boolean validateOldAuthentication(QiyuCrmRequest request);

    /**
     * 验证Token是否有效
     *
     * @param appid 应用ID
     * @param token Token值
     * @return 验证结果
     */
    boolean validateToken(String appid, String token);
}
