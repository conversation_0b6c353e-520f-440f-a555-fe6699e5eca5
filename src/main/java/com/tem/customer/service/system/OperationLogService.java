package com.tem.customer.service.system;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.tem.customer.model.dto.system.OperationLogQueryDTO;
import com.tem.customer.repository.entity.OperationLog;
import com.tem.customer.shared.enums.BusinessType;
import com.tem.customer.shared.enums.OperationType;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 操作记录日志服务接口
 *
 * <AUTHOR>
 * @since 2025-06-23
 */
public interface OperationLogService extends IService<OperationLog> {

    /**
     * 记录操作日志
     *
     * @param businessType    业务类型
     * @param businessId      业务ID
     * @param operationType   操作类型
     * @param description     操作描述
     * @param targetPartnerId 目标企业ID
     * @param executionTime   执行耗时（毫秒）
     * @return 是否记录成功
     */
    boolean recordLog(BusinessType businessType, Long businessId, OperationType operationType,
                      String description, Long targetPartnerId, Integer executionTime);

    /**
     * 异步记录操作日志
     *
     * @param businessType    业务类型
     * @param businessId      业务ID
     * @param operationType   操作类型
     * @param description     操作描述
     * @param targetPartnerId 目标企业ID
     * @param executionTime   执行耗时（毫秒）
     */
    void recordLogAsync(BusinessType businessType, Long businessId, OperationType operationType,
                        String description, Long targetPartnerId, Integer executionTime);

    /**
     * 根据业务类型和业务ID查询操作日志列表
     *
     * @param businessType 业务类型
     * @param businessId   业务ID
     * @return 操作日志列表
     */
    List<OperationLog> listByBusinessTypeAndId(BusinessType businessType, Long businessId);

    /**
     * 根据目标企业ID查询操作日志列表
     *
     * @param targetPartnerId 目标企业ID
     * @return 操作日志列表
     */
    List<OperationLog> listByTargetPartnerId(Long targetPartnerId);

    /**
     * 根据操作人ID查询操作日志列表
     *
     * @param operatorId 操作人ID
     * @return 操作日志列表
     */
    List<OperationLog> listByOperatorId(Long operatorId);

    /**
     * 分页查询操作日志
     *
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    IPage<OperationLog> pageQuery(OperationLogQueryDTO queryDTO);

    /**
     * 统计指定时间范围内的操作日志数量
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 操作日志数量
     */
    int countByTimeRange(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 清理历史操作日志
     *
     * @param beforeTime 清理此时间之前的日志
     * @return 清理的记录数
     */
    int cleanHistoryLogs(LocalDateTime beforeTime);

    /**
     * 根据业务类型统计操作日志数量
     *
     * @param businessType 业务类型
     * @return 操作日志数量
     */
    int countByBusinessType(BusinessType businessType);

    /**
     * 根据操作类型统计操作日志数量
     *
     * @param operationType 操作类型
     * @return 操作日志数量
     */
    int countByOperationType(OperationType operationType);
}
