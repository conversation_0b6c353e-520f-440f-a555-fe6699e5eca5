package com.tem.customer.service.system;


import com.iplatform.common.utils.LogUtils;
import com.tem.imgserver.client.ImgClient;
import com.tem.imgserver.client.UploadResult;
import com.tem.platform.api.FileService;
import com.tem.platform.api.dto.FileDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * {@code @Author:} fumouren
 * {@code @CreateTime:} 2025-06-20 11:32
 * {@code @Description:}
 */
@Slf4j
@Service("imageService")
public class ImageServiceImpl implements ImageService {

    @DubboReference(timeout = 3000, retries = 0, check = false)
    private FileService fileService;

    @Override
    public String uploadImage(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new IllegalArgumentException("上传文件不能为空");
        }

        String originalFilename = file.getOriginalFilename();

        // 生成唯一文件名（标准UUID格式）
        String uuid = UUID.randomUUID().toString();
        String fileType = StringUtils.substringAfterLast(originalFilename, ".");
        String fileName = uuid + "." + fileType;

        try {
            // 使用ImgClient上传图片
            UploadResult uploadResult = ImgClient.uploadImg2(file.getInputStream(), "customer", fileName);
            LogUtils.info(log, "图片上传返回结果:{}", uploadResult);

            // 保存文件记录到fileService
            FileDto fileDto = new FileDto();
            fileDto.setBizType("CUSTOMER");
            fileDto.setFileId(uuid);
            fileDto.setFileName(originalFilename);
            fileDto.setSize(file.getSize());
            fileDto.setFileKey(uploadResult.getFileKey());
            fileDto.setUploadTime(new Date());
            fileDto.setFileType(fileType);

            fileService.insert(fileDto);

            // 返回文件访问URL或文件Key
            return uploadResult.getFileKey();

        } catch (Exception e) {
            log.error("图片上传失败，文件名: {}, 错误信息: {}", originalFilename, e.getMessage(), e);
            throw new RuntimeException("图片上传失败: " + e.getMessage(), e);
        }
    }

    @Override
    public Map<String, Object> uploadImages(List<MultipartFile> files) {
        if (files == null || files.isEmpty()) {
            throw new IllegalArgumentException("上传文件列表不能为空");
        }

        // 统计计数器
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger failCount = new AtomicInteger(0);

        // 结果列表
        List<Map<String, Object>> successFiles = new ArrayList<>();
        List<Map<String, Object>> failedFiles = new ArrayList<>();

        // 遍历处理每个文件
        for (MultipartFile file : files) {
            try {
                // 基本验证
                if (file == null || file.isEmpty()) {
                    addFailedFile(failedFiles, file, "文件为空");
                    failCount.incrementAndGet();
                    continue;
                }

                // 调用单文件上传方法
                String fileKey = uploadImage(file);

                // 构建成功文件信息
                Map<String, Object> successFile = new HashMap<>();
                successFile.put("fileName", file.getOriginalFilename());
                successFile.put("fileKey", fileKey);
                successFile.put("fileSize", file.getSize());
                successFile.put("contentType", file.getContentType());

                successFiles.add(successFile);
                successCount.incrementAndGet();

                LogUtils.info(log, "文件上传成功: {}", file.getOriginalFilename());

            } catch (Exception e) {
                // 记录失败文件
                String errorMessage = e.getMessage();
                if (errorMessage != null && errorMessage.startsWith("图片上传失败: ")) {
                    errorMessage = errorMessage.substring("图片上传失败: ".length());
                }
                addFailedFile(failedFiles, file, errorMessage);
                failCount.incrementAndGet();

                LogUtils.warn(log, "文件上传失败: {}, 错误: {}",
                    file != null ? file.getOriginalFilename() : "unknown", e.getMessage());
            }
        }

        // 构建返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("totalCount", files.size());
        result.put("successCount", successCount.get());
        result.put("failCount", failCount.get());
        result.put("successFiles", successFiles);
        result.put("failedFiles", failedFiles);

        LogUtils.info(log, "批量上传完成，总数: {}, 成功: {}, 失败: {}",
            files.size(), successCount.get(), failCount.get());

        return result;
    }

    /**
     * 添加失败文件信息
     */
    private void addFailedFile(List<Map<String, Object>> failedFiles, MultipartFile file, String error) {
        Map<String, Object> failedFile = new HashMap<>();
        failedFile.put("fileName", file != null ? file.getOriginalFilename() : "unknown");
        failedFile.put("error", error);
        if (file != null) {
            failedFile.put("fileSize", file.getSize());
            failedFile.put("contentType", file.getContentType());
        }
        failedFiles.add(failedFile);
    }
}
