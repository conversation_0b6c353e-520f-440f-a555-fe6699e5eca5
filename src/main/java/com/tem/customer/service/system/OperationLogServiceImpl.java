package com.tem.customer.service.system;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.iplatform.common.utils.LogUtils;
import com.tem.customer.model.dto.system.OperationLogQueryDTO;
import com.tem.customer.repository.entity.OperationLog;
import com.tem.customer.shared.enums.BusinessType;
import com.tem.customer.shared.enums.OperationType;
import com.tem.customer.repository.mapper.OperationLogMapper;
import com.tem.customer.shared.utils.UserContextUtil;
import com.tem.customer.shared.utils.WebUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 操作记录日志服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-23
 */
@Slf4j
@Service("operationLogService")
public class OperationLogServiceImpl extends ServiceImpl<OperationLogMapper, OperationLog> implements OperationLogService {

    /**
     * 单次查询最大记录数限制
     */
    private static final int MAX_QUERY_LIMIT = 10000;

    @Override
    public boolean recordLog(BusinessType businessType, Long businessId, OperationType operationType,
                             String description, Long targetPartnerId, Integer executionTime) {
        try {
            // 容错处理：如果business_id为null，记录警告日志但不抛出异常，避免影响业务流程
            if (businessId == null) {
                LogUtils.warn(log, "操作日志记录时business_id为null，跳过记录。业务类型: {}, 操作类型: {}, 描述: {}",
                        businessType, operationType, description);
                return false;
            }

            OperationLog operationLog = buildOperationLog(businessType, businessId, operationType,
                    description, targetPartnerId, executionTime, null);
            return save(operationLog);
        } catch (Exception e) {
            LogUtils.error(log, "记录操作日志失败", e);
            return false;
        }
    }

    @Override
    public void recordLogAsync(BusinessType businessType, Long businessId, OperationType operationType,
                               String description, Long targetPartnerId, Integer executionTime) {
        // 在主线程中获取用户上下文快照
        UserContextUtil.UserContextSnapshot userSnapshot = UserContextUtil.getCurrentUserSnapshot();

        // 调用内部异步方法
        recordLogAsyncInternal(businessType, businessId, operationType, description, targetPartnerId, executionTime, userSnapshot);
    }

    /**
     * 内部异步记录方法
     */
    @Async("asyncExecutor")
    protected void recordLogAsyncInternal(BusinessType businessType, Long businessId, OperationType operationType,
                                          String description, Long targetPartnerId, Integer executionTime,
                                          UserContextUtil.UserContextSnapshot userSnapshot) {
        try {
            // 容错处理：如果business_id为null，记录警告日志但不抛出异常，避免影响业务流程和监控报警
            if (businessId == null) {
                LogUtils.warn(log, "异步操作日志记录时business_id为null，跳过记录。业务类型: {}, 操作类型: {}, 描述: {}",
                        businessType, operationType, description);
                return;
            }

            OperationLog operationLog = buildOperationLog(businessType, businessId, operationType,
                    description, targetPartnerId, executionTime, userSnapshot);
            save(operationLog);
            if (log.isDebugEnabled()) {
                LogUtils.debug(log, "异步记录操作日志成功: {}", operationLog);
            }
        } catch (Exception e) {
            LogUtils.error(log, "异步记录操作日志失败", e);
        }
    }

    @Override
    public List<OperationLog> listByBusinessTypeAndId(BusinessType businessType, Long businessId) {
        if (businessType == null || businessId == null) {
            return List.of();
        }
        return baseMapper.selectByBusinessTypeAndId(businessType.getCode(), businessId);
    }

    @Override
    public List<OperationLog> listByTargetPartnerId(Long targetPartnerId) {
        if (targetPartnerId == null) {
            return List.of();
        }
        return baseMapper.selectByTargetPartnerId(targetPartnerId);
    }

    @Override
    public List<OperationLog> listByOperatorId(Long operatorId) {
        if (operatorId == null) {
            return List.of();
        }
        return baseMapper.selectByOperatorId(operatorId);
    }

    @Override
    public IPage<OperationLog> pageQuery(OperationLogQueryDTO queryDTO) {
        if (queryDTO == null) {
            queryDTO = new OperationLogQueryDTO();
        }

        // 限制查询数量，防止大数据量查询
        int pageSize = Math.min(queryDTO.getPageSize(), MAX_QUERY_LIMIT);
        Page<OperationLog> page = new Page<>(queryDTO.getPageNum(), pageSize);

        return baseMapper.selectPageWithConditions(
                page,
                queryDTO.getBusinessType(),
                queryDTO.getOperationType(),
                queryDTO.getTargetPartnerId(),
                queryDTO.getOperatorId(),
                queryDTO.getStartTime(),
                queryDTO.getEndTime()
        );
    }

    @Override
    public int countByTimeRange(LocalDateTime startTime, LocalDateTime endTime) {
        if (startTime == null || endTime == null) {
            return 0;
        }
        return baseMapper.countByTimeRange(startTime, endTime);
    }

    @Override
    public int cleanHistoryLogs(LocalDateTime beforeTime) {
        if (beforeTime == null) {
            return 0;
        }
        return baseMapper.deleteByCreateTimeBefore(beforeTime);
    }

    @Override
    public int countByBusinessType(BusinessType businessType) {
        if (businessType == null) {
            return 0;
        }
        return baseMapper.countByBusinessType(businessType.getCode());
    }

    @Override
    public int countByOperationType(OperationType operationType) {
        if (operationType == null) {
            return 0;
        }
        return baseMapper.countByOperationType(operationType.getCode());
    }

    /**
     * 构建操作日志对象
     *
     * @param userSnapshot 用户上下文快照，用于异步场景，如果为null则实时获取
     */
    private OperationLog buildOperationLog(BusinessType businessType,
                                           Long businessId,
                                           OperationType operationType,
                                           String description,
                                           Long targetPartnerId,
                                           Integer executionTime,
                                           UserContextUtil.UserContextSnapshot userSnapshot) {
        OperationLog operationLog = new OperationLog();

        // 基本信息
        operationLog.setBusinessType(businessType.getCode())
                .setBusinessId(businessId)
                .setOperationType(operationType.getCode())
                .setOperationDesc(description)
                .setTargetPartnerId(targetPartnerId)
                .setExecutionTime(executionTime);

        // 手动设置创建时间和创建人，确保不为null
        LocalDateTime now = LocalDateTime.now();
        operationLog.setCreateTime(now);

        // 操作人信息
        try {
            if (userSnapshot != null) {
                // 使用传入的用户快照（异步场景）
                operationLog.setOperatorId(userSnapshot.userId())
                        .setOperatorName(userSnapshot.fullname())
                        .setOperatorUsername(userSnapshot.fullname())
                        .setPartnerId(userSnapshot.partnerId())
                        .setCreateBy(userSnapshot.userId() != null ? userSnapshot.userId().toString() : null);

                if (log.isDebugEnabled()) {
                    LogUtils.debug(log, "使用用户上下文快照设置操作人信息: userId={}, username={}",
                            userSnapshot.userId(), userSnapshot.username());
                }
            } else {
                // 实时获取用户信息（同步场景）
                Long operatorId = UserContextUtil.getCurrentUserId();
                String operatorName = UserContextUtil.getCurrentUserFullname();
                Long partnerId = UserContextUtil.getCurrentUserPartnerId();

                operationLog.setOperatorId(operatorId)
                        .setOperatorName(operatorName)
                        .setOperatorUsername(operatorName)
                        .setPartnerId(partnerId)
                        .setCreateBy(operatorId != null ? operatorId.toString() : null);

                if (log.isDebugEnabled()) {
                    LogUtils.debug(log, "实时获取操作人信息: userId={}, username={}", operatorId, operatorName);
                }
            }
        } catch (Exception e) {
            LogUtils.error(log, "获取操作人信息失败", e);
        }

        // 请求信息
        try {
            String ipAddress = WebUtil.getClientIpAddress();
            String userAgent = WebUtil.getUserAgent();
            String requestUri = WebUtil.getRequestUri();
            String requestMethod = WebUtil.getRequestMethod();

            operationLog.setIpAddress(ipAddress)
                    .setUserAgent(userAgent)
                    .setRequestUri(requestUri)
                    .setRequestMethod(requestMethod);
        } catch (Exception e) {
            LogUtils.error(log, "获取请求信息失败", e);
        }

        return operationLog;
    }
}
