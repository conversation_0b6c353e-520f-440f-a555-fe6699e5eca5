package com.tem.customer.service.wechat;

import com.tem.customer.model.dto.wechat.WechatCustomerDetailRequest;
import com.tem.customer.model.dto.wechat.WechatCustomerDetailResponse;
import com.tem.customer.model.dto.wechat.WechatGroupDetailRequest;
import com.tem.customer.model.dto.wechat.WechatGroupDetailResponse;

/**
 * 企业微信API服务接口
 *
 * <AUTHOR>
 * @since 2025-07-31
 */
public interface WechatApiService {

    /**
     * 获取客户群详情
     * 通过客户群ID，获取详情。包括群名、群成员列表、群成员入群时间、入群方式。
     *
     * @param request 群详情请求
     * @return 群详情响应
     */
    WechatGroupDetailResponse getGroupDetail(WechatGroupDetailRequest request);

    /**
     * 获取客户群详情
     * 通过客户群ID，获取详情。包括群名、群成员列表、群成员入群时间、入群方式。
     *
     * @param chatId   客户群ID
     * @param needName 是否需要返回群成员的名字，0-不返回；1-返回
     * @return 群详情响应
     */
    WechatGroupDetailResponse getGroupDetail(String chatId, Integer needName);

    /**
     * 获取客户群详情（默认返回群成员名字）
     * 通过客户群ID，获取详情。包括群名、群成员列表、群成员入群时间、入群方式。
     *
     * @param chatId 客户群ID
     * @return 群详情响应
     */
    WechatGroupDetailResponse getGroupDetail(String chatId);

    /**
     * 获取Access Token
     * 企业微信API调用凭证
     *
     * @return Access Token
     */
    String getAccessToken();

    /**
     * 刷新Access Token
     * 强制刷新Access Token缓存
     *
     * @return 新的Access Token
     */
    String refreshAccessToken();

    /**
     * 获取客户详情
     * 通过外部联系人的userid，获取客户详情。包括昵称、头像、性别等信息。
     *
     * @param request 客户详情请求
     * @return 客户详情响应
     */
    WechatCustomerDetailResponse getCustomerDetail(WechatCustomerDetailRequest request);

    /**
     * 获取客户详情
     * 通过外部联系人的userid，获取客户详情。包括昵称、头像、性别等信息。
     *
     * @param externalUserId 外部联系人的userid
     * @return 客户详情响应
     */
    WechatCustomerDetailResponse getCustomerDetail(String externalUserId);
}
