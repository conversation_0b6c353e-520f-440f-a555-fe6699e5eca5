package com.tem.customer.infrastructure.filter;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.iplatform.common.utils.LogUtils;
import com.tem.customer.infrastructure.config.QiyuCrmProperties;
import com.tem.customer.infrastructure.web.CachedBodyHttpServletRequest;
import com.tem.customer.model.dto.qiyu.QiyuCrmRequest;
import com.tem.customer.model.dto.qiyu.QiyuCrmResponse;
import com.tem.customer.service.qiyu.QiyuCrmAuthService;
import jakarta.servlet.FilterChain;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.io.PrintWriter;

/**
 * 七鱼CRM认证过滤器
 * 替代拦截器实现，解决请求体只能读取一次的问题
 * 
 * <AUTHOR>
 * @since 2025-07-28
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class QiyuCrmAuthFilter extends OncePerRequestFilter {

    private final QiyuCrmAuthService qiyuCrmAuthService;
    private final QiyuCrmProperties qiyuCrmProperties;
    private final ObjectMapper objectMapper;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, 
                                    FilterChain filterChain) throws IOException {
        try {
            LogUtils.info(log, "七鱼CRM认证过滤器开始处理请求: {}", request.getRequestURI());

            // 处理OPTIONS预检请求
            if (HttpMethod.OPTIONS.matches(request.getMethod())) {
                LogUtils.info(log, "处理OPTIONS预检请求");
                response.setStatus(HttpServletResponse.SC_OK);
                return;
            }

            // 只对POST请求进行认证
            if (!HttpMethod.POST.matches(request.getMethod())) {
                LogUtils.warn(log, "非POST请求，跳过认证: {}", request.getMethod());
                filterChain.doFilter(request, response);
                return;
            }

            // 检查是否需要七鱼CRM认证
            if (!needsQiyuCrmAuth(request)) {
                LogUtils.info(log, "无需七鱼CRM认证，跳过认证: {}", request.getRequestURI());
                filterChain.doFilter(request, response);
                return;
            }

            // 获取认证参数（新版认证方式）
            Long time = getTimeParameter(request);
            String checksum = getChecksumParameter(request);
            LogUtils.info(log, "获取到的认证参数: time={}, checksum={}", time, checksum);

            // 创建可重复读取的请求包装器
            CachedBodyHttpServletRequest wrappedRequest = new CachedBodyHttpServletRequest(request);
            
            // 读取请求体
            String requestBody = wrappedRequest.getCachedBody();
            LogUtils.info(log, "请求体内容: {}", requestBody);

            // 解析请求体中的认证参数（老版认证方式）
            QiyuCrmRequest crmRequest = parseRequestBody(requestBody);

            // 进行认证验证
            boolean isAuthenticated = qiyuCrmAuthService.validateAuthentication(crmRequest, requestBody, time, checksum);

            if (!isAuthenticated) {
                LogUtils.warn(log, "七鱼CRM认证失败");
                writeAuthFailureResponse(response);
                return;
            }

            LogUtils.info(log, "七鱼CRM认证成功");
            
            // 使用包装后的请求继续处理
            filterChain.doFilter(wrappedRequest, response);

        } catch (Exception e) {
            LogUtils.error(log, "七鱼CRM认证过滤器异常", e);
            writeSystemErrorResponse(response);
        }
    }

    /**
     * 判断是否需要七鱼CRM认证
     * 在过滤器阶段，直接使用路径匹配判断
     */
    private boolean needsQiyuCrmAuth(HttpServletRequest request) {
        // 通过配置的路径匹配判断
        String requestUri = request.getRequestURI();
        boolean isQiyuPath = qiyuCrmProperties.isQiyuCrmPath(requestUri);
        if (isQiyuPath) {
            LogUtils.info(log, "通过路径匹配识别为七鱼CRM请求: {}", requestUri);
        }
        return isQiyuPath;
    }

    /**
     * 获取时间参数
     */
    private Long getTimeParameter(HttpServletRequest request) {
        // 首先尝试从header获取
        String timeStr = request.getHeader("time");
        if (StringUtils.hasText(timeStr)) {
            try {
                return Long.parseLong(timeStr);
            } catch (NumberFormatException e) {
                LogUtils.warn(log, "header中时间参数格式错误: {}", timeStr);
            }
        }

        // 如果header中没有，再从URL参数获取
        timeStr = request.getParameter("time");
        if (StringUtils.hasText(timeStr)) {
            try {
                return Long.parseLong(timeStr);
            } catch (NumberFormatException e) {
                LogUtils.warn(log, "URL参数中时间参数格式错误: {}", timeStr);
            }
        }

        return null;
    }

    /**
     * 获取校验和参数
     */
    private String getChecksumParameter(HttpServletRequest request) {
        // 首先尝试从header获取
        String checksum = request.getHeader("checksum");
        if (StringUtils.hasText(checksum)) {
            return checksum;
        }

        // 如果header中没有，再从URL参数获取
        checksum = request.getParameter("checksum");
        if (StringUtils.hasText(checksum)) {
            return checksum;
        }

        return null;
    }

    /**
     * 解析请求体中的认证参数
     */
    private QiyuCrmRequest parseRequestBody(String requestBody) {
        try {
            if (!StringUtils.hasText(requestBody)) {
                return new QiyuCrmRequest();
            }

            // 尝试解析为JSON
            return objectMapper.readValue(requestBody, QiyuCrmRequest.class);

        } catch (Exception e) {
            LogUtils.warn(log, "解析请求体失败: {}", e.getMessage());
            return new QiyuCrmRequest();
        }
    }

    /**
     * 写入认证失败响应
     */
    private void writeAuthFailureResponse(HttpServletResponse response) throws IOException {
        response.setStatus(HttpServletResponse.SC_OK);
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        response.setCharacterEncoding("UTF-8");

        QiyuCrmResponse errorResponse = new QiyuCrmResponse();
        errorResponse.setRlt(401);
        errorResponse.setMessage("Authentication failed");

        try (PrintWriter writer = response.getWriter()) {
            writer.write(objectMapper.writeValueAsString(errorResponse));
            writer.flush();
        }
    }

    /**
     * 写入系统错误响应
     */
    private void writeSystemErrorResponse(HttpServletResponse response) throws IOException {
        response.setStatus(HttpServletResponse.SC_OK);
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        response.setCharacterEncoding("UTF-8");

        QiyuCrmResponse errorResponse = new QiyuCrmResponse();
        errorResponse.setRlt(500);
        errorResponse.setMessage("System error");

        try (PrintWriter writer = response.getWriter()) {
            writer.write(objectMapper.writeValueAsString(errorResponse));
            writer.flush();
        }
    }
}
