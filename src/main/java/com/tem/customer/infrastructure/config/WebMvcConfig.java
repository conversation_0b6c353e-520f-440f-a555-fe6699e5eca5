package com.tem.customer.infrastructure.config;

import cn.dev33.satoken.fun.SaParamFunction;
import cn.dev33.satoken.interceptor.SaInterceptor;
import cn.dev33.satoken.router.SaRouter;
import cn.dev33.satoken.stp.StpUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.iplatform.common.utils.LogUtils;
import com.tem.customer.infrastructure.auth.LoginFilterCompatibilityService;
import com.tem.customer.infrastructure.interceptor.TraceIdInterceptor;
import com.tem.customer.infrastructure.interceptor.UserContextCleanupInterceptor;
import com.tem.customer.shared.common.Constant;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.TimeZone;

/**
 * Web MVC配置类
 * 用于注册拦截器等Web相关配置，整合sa-token权限认证
 *
 * <AUTHOR>
 * @since 2025-05-29
 */
@Slf4j
@Configuration
@ComponentScan
public class WebMvcConfig implements WebMvcConfigurer {

    private final UserContextCleanupInterceptor userContextCleanupInterceptor;
    private final LoginFilterCompatibilityService loginFilterCompatibilityService;

    public WebMvcConfig(LoginFilterCompatibilityService loginFilterCompatibilityService, 
                        UserContextCleanupInterceptor userContextCleanupInterceptor) {
        this.loginFilterCompatibilityService = loginFilterCompatibilityService;
        this.userContextCleanupInterceptor = userContextCleanupInterceptor;
    }

    /**
     * 注册拦截器
     * 注册TraceId拦截器，确保每个请求都有traceId
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {

        CustomAuthInterceptor interceptor = new CustomAuthInterceptor(
                handle -> {
                    SaRouter
                            .match("/**")    // 拦截的 path 列表，可以写多个 */
                            .notMatch("/api/cx/customer/auth/**")        // 排除掉的 path 列表，可以写多个
                            .notMatch("/api/cx/customer/admin/qiyu/crm/**")        // 排除掉的 path 列表，可以写多个
                            .notMatch("/health-check.json")
                            .check(r -> StpUtil.checkLogin());        // 要执行的校验动作，可以写完整的 lambda 表达式

                }, loginFilterCompatibilityService);
        // 1. 注册TraceId拦截器 - 最高优先级，确保最先设置traceId
        registry.addInterceptor(new TraceIdInterceptor())
                .addPathPatterns("/**")
                .order(-2000);  // 设置最高优先级，确保最先执行

        registry.addInterceptor(interceptor).addPathPatterns("/**");

        // 3. 注册用户上下文清理拦截器 - 高优先级，确保在TraceId拦截器之后执行清理
        registry.addInterceptor(userContextCleanupInterceptor)
                .addPathPatterns("/**")
                .order(-1000);  // 设置高优先级，确保在TraceId拦截器之后执行清理

        log.info("TraceId拦截器已注册，优先级: -2000");
        log.info("Sa-Token拦截器已注册，白名单路径: {}", java.util.Arrays.toString(Constant.WHITE_LIST));
        log.info("用户上下文清理拦截器已注册，优先级: -1000");
    }


    static class CustomAuthInterceptor extends SaInterceptor {

        private final LoginFilterCompatibilityService loginFilterCompatibilityService;

        public CustomAuthInterceptor(SaParamFunction<Object> auth, LoginFilterCompatibilityService loginFilterCompatibilityService) {
            super(auth);
            this.loginFilterCompatibilityService = loginFilterCompatibilityService;
        }

        @Override
        public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
                throws Exception {

            try {
                // 1. 检查是否为排除URL
                if (loginFilterCompatibilityService.isExcludedUrl(request)) {
                    LogUtils.debug(log, "排除URL，跳过认证: {}", request.getRequestURI());
                    return true;
                }

                // 2. 尝试LoginFilter兼容认证
                if (loginFilterCompatibilityService.tryCompatibleAuthentication(request)) {
                    LogUtils.info(log, "LoginFilter兼容认证成功，跳过Sa-Token校验: {}", request.getRequestURI());
                    return true;
                }

                // 3. LoginFilter认证失败，继续Sa-Token校验
                LogUtils.debug(log, "LoginFilter认证失败，执行Sa-Token校验: {}", request.getRequestURI());
                return super.preHandle(request, response, handler);

            } catch (Exception e) {
                LogUtils.error(log, "认证处理异常", e);
                // 异常情况下继续Sa-Token校验
                return super.preHandle(request, response, handler);
            }
        }
    }

    /// 配置CORS跨域支持
    /// 统一的CORS配置，替代自定义CORS过滤器，避免配置冲突
    /// 重要提示：
    /// 1. 这是应用程序的唯一CORS配置点，其他地方不应再设置CORS响应头
    /// 2. 使用 allowedOriginPatterns("*") 而不是 allowedOrigins("*")，
    ///    因为当 allowCredentials=true 时，不能使用 allowedOrigins("*")
    /// 3. 如果在拦截器或过滤器中手动设置 Access-Control-Allow-Origin: *，
    ///    会与此配置冲突，导致 CORS 错误
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                .allowedOriginPatterns("*")  // 允许所有域名跨域访问（支持credentials）
                .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS")  // 明确指定允许的HTTP方法
                .allowCredentials(true)      // 允许携带认证信息（Cookie、Authorization header等）
                .maxAge(3600)               // 预检请求缓存时间（秒）
                .allowedHeaders("*")        // 允许所有请求头
                .exposedHeaders("*");       // 暴露所有响应头给客户端
    }


    @Override
    public void configureMessageConverters(List<HttpMessageConverter<?>> converters) {
        converters.add(jacksonHttpMessageConverter());
    }

    @Bean
    public MappingJackson2HttpMessageConverter jacksonHttpMessageConverter() {
        MappingJackson2HttpMessageConverter converter = new MappingJackson2HttpMessageConverter();

        ObjectMapper objectMapper = new ObjectMapper();

        // 注册 JavaTimeModule 以支持 Java 8 时间类型
        objectMapper.registerModule(new JavaTimeModule());

        // 禁用将日期写为时间戳
        objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);

        // 设置日期格式
        objectMapper.setDateFormat(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
        objectMapper.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));

        // 设置 ObjectMapper
        converter.setObjectMapper(objectMapper);

        // 设置字符编码
        converter.setDefaultCharset(StandardCharsets.UTF_8);

        // 设置支持的媒体类型
        List<MediaType> mediaTypeList = new ArrayList<>();
        mediaTypeList.add(MediaType.APPLICATION_JSON);
        converter.setSupportedMediaTypes(mediaTypeList);

        return converter;
    }

}
