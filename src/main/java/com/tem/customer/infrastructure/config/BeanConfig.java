package com.tem.customer.infrastructure.config;

import com.iplatform.common.SpringContextUtils;
import com.iplatform.common.idgen.ShortIdWorker;
import com.iplatform.common.idgen.strategy.RedissonWorkerStrategy;
import com.iplatform.common.router.aop.WebTrafficTagAspect;
import com.iplatform.common.trace.TraceEntryInterceptor;
import com.iplatform.common.web.interceptor.LogInterceptor;
import com.tem.platform.api.OrgUserService;
import com.tem.platform.api.PartnerService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.Primary;
import org.springframework.validation.beanvalidation.MethodValidationPostProcessor;

/**
 * Bean配置类
 * 用于注册各种Bean，包括Dubbo服务引用、拦截器等
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@Slf4j
@Configuration
@EnableAspectJAutoProxy
public class BeanConfig {


    private final RedissonClient redissonClient;

    @DubboReference(timeout = 10000, retries = 0, check = false)
    private PartnerService partnerService;

    @DubboReference(timeout = 10000, retries = 0, check = false)
    private OrgUserService orgUserService;

    public BeanConfig(RedissonClient redissonClient) {
        this.redissonClient = redissonClient;
    }

    @Bean("shortIdWorker")
    public ShortIdWorker shortIdWorker() {
        ShortIdWorker shortIdWorker = new ShortIdWorker();
        shortIdWorker.setWorkerStrategy(new RedissonWorkerStrategy(redissonClient));
        return shortIdWorker;
    }


    @Bean
    @Primary
    public PartnerService partnerServiceBean() {
        return partnerService;
    }

    @Bean
    @Primary
    public OrgUserService orgUserServiceBean() {
        return orgUserService;
    }


    @Bean
    public TraceEntryInterceptor getTraceEntryInterceptor() {
        return new TraceEntryInterceptor();
    }


    @Bean
    public SpringContextUtils springContextUtils() {
        return new SpringContextUtils();
    }



    @Bean
    public WebTrafficTagAspect webTrafficTagAspect() {
        return new WebTrafficTagAspect();
    }


    @Bean
    public MethodValidationPostProcessor methodValidationPostProcessor() {
        return new MethodValidationPostProcessor();
    }

    @Bean
    public LogInterceptor logInterceptor() {
        return new LogInterceptor();
    }
}
