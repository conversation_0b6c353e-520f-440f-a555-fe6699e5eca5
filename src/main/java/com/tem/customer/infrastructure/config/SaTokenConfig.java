package com.tem.customer.infrastructure.config;

import cn.dev33.satoken.dao.SaTokenDao;
import cn.dev33.satoken.dao.SaTokenDaoForRedisson;
import com.iplatform.common.utils.LogUtils;
import lombok.extern.slf4j.Slf4j;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.util.StringUtils;

/**
 * Sa-Token配置类
 * 解决Sa-Token与Redisson序列化冲突问题
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Configuration
public class SaTokenConfig {

    @Value("${spring.redis.sentinel.nodes}")
    private String address;

    @Value("${spring.redis.sentinel.mode}")
    private String type;

    @Value("${spring.redis.password}")
    private String password;

    @Value("${spring.redis.database:0}")
    private int database;

    @Value("${redis.session.masterName}")
    private String sentinelMasterName;

    /**
     * 为Sa-Token创建专用的RedissonClient
     * 使用String序列化，避免与业务Redis的Jackson序列化冲突
     */
    @Bean("saTokenRedissonClient")
    public RedissonClient saTokenRedissonClient() {
        Config config = new Config();
        
        // 使用默认的序列化方式（String），不使用Jackson
        // 这样Sa-Token可以正确处理String类型的数据
        
        try {
            RedisConnectionType redisConnectionType = RedisConnectionType.fromType(this.type);
            
            switch (redisConnectionType) {
                case SINGLE:
                    configureSingleServer(config);
                    break;
                case CLUSTER:
                    configureClusterServers(config);
                    break;
                case SENTINEL:
                    configureSentinelServers(config);
                    break;
                case MASTER_SLAVE:
                    configureMasterSlaveServers(config);
                    break;
                default:
                    throw new IllegalArgumentException("Unsupported Redis connection type: " + type);
            }
            
            RedissonClient redissonClient = Redisson.create(config);
            LogUtils.info(log, "Sa-Token专用RedissonClient初始化成功，连接类型: {}", redisConnectionType);
            return redissonClient;
            
        } catch (Exception e) {
            LogUtils.error(log, "Sa-Token RedissonClient初始化失败", e);
            throw new RuntimeException("Failed to initialize Sa-Token RedissonClient", e);
        }
    }

    /**
     * 配置Sa-Token专用的SaTokenDao
     * 使用专用的RedissonClient，避免序列化冲突
     */
    @Bean
    @Primary
    public SaTokenDao saTokenDao(@Qualifier("saTokenRedissonClient") RedissonClient saTokenRedissonClient) {
        SaTokenDaoForRedisson saTokenDao = new SaTokenDaoForRedisson(saTokenRedissonClient);
        LogUtils.info(log, "Sa-Token专用SaTokenDao配置完成");
        return saTokenDao;
    }

    /**
     * 配置单节点模式
     */
    private void configureSingleServer(Config config) {
        String serverAddress = addProtocolPrefix(address);
        config.useSingleServer()
                .setAddress(serverAddress)
                .setDatabase(database)
                .setPassword(StringUtils.hasText(password) ? password : null)
                .setConnectTimeout(10000)
                .setTimeout(3000)
                .setRetryAttempts(3)
                .setRetryInterval(1500)
                .setConnectionPoolSize(64)
                .setConnectionMinimumIdleSize(10);

        LogUtils.info(log, "Sa-Token配置单节点Redis: {}", serverAddress);
    }

    /**
     * 配置集群模式
     */
    private void configureClusterServers(Config config) {
        String[] nodeAddresses = parseAddresses(address);
        config.useClusterServers()
                .addNodeAddress(nodeAddresses)
                .setPassword(StringUtils.hasText(password) ? password : null)
                .setConnectTimeout(10000)
                .setTimeout(3000)
                .setRetryAttempts(3)
                .setRetryInterval(1500)
                .setMasterConnectionPoolSize(64)
                .setSlaveConnectionPoolSize(64);

        LogUtils.info(log, "Sa-Token配置集群Redis: {}", String.join(",", nodeAddresses));
    }

    /**
     * 配置哨兵模式
     */
    private void configureSentinelServers(Config config) {
        String masterName = StringUtils.hasText(sentinelMasterName) ? sentinelMasterName : "mymaster";
        String[] sentinelAddresses = parseAddresses(address);

        config.useSentinelServers()
                .setMasterName(masterName)
                .addSentinelAddress(sentinelAddresses)
                .setDatabase(database)
                .setPassword(StringUtils.hasText(password) ? password : null)
                .setConnectTimeout(10000)
                .setTimeout(3000)
                .setRetryAttempts(3)
                .setRetryInterval(1500)
                .setMasterConnectionPoolSize(64)
                .setSlaveConnectionPoolSize(64);

        LogUtils.info(log, "Sa-Token配置哨兵Redis: master={}, sentinels={}", 
                masterName, String.join(",", sentinelAddresses));
    }

    /**
     * 配置主从模式
     */
    private void configureMasterSlaveServers(Config config) {
        String[] addresses = parseAddresses(address);
        if (addresses.length < 2) {
            throw new IllegalArgumentException("Master-slave mode requires at least 2 addresses");
        }

        String masterAddress = addresses[0];
        String[] slaveAddresses = new String[addresses.length - 1];
        System.arraycopy(addresses, 1, slaveAddresses, 0, slaveAddresses.length);

        config.useMasterSlaveServers()
                .setMasterAddress(masterAddress)
                .addSlaveAddress(slaveAddresses)
                .setDatabase(database)
                .setPassword(StringUtils.hasText(password) ? password : null)
                .setConnectTimeout(10000)
                .setTimeout(3000)
                .setRetryAttempts(3)
                .setRetryInterval(1500)
                .setMasterConnectionPoolSize(64)
                .setSlaveConnectionPoolSize(64);

        LogUtils.info(log, "Sa-Token配置主从Redis: master={}, slaves={}", 
                masterAddress, String.join(",", slaveAddresses));
    }

    /**
     * 解析地址字符串
     */
    private String[] parseAddresses(String address) {
        String[] addresses = address.split(",");
        for (int i = 0; i < addresses.length; i++) {
            addresses[i] = addProtocolPrefix(addresses[i].trim());
        }
        return addresses;
    }

    /**
     * 添加协议前缀
     */
    private String addProtocolPrefix(String address) {
        if (!address.startsWith("redis://") && !address.startsWith("rediss://")) {
            return "redis://" + address;
        }
        return address;
    }

    /**
     * Redis连接类型枚举
     */
    private enum RedisConnectionType {
        /**
         * 单节点、集群、哨兵、主从
         */
        SINGLE("S"),
        CLUSTER("C"),
        SENTINEL("SC"),
        MASTER_SLAVE("masterslave");

        private final String type;

        RedisConnectionType(String type) {
            this.type = type;
        }

        public static RedisConnectionType fromType(String type) {
            for (RedisConnectionType connectionType : values()) {
                if (connectionType.type.equalsIgnoreCase(type)) {
                    return connectionType;
                }
            }
            throw new IllegalArgumentException("Unknown Redis connection type: " + type);
        }
    }
}
