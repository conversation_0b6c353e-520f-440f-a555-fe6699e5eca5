package com.tem.customer.infrastructure.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;

import java.util.List;

/**
 * 七鱼CRM配置属性
 *
 * <AUTHOR>
 * @since 2025-07-17
 */
@Data
@Component
@ConfigurationProperties(prefix = "qiyu.crm")
public class QiyuCrmProperties {

    private static final AntPathMatcher PATH_MATCHER = new AntPathMatcher();

    /**
     * 应用配置
     */
    private App app = new App();

    /**
     * Token配置
     */
    private Token token = new Token();

    /**
     * 认证配置
     */
    private Auth auth = new Auth();

    /**
     * 应用配置
     */
    @Data
    public static class App {
        /**
         * 应用ID
         */
        private String key;

        /**
         * 应用密钥
         */
        private String secret;
    }

    /**
     * Token配置
     */
    @Data
    public static class Token {
        /**
         * Token有效期（分钟）
         */
        private int timeout = 120;
    }

    /**
     * 认证配置
     */
    @Data
    public static class Auth {
        /**
         * 时间容忍度（秒）
         */
        private long timeTolerance = 300;

        /**
         * 是否启用认证
         */
        private boolean enabled = true;

        /**
         * 需要认证的路径模式
         */
        private List<String> includePatterns = List.of(
            "/api/cx/customer/admin/qiyu/crm/**"
        );

        /**
         * 排除认证的路径模式
         */
        private List<String> excludePatterns = List.of(
            "/api/cx/customer/admin/qiyu/crm/get_token"
        );

        /**
         * 七鱼CRM接口路径模式（用于精确匹配）
         */
        private List<String> qiyuApiPatterns = List.of(
            "/api/cx/customer/admin/qiyu/crm/wechat/get_user_info",
            "/api/cx/customer/admin/qiyu/crm/get_group_info",
            "/api/cx/customer/admin/qiyu/crm/get_user_info"
        );
    }

    /**
     * 判断是否为七鱼CRM请求路径
     * 统一的路径匹配逻辑，供拦截器使用
     *
     * @param requestUri 请求URI
     * @return 是否为七鱼CRM请求
     */
    public boolean isQiyuCrmPath(String requestUri) {
        if (requestUri == null) {
            return false;
        }

        // 检查是否匹配七鱼CRM API路径模式
        return auth.qiyuApiPatterns.stream()
                .anyMatch(pattern -> PATH_MATCHER.match(pattern, requestUri) || requestUri.endsWith(getEndpoint(pattern)));
    }

    /**
     * 从完整路径中提取端点名称
     */
    private String getEndpoint(String fullPath) {
        int lastSlashIndex = fullPath.lastIndexOf('/');
        return lastSlashIndex >= 0 ? fullPath.substring(lastSlashIndex) : fullPath;
    }
}
