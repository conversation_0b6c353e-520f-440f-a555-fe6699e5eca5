package com.tem.customer.infrastructure.config;

import com.alibaba.druid.pool.DruidDataSource;
import com.iplatform.common.datasource.DbPasswordCallback;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import javax.sql.DataSource;
import java.sql.SQLException;

/**
 * 数据源配置类
 * 支持通过connectionProperties进行密码解密
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Slf4j
@Configuration
public class DataSourceConfig {

    @Value("${spring.datasource.driver-class-name}")
    private String driverClassName;

    @Value("${spring.datasource.url}")
    private String url;

    @Value("${spring.datasource.username}")
    private String username;

    @Value("${jdbc.publickey:}")
    private String publicKey;

    @Value("${jdbc.encrypt.password:}")
    private String encryptPassword;

    // Druid连接池配置
    @Value("${spring.datasource.druid.initial-size:10}")
    private int initialSize;

    @Value("${spring.datasource.druid.min-idle:10}")
    private int minIdle;

    @Value("${spring.datasource.druid.max-active:100}")
    private int maxActive;

    @Value("${spring.datasource.druid.max-wait:60000}")
    private long maxWait;

    @Value("${spring.datasource.druid.time-between-eviction-runs-millis:60000}")
    private long timeBetweenEvictionRunsMillis;

    @Value("${spring.datasource.druid.min-evictable-idle-time-millis:300000}")
    private long minEvictableIdleTimeMillis;

    @Value("${spring.datasource.druid.validation-query:SELECT 1 FROM DUAL}")
    private String validationQuery;

    @Value("${spring.datasource.druid.test-while-idle:true}")
    private boolean testWhileIdle;

    @Value("${spring.datasource.druid.test-on-borrow:false}")
    private boolean testOnBorrow;

    @Value("${spring.datasource.druid.test-on-return:false}")
    private boolean testOnReturn;

    @Value("${spring.datasource.druid.pool-prepared-statements:true}")
    private boolean poolPreparedStatements;

    @Value("${spring.datasource.druid.max-pool-prepared-statement-per-connection-size:20}")
    private int maxPoolPreparedStatementPerConnectionSize;

    /**
     * 创建数据库密码解密回调
     */
    @Bean
    public DbPasswordCallback dbPasswordCallback() {
        return new DbPasswordCallback();
    }

    /**
     * 创建Druid数据源
     * 使用connectionProperties和passwordCallback进行密码解密
     */
    @Bean
    @Primary
    public DataSource dataSource(DbPasswordCallback dbPasswordCallback) throws SQLException {
        DruidDataSource dataSource = new DruidDataSource();
        
        // 基本连接信息
        dataSource.setDriverClassName(driverClassName);
        dataSource.setUrl(url);
        dataSource.setUsername(username);
        
        // 通过connectionProperties和passwordCallback设置解密参数
        if (publicKey != null && !publicKey.trim().isEmpty() &&
            encryptPassword != null && !encryptPassword.trim().isEmpty()) {

            // 设置connectionProperties进行密码解密
            String connectionProperties = String.format("publickey=%s;password=%s", publicKey, encryptPassword);
            dataSource.setConnectionProperties(connectionProperties);

            // 设置密码解密回调
            dataSource.setPasswordCallback(dbPasswordCallback);

            // 启用ConfigFilter进行密码解密
            dataSource.setFilters("config");

            log.info("数据源配置完成 - 使用加密密码，通过connectionProperties和passwordCallback解密");
        }
        
        // 连接池配置
        dataSource.setInitialSize(initialSize);
        dataSource.setMinIdle(minIdle);
        dataSource.setMaxActive(maxActive);
        dataSource.setMaxWait(maxWait);
        dataSource.setTimeBetweenEvictionRunsMillis(timeBetweenEvictionRunsMillis);
        dataSource.setMinEvictableIdleTimeMillis(minEvictableIdleTimeMillis);
        dataSource.setValidationQuery(validationQuery);
        dataSource.setTestWhileIdle(testWhileIdle);
        dataSource.setTestOnBorrow(testOnBorrow);
        dataSource.setTestOnReturn(testOnReturn);
        dataSource.setPoolPreparedStatements(poolPreparedStatements);
        dataSource.setMaxPoolPreparedStatementPerConnectionSize(maxPoolPreparedStatementPerConnectionSize);
        
        // 初始化数据源
        dataSource.init();
        
        log.info("Druid数据源初始化完成 - URL: {}, 用户名: {}, 初始连接数: {}, 最大连接数: {}", 
                url, username, initialSize, maxActive);
        
        return dataSource;
    }
}
