package com.tem.customer.infrastructure.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 企业微信API配置属性
 *
 * <AUTHOR>
 * @since 2025-07-31
 */
@Data
@Component
@ConfigurationProperties(prefix = "wechat.api")
public class WechatApiProperties {

    /**
     * 企业微信API基础URL
     */
    private String baseUrl = "https://qyapi.weixin.qq.com";

    /**
     * 企业ID
     */
    private String corpId;

    /**
     * 应用密钥
     */
    private String corpSecret;

    /**
     * 应用ID
     */
    private String agentId;

    /**
     * HTTP连接超时时间（毫秒）
     */
    private int connectTimeout = 10000;

    /**
     * HTTP读取超时时间（毫秒）
     */
    private int readTimeout = 30000;

    /**
     * 重试次数
     */
    private int retryCount = 3;

    /**
     * Access Token缓存时间（秒）
     */
    private int tokenCacheTime = 7000;

    /**
     * 是否启用企业微信API
     */
    private boolean enabled = true;
}
