package com.tem.customer.infrastructure.config;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.jsontype.impl.LaissezFaireSubTypeValidator;
import com.tem.customer.shared.enums.RedisConnectionType;
import lombok.extern.slf4j.Slf4j;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.api.RedissonReactiveClient;
import org.redisson.codec.JsonJacksonCodec;
import org.redisson.config.Config;
import org.redisson.config.ReadMode;
import org.redisson.config.SubscriptionMode;
import org.redisson.spring.data.connection.RedissonConnectionFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.util.StringUtils;

import java.util.Arrays;

/**
 * Redisson配置类
 * 支持单节点、集群、哨兵、主从等多种Redis部署模式
 *
 * <AUTHOR>
 * @since 2025-05-29
 */
@Slf4j
@Configuration
public class RedissonConfig {

    private static final String REDIS_PROTOCOL_PREFIX = "redis://";
    private static final String REDISS_PROTOCOL_PREFIX = "rediss://";

    /**
     * redis主机地址，ip：port，有多个用半角逗号分隔
     */
    @Value("${spring.redis.sentinel.nodes}")
    private String address;

    /**
     * 连接类型，S-单机节点，SC-哨兵，C-集群，masterslave-主从
     */
    @Value("${spring.redis.sentinel.mode}")
    private String type;

    /**
     * redis 连接密码
     */
    @Value("${spring.redis.password}")
    private String password;

    /**
     * 选取那个数据库
     */
    @Value("${spring.redis.database:0}")
    private int database;

    @Value("${redis.itop.masterName}")
    private String sentinelMasterName;

    @Bean
    @Primary
    public RedissonClient redissonClient() {
        return Redisson.create(this.createConfigBasedOnType());
    }

    @Bean("businessRedisConnectionFactory")
    public RedissonConnectionFactory redissonConnectionFactory(RedissonClient redissonClient) {
        return new RedissonConnectionFactory(redissonClient);
    }


    @Bean
    public RedissonReactiveClient redissonReactiveClient(RedissonClient redissonClient) {
        return redissonClient.reactive();
    }

    @Bean
    public RedisTemplate<String, Object> redisTemplate(@Qualifier("businessRedisConnectionFactory") RedissonConnectionFactory redisConnectionFactory) {
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(redisConnectionFactory);

        // 配置序列化器
        StringRedisSerializer stringRedisSerializer = new StringRedisSerializer();
        Jackson2JsonRedisSerializer<Object> jackson2JsonRedisSerializer = createJacksonSerializer();

        // key采用String的序列化方式
        template.setKeySerializer(stringRedisSerializer);
        // hash的key也采用String的序列化方式
        template.setHashKeySerializer(stringRedisSerializer);
        // value序列化方式采用jackson
        template.setValueSerializer(jackson2JsonRedisSerializer);
        // hash的value序列化方式采用jackson
        template.setHashValueSerializer(jackson2JsonRedisSerializer);

        template.afterPropertiesSet();
        return template;
    }

    @Bean
    @ConditionalOnMissingBean(StringRedisTemplate.class)
    public StringRedisTemplate stringRedisTemplate(@Qualifier("businessRedisConnectionFactory") RedissonConnectionFactory redisConnectionFactory) {
        StringRedisTemplate template = new StringRedisTemplate();
        template.setConnectionFactory(redisConnectionFactory);
        return template;
    }


    /**
     * 创建Jackson序列化器
     */
    private Jackson2JsonRedisSerializer<Object> createJacksonSerializer() {
        ObjectMapper om = new ObjectMapper();
        om.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
        om.activateDefaultTyping(LaissezFaireSubTypeValidator.instance, ObjectMapper.DefaultTyping.NON_FINAL);

        return new Jackson2JsonRedisSerializer<>(om, Object.class);
    }

    /**
     * 根据配置类型创建Redisson配置
     */
    private Config createConfigBasedOnType() {
        if (!StringUtils.hasText(address)) {
            throw new IllegalArgumentException("Redis address cannot be empty");
        }
        if (!StringUtils.hasText(type)) {
            throw new IllegalArgumentException("Redis connection type cannot be empty");
        }

        Config config = new Config();

        // 配置Jackson编解码器，避免Kryo依赖问题
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
        objectMapper.activateDefaultTyping(LaissezFaireSubTypeValidator.instance, ObjectMapper.DefaultTyping.NON_FINAL);
        config.setCodec(new JsonJacksonCodec(objectMapper));

        RedisConnectionType redisConnectionType = RedisConnectionType.fromType(this.type);

        log.info("Initializing Redisson with connection type: {}, address: {}, codec: Jackson", redisConnectionType, address);

        switch (redisConnectionType) {
            case STANDALONE:
                configureSingleServer(config);
                break;

            case CLUSTER:
                configureClusterServers(config);
                break;

            case SENTINEL:
                configureSentinelServers(config);
                break;

            case MASTERSLAVE:
                configureMasterSlaveServers(config);
                break;

            default:
                throw new IllegalArgumentException("Unknown Redisson connection type: " + redisConnectionType);
        }

        return config;
    }

    /**
     * 配置单节点模式
     */
    private void configureSingleServer(Config config) {
        String serverAddress = addProtocolPrefix(address);
        config.useSingleServer()
                .setAddress(serverAddress)
                .setDatabase(database)
                .setPassword(StringUtils.hasText(password) ? password : null)
                .setConnectTimeout(10000)
                .setTimeout(3000)
                .setRetryAttempts(3)
                .setRetryInterval(1500)
                .setConnectionPoolSize(64)
                .setConnectionMinimumIdleSize(10);

        log.info("Configured Redisson single server: {}", serverAddress);
    }

    /**
     * 配置集群模式
     */
    private void configureClusterServers(Config config) {
        String[] nodeAddresses = parseAddresses(address);
        config.useClusterServers()
                .addNodeAddress(nodeAddresses)
                .setPassword(StringUtils.hasText(password) ? password : null)
                .setConnectTimeout(10000)
                .setTimeout(3000)
                .setRetryAttempts(3)
                .setRetryInterval(1500)
                .setReadMode(ReadMode.SLAVE)
                .setSubscriptionMode(SubscriptionMode.SLAVE)
                .setMasterConnectionPoolSize(64)
                .setSlaveConnectionPoolSize(64);

        log.info("Configured Redisson cluster servers: {}", Arrays.toString(nodeAddresses));
    }

    /**
     * 配置哨兵模式
     */
    private void configureSentinelServers(Config config) {
        String masterName = StringUtils.hasText(sentinelMasterName) ? sentinelMasterName : "mymaster";
        String[] sentinelAddresses = parseAddresses(address);

        config.useSentinelServers()
                .setMasterName(masterName)
                .addSentinelAddress(sentinelAddresses)
                .setDatabase(database)
                .setPassword(StringUtils.hasText(password) ? password : null)
                .setConnectTimeout(10000)
                .setTimeout(3000)
                .setRetryAttempts(3)
                .setRetryInterval(1500)
                .setMasterConnectionPoolSize(64)
                .setSlaveConnectionPoolSize(64);

        log.info("Configured Redisson sentinel servers with master: {}, sentinels: {}",
                masterName, Arrays.toString(sentinelAddresses));
    }

    /**
     * 配置主从模式
     */
    private void configureMasterSlaveServers(Config config) {
        String[] addresses = parseAddresses(address);
        if (addresses.length < 2) {
            throw new IllegalArgumentException("Master-slave mode requires at least 2 addresses (master and slave)");
        }

        // 第一个地址作为主节点，其余作为从节点
        String masterAddress = addresses[0];
        String[] slaveAddresses = Arrays.copyOfRange(addresses, 1, addresses.length);

        config.useMasterSlaveServers()
                .setMasterAddress(masterAddress)
                .addSlaveAddress(slaveAddresses)
                .setDatabase(database)
                .setPassword(StringUtils.hasText(password) ? password : null)
                .setConnectTimeout(10000)
                .setTimeout(3000)
                .setRetryAttempts(3)
                .setRetryInterval(1500)
                .setReadMode(ReadMode.SLAVE)
                .setSubscriptionMode(SubscriptionMode.SLAVE)
                .setMasterConnectionPoolSize(64)
                .setSlaveConnectionPoolSize(64);

        log.info("Configured Redisson master-slave servers: master={}, slaves={}",
                masterAddress, Arrays.toString(slaveAddresses));
    }

    /**
     * 解析地址字符串，支持逗号分隔的多个地址
     */
    private String[] parseAddresses(String addressStr) {
        if (!StringUtils.hasText(addressStr)) {
            throw new IllegalArgumentException("Address cannot be empty");
        }

        return Arrays.stream(addressStr.split(","))
                .map(String::trim)
                .filter(StringUtils::hasText)
                .map(this::addProtocolPrefix)
                .toArray(String[]::new);
    }

    /**
     * 为节点地址添加协议前缀
     */
    private String addProtocolPrefix(String node) {
        if (!node.startsWith(REDIS_PROTOCOL_PREFIX) && !node.startsWith(REDISS_PROTOCOL_PREFIX)) {
            return REDIS_PROTOCOL_PREFIX + node;
        } else {
            return node;
        }
    }
}
