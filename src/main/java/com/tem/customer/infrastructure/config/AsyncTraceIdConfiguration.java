package com.tem.customer.infrastructure.config;

import com.tem.customer.shared.utils.TraceIdContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.interceptor.AsyncUncaughtExceptionHandler;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.TaskDecorator;
import org.springframework.scheduling.annotation.AsyncConfigurer;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;

/**
 * 异步任务配置类
 * 支持@Async注解的TraceId自动传递
 * <p>
 * 主要功能：
 * 1. 配置异步任务执行器
 * 2. 自动传递TraceId到异步任务
 * 3. 异步任务异常处理
 * 4. 任务装饰器配置
 *
 * <AUTHOR>
 * @since 2025-05-29
 */
@Slf4j
@Configuration
@EnableAsync
public class AsyncTraceIdConfiguration implements AsyncConfigurer {

    /**
     * 配置异步任务执行器
     */
    @Override
    @Bean(name = "asyncExecutor")
    public Executor getAsyncExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();

        // 基本配置
        executor.setCorePoolSize(8);
        executor.setMaxPoolSize(20);
        executor.setQueueCapacity(200);
        executor.setKeepAliveSeconds(60);
        executor.setThreadNamePrefix("async-trace-");

        // 设置任务装饰器，自动传递TraceId
        executor.setTaskDecorator(new TraceIdTaskDecorator());

        // 拒绝策略：调用者运行
        executor.setRejectedExecutionHandler(new java.util.concurrent.ThreadPoolExecutor.CallerRunsPolicy());

        // 等待所有任务完成后再关闭线程池
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(60);

        executor.initialize();

        log.info("异步任务执行器配置完成: corePoolSize={}, maxPoolSize={}, queueCapacity={}",
                executor.getCorePoolSize(), executor.getMaxPoolSize(), executor.getQueueCapacity());

        return executor;
    }

    /**
     * 异步任务异常处理器
     */
    @Override
    public AsyncUncaughtExceptionHandler getAsyncUncaughtExceptionHandler() {
        return new TraceIdAsyncUncaughtExceptionHandler();
    }

    /**
     * TraceId任务装饰器
     * 自动为异步任务传递TraceId
     */
    public static class TraceIdTaskDecorator implements TaskDecorator {

        @Override
        public Runnable decorate(Runnable runnable) {
            // 使用TraceIdContext包装任务，自动传递TraceId
            return TraceIdContext.wrapWithTraceId(runnable);
        }
    }

    /**
     * 带TraceId的异步异常处理器
     */
    public static class TraceIdAsyncUncaughtExceptionHandler implements AsyncUncaughtExceptionHandler {

        @Override
        public void handleUncaughtException(Throwable ex, java.lang.reflect.Method method, Object... params) {
            String traceId = TraceIdContext.getCurrentTraceId();

            log.error("异步任务执行异常 - TraceId: {}, Method: {}.{}, Params: {}",
                    traceId,
                    method.getDeclaringClass().getSimpleName(),
                    method.getName(),
                    params,
                    ex);
        }
    }

    /**
     * 定时任务执行器（支持TraceId）
     */
    @Bean(name = "scheduledExecutor")
    public Executor getScheduledExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();

        // 基本配置
        executor.setCorePoolSize(5);
        executor.setMaxPoolSize(10);
        executor.setQueueCapacity(100);
        executor.setKeepAliveSeconds(60);
        executor.setThreadNamePrefix("scheduled-trace-");

        // 设置任务装饰器，为定时任务生成TraceId
        executor.setTaskDecorator(new ScheduledTraceIdTaskDecorator());

        // 拒绝策略：丢弃任务
        executor.setRejectedExecutionHandler(new java.util.concurrent.ThreadPoolExecutor.DiscardPolicy());

        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(30);

        executor.initialize();

        log.info("定时任务执行器配置完成: corePoolSize={}, maxPoolSize={}, queueCapacity={}",
                executor.getCorePoolSize(), executor.getMaxPoolSize(), executor.getQueueCapacity());

        return executor;
    }

    /**
     * 定时任务TraceId装饰器
     * 为定时任务生成带时间戳的TraceId
     */
    public static class ScheduledTraceIdTaskDecorator implements TaskDecorator {

        @Override
        public Runnable decorate(Runnable runnable) {
            return () -> {
                // 为定时任务生成带时间戳的TraceId（不含特殊字符）
                String traceId = TraceIdContext.generateAndSetCleanTimestampTraceId();

                try {
                    if (log.isDebugEnabled()) {
                        log.debug("定时任务开始执行，TraceId: {}", traceId);
                    }

                    runnable.run();

                    if (log.isDebugEnabled()) {
                        log.debug("定时任务执行完成，TraceId: {}", traceId);
                    }
                } catch (Exception e) {
                    log.error("定时任务执行失败，TraceId: {}", traceId, e);
                    throw e;
                } finally {
                    // 清理TraceId
                    TraceIdContext.clearTraceId();
                }
            };
        }
    }
}
