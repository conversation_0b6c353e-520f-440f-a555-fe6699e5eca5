package com.tem.customer.infrastructure.config;

import com.alibaba.ttl.threadpool.TtlExecutors;
import com.tem.customer.shared.utils.ThreadPoolUtil;
import jakarta.annotation.PreDestroy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.RejectedExecutionHandler;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 线程池配置类
 * 提供统一的线程池Bean
 *
 * <AUTHOR>
 * @since 2025-05-29
 */
@Slf4j
@Configuration
@RequiredArgsConstructor
@EnableConfigurationProperties(ThreadPoolProperties.class)
public class ThreadPoolConfig {

    private final ThreadPoolProperties threadPoolProperties;

    /**
     * 通用线程池
     */
    @Bean
    @Primary
    public ExecutorService threadPoolExecutor() {
        ThreadPoolProperties.PoolConfig config = threadPoolProperties.getDefaultPoolConfig();

        ThreadPoolExecutor executor = new ThreadPoolExecutor(
                config.getCoreSize(),
                config.getMaxSize(),
                config.getKeepAliveTime(),
                config.getTimeUnit(),
                new LinkedBlockingQueue<>(config.getQueueCapacity()),
                new ThreadPoolUtil.BenefitsThreadFactory(
                        config.getThreadNamePrefix(),
                        config.getThreadPriority(),
                        config.isDaemon()
                ),
                createRejectedExecutionHandler(config.getRejectedExecutionHandlerType())
        );

        executor.allowCoreThreadTimeOut(config.isAllowCoreThreadTimeOut());

        if (config.isPrestartAllCoreThreads()) {
            executor.prestartAllCoreThreads();
        }

        log.info("创建线程池: coreSize={}, maxSize={}, queueCapacity={}, threadNamePrefix={}",
                config.getCoreSize(), config.getMaxSize(),
                config.getQueueCapacity(), config.getThreadNamePrefix());

        return config.isTtlEnabled() ?
                TtlExecutors.getTtlExecutorService(executor) : executor;
    }

    /**
     * 创建拒绝策略处理器
     */
    private RejectedExecutionHandler createRejectedExecutionHandler(
            ThreadPoolProperties.RejectedExecutionHandlerType type) {
        return switch (type) {
            case CALLER_RUNS_POLICY -> new ThreadPoolExecutor.CallerRunsPolicy();
            case DISCARD_POLICY -> new ThreadPoolExecutor.DiscardPolicy();
            case DISCARD_OLDEST_POLICY -> new ThreadPoolExecutor.DiscardOldestPolicy();
            default -> new ThreadPoolExecutor.AbortPolicy();
        };
    }

    /**
     * 应用关闭时优雅关闭线程池
     */
    @PreDestroy
    public void destroy() {
        log.info("开始关闭线程池...");
        // 线程池的关闭由Spring容器自动管理
        log.info("线程池关闭完成");
    }
}
