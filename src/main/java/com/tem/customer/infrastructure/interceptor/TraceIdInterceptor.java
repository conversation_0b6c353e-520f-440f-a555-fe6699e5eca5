package com.tem.customer.infrastructure.interceptor;

import com.tem.customer.shared.common.Constant;
import com.tem.customer.shared.utils.ShortUUID;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

/**
 * TraceId拦截器
 * 用于为每个HTTP请求生成唯一的追踪标识符，支持分布式链路追踪
 * <p>
 * 功能特性：
 * 1. 自动生成TraceId或从请求头中获取
 * 2. 将TraceId设置到MDC中，便于日志记录
 * 3. 将TraceId添加到响应头中，便于客户端获取
 * 4. 请求完成后自动清理MDC，避免内存泄漏
 *
 * <AUTHOR>
 * @since 2025-05-29
 */
@Slf4j
@Component
public class TraceIdInterceptor implements HandlerInterceptor {

    /**
     * 请求前处理
     * 1. 优先从请求头获取TraceId（支持分布式追踪）
     * 2. 如果没有则生成新的TraceId
     * 3. 设置到MDC和响应头中
     */
    @Override
    public boolean preHandle(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response, @NonNull Object handler) {
        try {
            String traceId = getOrGenerateTraceId(request);

            // 设置到MDC中，供日志使用
            MDC.put(Constant.TRACE_ID, traceId);

            // 设置到响应头中，供客户端获取
            response.setHeader(Constant.TRACE_ID, traceId);

            if (log.isDebugEnabled()) {
                log.debug("TraceId已设置: {} for request: {} {}",
                        traceId, request.getMethod(), request.getRequestURI());
            }

            return true;
        } catch (Exception e) {
            log.error("TraceId拦截器处理失败", e);
            // 即使失败也要继续处理请求
            return true;
        }
    }

    /**
     * 请求完成后处理
     * 1. 确保响应头中包含TraceId（兜底处理）
     * 2. 清理MDC中的TraceId，避免内存泄漏和线程污染
     */
    @Override
    public void afterCompletion(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response,
                                @NonNull Object handler, Exception ex) {
        try {
            String traceId = MDC.get(Constant.TRACE_ID);

            // 如果有异常且traceId存在，记录异常信息
            if (ex != null && StringUtils.isNotBlank(traceId)) {
                log.warn("请求处理异常 - TraceId: {}, URI: {}, Exception: {}",
                        traceId, request.getRequestURI(), ex.getClass().getSimpleName());
            }

            // 兜底处理：确保响应头中包含TraceId
            if (StringUtils.isNotBlank(traceId)) {
                String existingTraceId = response.getHeader(Constant.TRACE_ID);
                if (StringUtils.isBlank(existingTraceId)) {
                    response.setHeader(Constant.TRACE_ID, traceId);
                    if (log.isDebugEnabled()) {
                        log.debug("兜底设置TraceId响应头: {} for request: {} {}",
                                traceId, request.getMethod(), request.getRequestURI());
                    }
                }

                if (log.isDebugEnabled()) {
                    log.debug("清理TraceId: {} for request: {} {}",
                            traceId, request.getMethod(), request.getRequestURI());
                }
            }
        } catch (Exception e) {
            log.warn("TraceId清理时记录日志失败", e);
        } finally {
            // 确保MDC被清理
            MDC.remove(Constant.TRACE_ID);
        }
    }

    /**
     * 获取或生成TraceId
     * 优先级：请求头 > MDC > 新生成
     */
    private String getOrGenerateTraceId(HttpServletRequest request) {
        // 1. 优先从请求头获取（支持分布式追踪）
        String traceId = request.getHeader(Constant.TRACE_ID);
        if (StringUtils.isNotBlank(traceId)) {
            traceId = traceId.trim();
            // 验证TraceId格式
            if (ShortUUID.isValidTraceId(traceId)) {
                return traceId;
            } else {
                log.warn("请求头中的TraceId格式无效: {}, 将生成新的TraceId", traceId);
            }
        }

        // 2. 从MDC获取（可能是其他组件设置的）
        traceId = MDC.get(Constant.TRACE_ID);
        if (StringUtils.isNotBlank(traceId)) {
            return traceId;
        }

        // 3. 生成新的TraceId（不含特殊字符）
        return ShortUUID.getUuidWithoutSpecialChars();
    }
}
