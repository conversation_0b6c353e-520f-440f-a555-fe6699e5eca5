package com.tem.customer.infrastructure.interceptor;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.iplatform.common.utils.LogUtils;
import com.tem.customer.shared.annotation.QiyuCrmAuth;
import com.tem.customer.infrastructure.config.QiyuCrmProperties;
import com.tem.customer.model.dto.qiyu.QiyuCrmRequest;
import com.tem.customer.model.dto.qiyu.QiyuCrmResponse;
import com.tem.customer.service.qiyu.QiyuCrmAuthService;
import com.tem.customer.infrastructure.web.CachedBodyHttpServletRequest;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * 七鱼CRM认证拦截器
 * 
 * <AUTHOR>
 * @since 2025-07-17
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class QiyuCrmAuthInterceptor implements HandlerInterceptor {

    private final QiyuCrmAuthService qiyuCrmAuthService;
    private final ObjectMapper objectMapper;
    private final QiyuCrmProperties qiyuCrmProperties;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        try {
            LogUtils.info(log, "七鱼CRM认证拦截器开始处理请求: {}", request.getRequestURI());



            // 处理OPTIONS预检请求
            if (HttpMethod.OPTIONS.matches(request.getMethod())) {
                LogUtils.info(log, "处理OPTIONS预检请求");
                response.setStatus(HttpServletResponse.SC_OK);
                return false; // 不继续处理
            }

            // 只对POST请求进行认证
            if (!HttpMethod.POST.matches(request.getMethod())) {
                LogUtils.warn(log, "非POST请求，跳过认证: {}", request.getMethod());
                return true;
            }

            // 检查是否需要七鱼CRM认证
            if (!needsQiyuCrmAuth(handler, request)) {
                LogUtils.info(log, "无需七鱼CRM认证，跳过认证: {}", request.getRequestURI());
                return true;
            }

            // 获取认证参数（新版认证方式）
            Long time = getTimeParameter(request);
            String checksum = getChecksumParameter(request);
            LogUtils.info(log, "获取到的认证参数: time={}, checksum={}", time, checksum);

            // 确保请求体可以重复读取，如果需要的话创建包装器
            HttpServletRequest wrappedRequest = request;
            if (!(request instanceof CachedBodyHttpServletRequest)) {
                wrappedRequest = createCachedBodyRequest(request);
                LogUtils.debug(log, "创建CachedBodyHttpServletRequest包装器");
            }

            // 读取请求体
            String requestBody = getRequestBody(wrappedRequest);
            LogUtils.info(log, "请求体内容: {}", requestBody);

            // 解析请求体中的认证参数（老版认证方式）
            QiyuCrmRequest crmRequest = parseRequestBody(requestBody);

            // 进行认证验证
            boolean isAuthenticated = qiyuCrmAuthService.validateAuthentication(crmRequest, requestBody, time, checksum);

            if (!isAuthenticated) {
                LogUtils.warn(log, "七鱼CRM认证失败");
                writeAuthFailureResponse(response);
                return false;
            }

            LogUtils.info(log, "七鱼CRM认证成功");
            return true;

        } catch (Exception e) {
            LogUtils.error(log, "七鱼CRM认证拦截器异常", e);
            writeSystemErrorResponse(response);
            return false;
        }
    }

    /**
     * 判断是否需要七鱼CRM认证
     * 优先通过注解判断，其次通过配置的路径匹配判断
     */
    private boolean needsQiyuCrmAuth(Object handler, HttpServletRequest request) {
        // 1. 通过注解判断（最准确的方式）
        if (handler instanceof HandlerMethod handlerMethod) {

            // 检查方法上的注解
            QiyuCrmAuth methodAuth = handlerMethod.getMethodAnnotation(QiyuCrmAuth.class);
            if (methodAuth != null) {
                LogUtils.info(log, "通过方法注解识别需要七鱼CRM认证: {}", methodAuth.description());
                return methodAuth.required();
            }

            // 检查类上的注解
            QiyuCrmAuth classAuth = handlerMethod.getBeanType().getAnnotation(QiyuCrmAuth.class);
            if (classAuth != null) {
                LogUtils.info(log, "通过类注解识别需要七鱼CRM认证: {}", classAuth.description());
                return classAuth.required();
            }
        }

        // 2. 通过配置的路径匹配判断（兜底方式）
        String requestUri = request.getRequestURI();
        boolean isQiyuPath = qiyuCrmProperties.isQiyuCrmPath(requestUri);
        if (isQiyuPath) {
            LogUtils.info(log, "通过路径匹配识别为七鱼CRM请求: {}", requestUri);
        }
        return isQiyuPath;
    }





    /**
     * 获取时间参数
     * 优先从header获取，如果没有则从URL参数获取
     */
    private Long getTimeParameter(HttpServletRequest request) {
        // 首先尝试从header获取
        String timeStr = request.getHeader("time");
        if (StringUtils.hasText(timeStr)) {
            try {
                return Long.parseLong(timeStr);
            } catch (NumberFormatException e) {
                LogUtils.warn(log, "header中时间参数格式错误: {}", timeStr);
            }
        }

        // 如果header中没有，再从URL参数获取
        timeStr = request.getParameter("time");
        if (StringUtils.hasText(timeStr)) {
            try {
                return Long.parseLong(timeStr);
            } catch (NumberFormatException e) {
                LogUtils.warn(log, "URL参数中时间参数格式错误: {}", timeStr);
            }
        }

        return null;
    }

    /**
     * 获取校验和参数
     * 优先从header获取，如果没有则从URL参数获取
     */
    private String getChecksumParameter(HttpServletRequest request) {
        // 首先尝试从header获取
        String checksum = request.getHeader("checksum");
        if (StringUtils.hasText(checksum)) {
            return checksum;
        }

        // 如果header中没有，再从URL参数获取
        checksum = request.getParameter("checksum");
        if (StringUtils.hasText(checksum)) {
            return checksum;
        }

        return null;
    }

    /**
     * 读取请求体内容
     * 如果需要重复读取，会自动创建CachedBodyHttpServletRequest包装器
     */
    private String getRequestBody(HttpServletRequest request) throws IOException {
        // 如果已经是包装的请求，直接获取缓存的请求体
        if (request instanceof CachedBodyHttpServletRequest cachedRequest) {
            return cachedRequest.getCachedBody();
        }

        // 对于普通请求，直接读取请求体
        // 注意：这种方式只能读取一次，如果需要重复读取需要使用CachedBodyHttpServletRequest
        var requestBody = new StringBuilder();
        try (var reader = request.getReader()) {
            String line;
            while ((line = reader.readLine()) != null) {
                requestBody.append(line);
            }
        }
        return requestBody.toString();
    }

    /**
     * 创建可缓存请求体的HttpServletRequest包装器
     * 在拦截器中实现请求体缓存功能
     */
    private CachedBodyHttpServletRequest createCachedBodyRequest(HttpServletRequest request) throws IOException {
        // 如果已经是包装的请求，直接返回
        if (request instanceof CachedBodyHttpServletRequest cachedRequest) {
            return cachedRequest;
        }

        // 创建新的包装器
        return new CachedBodyHttpServletRequest(request);
    }

    /**
     * 解析请求体中的认证参数
     */
    private QiyuCrmRequest parseRequestBody(String requestBody) {
        try {
            if (!StringUtils.hasText(requestBody)) {
                return new QiyuCrmRequest();
            }

            // 尝试解析为JSON
            return objectMapper.readValue(requestBody, QiyuCrmRequest.class);

        } catch (Exception e) {
            LogUtils.warn(log, "解析请求体失败: {}", e.getMessage());
            return new QiyuCrmRequest();
        }
    }

    /**
     * 写入认证失败响应
     */
    private void writeAuthFailureResponse(HttpServletResponse response) throws IOException {
        QiyuCrmResponse errorResponse = new QiyuCrmResponse();
        errorResponse.setRlt(1);
        errorResponse.setMessage("Authentication failed");

        writeJsonResponse(response, errorResponse, HttpServletResponse.SC_OK);
    }

    /**
     * 写入系统错误响应
     */
    private void writeSystemErrorResponse(HttpServletResponse response) throws IOException {
        QiyuCrmResponse errorResponse = new QiyuCrmResponse();
        errorResponse.setRlt(500);
        errorResponse.setMessage("System error");

        writeJsonResponse(response, errorResponse, HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
    }

    /**
     * 写入JSON响应
     */
    private void writeJsonResponse(HttpServletResponse response, Object responseObject, int statusCode) throws IOException {
        response.setStatus(statusCode);
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        response.setCharacterEncoding(StandardCharsets.UTF_8.name());

        var jsonResponse = objectMapper.writeValueAsString(responseObject);
        var writer = response.getWriter();
        writer.write(jsonResponse);
        writer.flush();
    }
}
