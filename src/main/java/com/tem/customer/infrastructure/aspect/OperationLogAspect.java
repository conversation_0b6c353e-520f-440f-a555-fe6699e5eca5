package com.tem.customer.infrastructure.aspect;

import com.iplatform.common.utils.LogUtils;
import com.tem.customer.shared.annotation.OperationLog;
import com.tem.customer.service.system.OperationLogService;
import com.tem.customer.shared.utils.SafeSpelUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

/**
 * 操作日志AOP切面
 * 自动记录标注了@OperationLog注解的方法的操作日志
 *
 * <AUTHOR>
 * @since 2025-06-23
 */
@Slf4j
@Aspect
@Component
@Order(100)
@RequiredArgsConstructor
public class OperationLogAspect {

    private final OperationLogService operationLogService;

    /**
     * 定义切点：所有标注了@OperationLog注解的方法
     */
    @Pointcut("@annotation(com.tem.customer.shared.annotation.OperationLog)")
    public void operationLogPointcut() {
    }

    /**
     * 环绕通知：记录操作日志
     *
     * @param joinPoint 连接点
     * @return 方法执行结果
     * @throws Throwable 方法执行异常
     */
    @Around("operationLogPointcut()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        long startTime = System.currentTimeMillis();
        Object result = null;
        Throwable exception = null;

        try {
            // 执行目标方法
            result = joinPoint.proceed();
            return result;
        } catch (Throwable e) {
            exception = e;
            throw e;
        } finally {
            try {
                // 记录操作日志
                recordOperationLog(joinPoint, result, exception, startTime);
            } catch (Exception e) {
                // 记录日志失败不应影响业务执行
                LogUtils.error(log, "记录操作日志失败", e);
            }
        }
    }

    /**
     * 记录操作日志
     *
     * @param joinPoint 连接点
     * @param result    方法执行结果
     * @param exception 方法执行异常
     * @param startTime 开始时间
     */
    private void recordOperationLog(ProceedingJoinPoint joinPoint, Object result, Throwable exception, long startTime) {
        try {
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            Method method = signature.getMethod();
            OperationLog annotation = method.getAnnotation(OperationLog.class);

            if (annotation == null) {
                return;
            }

            // 计算执行时间
            int executionTime = (int) (System.currentTimeMillis() - startTime);

            // 解析业务ID
            Long businessId = parseBusinessId(annotation, joinPoint, result);

            // 解析目标企业ID
            Long targetPartnerId = parseTargetPartnerId(annotation, joinPoint, result);

            // 构建操作描述
            String description = buildDescription(annotation, exception);

            // 记录日志
            if (annotation.async()) {
                operationLogService.recordLogAsync(
                        annotation.businessType(),
                        businessId,
                        annotation.operationType(),
                        description,
                        targetPartnerId,
                        executionTime
                );
            } else {
                operationLogService.recordLog(
                        annotation.businessType(),
                        businessId,
                        annotation.operationType(),
                        description,
                        targetPartnerId,
                        executionTime
                );
            }

            if (log.isDebugEnabled()) {
                LogUtils.info(log, "记录操作日志: 业务类型={}, 操作类型={}, 描述={}, 执行时间={}ms",
                        annotation.businessType(), annotation.operationType(), description, executionTime);
            }

        } catch (Exception e) {
            LogUtils.error(log, "处理操作日志记录失败", e);
        }
    }

    /**
     * 通用的ID解析方法
     *
     * @param expression SpEL表达式
     * @param joinPoint  连接点
     * @param result     方法执行结果
     * @return 解析出的ID
     */
    private Long parseIdFromExpression(String expression, ProceedingJoinPoint joinPoint, Object result) {
        if (expression == null || expression.trim().isEmpty()) {
            return null;
        }

        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        Object[] args = joinPoint.getArgs();

        return SafeSpelUtil.parseLongExpression(expression, method, args, result);
    }

    /**
     * 解析业务ID
     *
     * @param annotation 注解
     * @param joinPoint  连接点
     * @param result     方法执行结果
     * @return 业务ID
     */
    private Long parseBusinessId(OperationLog annotation, ProceedingJoinPoint joinPoint, Object result) {
        return parseIdFromExpression(annotation.businessIdExpression(), joinPoint, result);
    }

    /**
     * 解析目标企业ID
     *
     * @param annotation 注解
     * @param joinPoint  连接点
     * @param result     方法执行结果
     * @return 目标企业ID
     */
    private Long parseTargetPartnerId(OperationLog annotation, ProceedingJoinPoint joinPoint, Object result) {
        return parseIdFromExpression(annotation.targetPartnerIdExpression(), joinPoint, result);
    }

    /**
     * 构建操作描述
     *
     * @param annotation 注解
     * @param exception  异常
     * @return 操作描述
     */
    private String buildDescription(OperationLog annotation, Throwable exception) {
        String description = annotation.description();

        if (exception != null) {
            // 只标记失败状态，不拼接具体异常信息避免描述过长
            description += " (执行失败)";
        }

        return description;
    }
}
