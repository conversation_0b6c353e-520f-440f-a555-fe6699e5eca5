package org.springframework.security.core.context;

import org.springframework.security.core.Authentication;
import org.springframework.util.ObjectUtils;

import java.io.Serial;

/**
 * 兼容版本的SecurityContextImpl实现类
 * 解决Spring Security版本升级导致的serialVersionUID不兼容问题
 *
 *
 * 重要说明：
 * - 此类位于org.springframework.security.core.context包下，会覆盖Spring Security原生的SecurityContextImpl
 * - 将serialVersionUID设置为550，与Redis中存储的旧版本数据保持兼容
 * - 保持与Spring Security原生SecurityContextImpl完全相同的功能和行为
 *
 * 问题背景：
 * - Spring Security 6.x版本的SecurityContextImpl的serialVersionUID为620
 * - Redis中存储的旧版本数据serialVersionUID为550
 * - 导致反序列化失败：java.io.InvalidClassException
 *
 * 解决方案：
 * - 通过包路径覆盖，替换Spring Security原生的SecurityContextImpl
 * - 设置serialVersionUID为550，保持与旧版本的序列化兼容性
 * - 维持Spring Security的所有功能不变
 *
 * <AUTHOR>
 * @since 2025-06-24
 */
public class SecurityContextImpl implements SecurityContext {

    /**
     * 设置serialVersionUID为550，与旧版本保持兼容
     * 这是解决序列化兼容性问题的关键
     */
    @Serial
    private static final long serialVersionUID = 550L;

    private Authentication authentication;

    public SecurityContextImpl() {
    }

    public SecurityContextImpl(Authentication authentication) {
        this.authentication = authentication;
    }

    public boolean equals(Object obj) {
        if (obj instanceof SecurityContextImpl other) {
            if (this.getAuthentication() == null && other.getAuthentication() == null) {
                return true;
            }

            if (this.getAuthentication() != null && other.getAuthentication() != null && this.getAuthentication().equals(other.getAuthentication())) {
                return true;
            }
        }

        return false;
    }

    public Authentication getAuthentication() {
        return this.authentication;
    }

    public int hashCode() {
        return ObjectUtils.nullSafeHashCode(this.authentication);
    }

    public void setAuthentication(Authentication authentication) {
        this.authentication = authentication;
    }

    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(this.getClass().getSimpleName()).append(" [");
        if (this.authentication == null) {
            sb.append("Null authentication");
        } else {
            sb.append("Authentication=").append(this.authentication);
        }

        sb.append("]");
        return sb.toString();
    }
}
