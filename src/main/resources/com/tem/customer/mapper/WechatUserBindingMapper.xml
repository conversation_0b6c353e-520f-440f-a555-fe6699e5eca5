<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tem.customer.repository.mapper.WechatUserBindingMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tem.customer.repository.entity.WechatUserBinding">
        <id column="id" property="id"/>
        <result column="partner_id" property="partnerId"/>
        <result column="union_id" property="unionId"/>
        <result column="source_type" property="sourceType"/>
        <result column="source_app_id" property="sourceAppId"/>
        <result column="user_id" property="userId"/>
        <result column="chat_id" property="chatId"/>
        <result column="status" property="status"/>
        <result column="remark" property="remark"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="deleted" property="deleted"/>
        <result column="version" property="version"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        partner_id,
        union_id,
        source_type,
        source_app_id,
        user_id,
        chat_id,
        status,
        remark,
        create_time,
        update_time,
        create_by,
        update_by,
        deleted,
        version
    </sql>

    <!-- 根据企业ID查询绑定关系列表 -->
    <select id="selectByPartnerId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_wechat_user_binding
        WHERE partner_id = #{partnerId}
          AND deleted = 0
        ORDER BY create_time DESC
    </select>

    <!-- 根据企业ID和用户ID查询绑定关系 -->
    <select id="selectByPartnerIdAndUserId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_wechat_user_binding
        WHERE partner_id = #{partnerId}
          AND user_id = #{userId}
          AND deleted = 0
        ORDER BY create_time DESC
    </select>


    <!-- 根据UnionID查询绑定关系 -->
    <select id="selectByUnionId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_wechat_user_binding
        WHERE  union_id = #{unionId}
          AND deleted = 0
        LIMIT 1
    </select>

    <!-- 根据来源类型查询绑定关系列表 -->
    <select id="selectBySourceType" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_wechat_user_binding
        WHERE partner_id = #{partnerId}
          AND source_type = #{sourceType}
          AND deleted = 0
        ORDER BY create_time DESC
    </select>


    <!-- 检查UnionID是否已存在（排除指定ID） -->
    <select id="existsByUnionIdExcludeId" resultType="boolean">
        SELECT COUNT(1) > 0
        FROM t_wechat_user_binding
        WHERE partner_id = #{partnerId}
          AND union_id = #{unionId}
          AND deleted = 0
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- 根据企业ID统计绑定关系数量 -->
    <select id="countByPartnerId" resultType="int">
        SELECT COUNT(1)
        FROM t_wechat_user_binding
        WHERE partner_id = #{partnerId}
          AND deleted = 0
    </select>

    <!-- 根据企业ID统计有效绑定关系数量 -->
    <select id="countValidByPartnerId" resultType="int">
        SELECT COUNT(1)
        FROM t_wechat_user_binding
        WHERE partner_id = #{partnerId}
          AND status = 1
          AND deleted = 0
    </select>

    <!-- 根据微信群ID查询绑定关系列表 -->
    <select id="selectByChatId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_wechat_user_binding
        WHERE chat_id = #{chatId}
          AND deleted = 0
        ORDER BY create_time DESC
    </select>

    <!-- 根据企业ID和微信群ID查询绑定关系列表 -->
    <select id="selectByPartnerIdAndChatId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_wechat_user_binding
        WHERE partner_id = #{partnerId}
          AND chat_id = #{chatId}
          AND deleted = 0
        ORDER BY create_time DESC
    </select>

    <!-- 根据企业ID和用户ID查询用户所在的微信群绑定关系 -->
    <select id="selectGroupsByUserId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_wechat_user_binding
        WHERE partner_id = #{partnerId}
          AND user_id = #{userId}
          AND chat_id IS NOT NULL
          AND deleted = 0
        ORDER BY create_time DESC
    </select>

    <!-- 根据微信群ID和用户ID查询绑定关系 -->
    <select id="selectByChatIdAndUserId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_wechat_user_binding
        WHERE chat_id = #{chatId}
          AND user_id = #{userId}
          AND deleted = 0
        LIMIT 1
    </select>

    <!-- 根据微信群ID统计绑定关系数量 -->
    <select id="countByChatId" resultType="int">
        SELECT COUNT(1)
        FROM t_wechat_user_binding
        WHERE chat_id = #{chatId}
          AND deleted = 0
    </select>

    <!-- 根据微信群ID统计有效绑定关系数量 -->
    <select id="countValidByChatId" resultType="int">
        SELECT COUNT(1)
        FROM t_wechat_user_binding
        WHERE chat_id = #{chatId}
          AND status = 1
          AND deleted = 0
    </select>

    <!-- 检查微信群和用户绑定关系是否已存在（排除指定ID） -->
    <select id="existsByChatIdAndUserIdExcludeId" resultType="boolean">
        SELECT COUNT(1) > 0
        FROM t_wechat_user_binding
        WHERE chat_id = #{chatId}
          AND user_id = #{userId}
          AND deleted = 0
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>


    <!-- 根据UnionID全局查询绑定关系（不限制企业ID） -->
    <select id="selectByUnionIdGlobal" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_wechat_user_binding
        WHERE union_id = #{unionId}
          AND deleted = 0
          AND status = 1
        ORDER BY update_time DESC, create_time DESC
        LIMIT 1
    </select>

</mapper>
