<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tem.customer.repository.mapper.OperationLogMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tem.customer.repository.entity.OperationLog">
        <id column="id" property="id"/>
        <result column="business_type" property="businessType"/>
        <result column="business_id" property="businessId"/>
        <result column="operation_type" property="operationType"/>
        <result column="operation_desc" property="operationDesc"/>
        <result column="operator_id" property="operatorId"/>
        <result column="operator_name" property="operatorName"/>
        <result column="operator_username" property="operatorUsername"/>
        <result column="partner_id" property="partnerId"/>
        <result column="target_partner_id" property="targetPartnerId"/>
        <result column="ip_address" property="ipAddress"/>
        <result column="user_agent" property="userAgent"/>
        <result column="request_uri" property="requestUri"/>
        <result column="request_method" property="requestMethod"/>
        <result column="execution_time" property="executionTime"/>
        <result column="create_time" property="createTime"/>
        <result column="create_by" property="createBy"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        business_type,
        business_id,
        operation_type,
        operation_desc,
        operator_id,
        operator_name,
        operator_username,
        partner_id,
        target_partner_id,
        ip_address,
        user_agent,
        request_uri,
        request_method,
        execution_time,
        create_time,
        create_by
    </sql>

    <!-- 根据业务类型和业务ID查询操作日志列表 -->
    <select id="selectByBusinessTypeAndId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_operation_log
        WHERE business_type = #{businessType}
          AND business_id = #{businessId}
        ORDER BY create_time DESC
    </select>

    <!-- 根据目标企业ID查询操作日志列表 -->
    <select id="selectByTargetPartnerId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_operation_log
        WHERE target_partner_id = #{targetPartnerId}
        ORDER BY create_time DESC
    </select>

    <!-- 根据操作人ID查询操作日志列表 -->
    <select id="selectByOperatorId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_operation_log
        WHERE operator_id = #{operatorId}
        ORDER BY create_time DESC
    </select>

    <!-- 分页查询操作日志 -->
    <select id="selectPageWithConditions" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_operation_log
        <where>
            <if test="businessType != null and businessType != ''">
                AND business_type = #{businessType}
            </if>
            <if test="operationType != null and operationType != ''">
                AND operation_type = #{operationType}
            </if>
            <if test="targetPartnerId != null">
                AND target_partner_id = #{targetPartnerId}
            </if>
            <if test="operatorId != null">
                AND operator_id = #{operatorId}
            </if>
            <if test="startTime != null">
                AND create_time >= #{startTime}
            </if>
            <if test="endTime != null">
                AND create_time &lt;= #{endTime}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

    <!-- 统计指定时间范围内的操作日志数量 -->
    <select id="countByTimeRange" resultType="int">
        SELECT COUNT(1)
        FROM t_operation_log
        WHERE create_time >= #{startTime}
          AND create_time &lt;= #{endTime}
    </select>

    <!-- 删除指定时间之前的操作日志 -->
    <delete id="deleteByCreateTimeBefore">
        DELETE FROM t_operation_log
        WHERE create_time &lt; #{beforeTime}
    </delete>

    <!-- 根据业务类型统计操作日志数量 -->
    <select id="countByBusinessType" resultType="int">
        SELECT COUNT(1)
        FROM t_operation_log
        WHERE business_type = #{businessType}
    </select>

    <!-- 根据操作类型统计操作日志数量 -->
    <select id="countByOperationType" resultType="int">
        SELECT COUNT(1)
        FROM t_operation_log
        WHERE operation_type = #{operationType}
    </select>
</mapper>
