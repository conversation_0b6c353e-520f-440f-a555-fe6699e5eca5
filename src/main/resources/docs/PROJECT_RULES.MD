# 项目规则
## 项目概述
> 项目概述可以使用AI总结，比如：总结项目信息和技术栈
>

+ **模块名称**: customer-admin-web
+ **功能定位**: 客服管理后台系统
+ **数据库**: MySQL + MyBatis
+ **技术栈**: JDK 21 + Spring Boot 3.5.0 + Dubbo 3.3.5 + MySQL + MyBatis-Plus + Redis + Sa-token+ Apollo配置中心

## 技术栈详情
+ **JDK版本**: 21
+ **Spring Boot版本**: 3.5.0
+ **Dubbo版本**: 3.3.5
+ **数据库**: MySQL
+ **ORM框架**: MyBatis-Plus 3.5.12
+ **缓存**: redis
+ **redis客户端**：Redisson 3.47.0
+ **构建工具**: Maven
+ **对象映射**：MapStruct 1.6.3
+ **权限认证：**Sa-token

## 编译构建
```plain
mvn -Dmaven.test.skip clean package install -Puat
```

## 开发规范
### 包结构规范
```plain
src/main/java/com/tem/customer/
├── CustomerAdminWebApplication.java    # 启动类
├── controller/                         # 控制器层
│   ├── auth/                          # 认证相关
│   ├── partner/                       # 合作伙伴管理
│   ├── qiyu/                          # 七鱼CRM集成
│   ├── system/                        # 系统管理
│   ├── third/                         # 第三方接口
│   └── wechat/                        # 微信相关
├── infrastructure/                     # 基础设施层
│   ├── config/                        # 配置类
│   ├── auth/                          # 认证组件
│   ├── filter/                        # 过滤器
│   └── interceptor/                   # 拦截器
├── model/                             # 数据模型
│   ├── dto/                           # 数据传输对象
│   ├── vo/                            # 视图对象
│   └── convert/                       # 对象转换
├── repository/                        # 数据访问层
│   ├── entity/                        # 实体类
│   └── mapper/                        # Mapper接口
├── service/                           # 业务服务层
└── shared/                            # 共享组件
    ├── common/                        # 通用类
    ├── utils/                         # 工具类
    └── generator/                     # 代码生成器
```



### 代码规范
+ **编码格式**: UTF-8
+ **缩进**: 使用4个空格
+ **类命名**: PascalCase (UserService, UserServiceImpl)
+ **方法命名**: camelCase (getUserById, updateUserStatus)
+ **常量命名**: UPPER_SNAKE_CASE (MAX_RETRY_COUNT)

### Lombok使用规范
```plain
@Slf4j                    // 日志
@Data                     // getter/setter/equals/hashCode/toString
@NoArgsConstructor        // 无参构造器
@AllArgsConstructor       // 全参构造器
@Builder                  // 建造者模式
```

### 实体 transform 规范
使用mapstruct框架来处理实体间的转换

### 日志规范
项目使用@Slf4j注解和LogUtils类处理日志

```plain
@Slf4j
public class UserService {
    public void updateUser(User user) {
        log.info("更新用户信息: userId={}", user.getId());
        log.debug("用户详细信息: {}", JSON.toJSONString(user));
        log.error("更新用户失败: userId={}", user.getId(), e);
    }
}
```

## 测试规范
### 测试框架
+ 单元测试使用Spock框架编写，需要mock静态方法时配合Mockito使用

### 测试覆盖率要求
+ 核心业务逻辑测试覆盖率 ≥ 80%
+ 所有Dubbo接口必须有集成测试

## 部署规范
### 环境配置
+ **开发环境**: dev
+ **测试环境**: test
+ **生产环境**: prod

### 端口配置
+ **应用端口**: 8244

## 依赖管理
### Maven规范
+ 统一在父pom.xml中管理版本号
+ 使用dependencyManagement管理依赖版本
+ 禁止在子模块中指定版本号

## 配置管理规范
### Apollo配置
+ **配置工具**: 项目中使用Apollo配置中心
+ **封装工具类**: 使用封装的Config类获取配置
+ **Config类位置**: `com.iplatform.common.Config`

### Config工具类详细说明
#### 支持的配置源
1. **本地配置文件**: `/config.properties` (classpath根路径)
2. **Apollo配置中心**: 支持`global`和`application`命名空间
3. **配置优先级**: Apollo配置会覆盖本地配置文件

#### 主要方法
```plain
// 获取字符串配置
String value = Config.getString("key");
// 获取整型配置
Integer intValue = Config.getInt("key");
// 获取长整型配置
Long longValue = Config.getLong("key");
// 获取双精度配置
Double doubleValue = Config.getDouble("key");
// 获取所有配置
Map<String, String> allConfigs = Config.getAll();
```

#### 配置变更监听
```plain
// 添加配置变更监听器
Config.addChangeListener("listenerName", (key, newValue, oldValue) -> {
    // 处理配置变更逻辑
    System.out.println("配置项 " + key + " 从 " + oldValue + " 变更为 " + newValue);
});
```

#### 使用示例
```plain
// 获取数据库连接配置
String dbUrl = Config.getString("jdbc.url");
String dbUsername = Config.getString("jdbc.username");
Integer dbMaxConnections = Config.getInt("jdbc.maxConnections");

// 获取业务配置
String appName = Config.getString("app.name");
Long timeout = Config.getLong("service.timeout");
```



####  Page<T> - 分页对象
+ **用途**: 分页一般使用`Page<T>`
+ **包路径**: `com.iplatform.common.Page`

##### Page结构说明
```plain
public class Page<E> {
    private int start;          // 从那条记录开始
    private int size = 10;      // 每页页数
    private int total;          // 总共条数
    private int currentPage;    // 当前页数
    private int totalPage;      // 总共页数
    private List<E> list;       // 数据列表
}
```

##### 主要构造方法
```plain
// 默认构造方法
Page<UserDto> page = new Page<>();
// 通过页码和页大小构造
Page<UserDto> page = new Page<>(userList, currentPage, pageSize, total);
// 通过起始索引构造
Page<UserDto> page = new Page<>(startIndex, range, userList, total);
```

##### 使用示例
```plain
@Service
public class UserServiceImpl implements UserService {
    
    @Override
    public ResponseDto<Page<UserDto>> queryUsers(QueryDto<UserQueryCondition> queryDto) {
        try {
            // 获取查询条件
            UserQueryCondition condition = queryDto.getCondition();
            int start = queryDto.getStart();
            int size = queryDto.getSize();
            
            // 查询总数
            int total = userDao.countByCondition(condition);
            
            // 查询数据列表
            List<UserDto> userList = userDao.selectByCondition(condition, start, size);
            
            // 构造分页对象
            Page<UserDto> page = new Page<>(userList, 
                                          start / size + 1,  // 当前页
                                          size,               // 页大小
                                          total);             // 总数
            
            return ResponseDto.success(page);
        } catch (Exception e) {
            log.error("分页查询用户失败", e);
            return ResponseDto.error(BizResultCode.SYSTEM_ERROR);
        }
    }
}
```

####  QueryDto<T> - 分页查询条件
+ **用途**: 分页的查询条件一般使用`QueryDto<T>`
+ **包路径**: `com.iplatform.common.QueryDto`
+ **重要提醒**: 新接口尽量使用分页接口来查询

##### QueryDto结构说明
```plain
public class QueryDto<T> {
    private T condition;        // 查询条件对象
    private int start;          // 从第几条记录开始查询
    private int size;           // 每页条数
    private String sidx;        // 排序属性名
    private String sord = "desc"; // 排序方式：desc/asc
    private String field1;      // 备用字段1
    private String field2;      // 备用字段2
    private Long field3;        // 备用字段3
    private Integer field4;     // 备用字段4
}
```

##### 主要构造方法
```plain
// 默认构造方法
QueryDto<UserQueryCondition> queryDto = new QueryDto<>();
// 通过页码和页大小构造
QueryDto<UserQueryCondition> queryDto = new QueryDto<>(pageIndex, pageSize);
// 通过页码、页大小和排序构造
QueryDto<UserQueryCondition> queryDto = new QueryDto<>(pageIndex, pageSize, "createTime", "desc");
```

##### 使用示例
```plain
// 服务接口定义
@DubboService
public class UserServiceImpl implements UserService {
    @Override
    public ResponseDto<Page<UserDto>> queryUsers(QueryDto<UserQueryCondition> queryDto) {
        // 获取查询条件
        UserQueryCondition condition = queryDto.getCondition();
        // 分页参数
        int start = queryDto.getStart();
        int size = queryDto.getSize();
        // 排序参数
        String orderBy = queryDto.getSidx();
        String orderDirection = queryDto.getSord();
        // 执行查询逻辑...
        return ResponseDto.success(page);
    }
}

// 调用方使用示例
@RestController
public class UserController {
    @DubboReference
    private UserService userService;
    @PostMapping("/users/query")
    public ResponseDto<Page<UserDto>> queryUsers(@RequestBody UserQueryRequest request) {
        // 构造查询条件
        UserQueryCondition condition = new UserQueryCondition();
        condition.setUserName(request.getUserName());
        condition.setStatus(request.getStatus());
        // 构造分页查询对象
        QueryDto<UserQueryCondition> queryDto = new QueryDto<>(
            request.getPageIndex(),     // 页码
            request.getPageSize()       // 页大小
        );
        queryDto.setCondition(condition);
        queryDto.setSidx("createTime");    // 排序字段
        queryDto.setSord("desc");          // 排序方式
        // 调用服务
        return userService.queryUsers(queryDto);
    }
}
// 查询条件对象示例
public class UserQueryCondition implements Serializable {
    private String userName;
    private Integer status;
    private Date createTimeStart;
    private Date createTimeEnd;
    private Long partnerId;
    // getter/setter...
}
```



## 新表设计规范
### 必需字段规范
所有新表必须包含以下字段：

1. **时间字段**（必需）：```sql`create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',`update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'```
2. **ID字段类型**：
    - 主键ID：`BIGINT` 类型
    - 业务相关ID（如 `partner_id`、`org_id`、`user_id` 等）：`BIGINT` 类型

##  工具类
1. AmountUtil：金额工具类
    1. 分元转换：提供分和元之间的精确转换
    2. 多类型支持：支持 Long、BigDecimal、String 等多种数据类型
    3. 精度控制：统一使用四舍五入，保留2位小数
    4. 安全计算：提供安全的金额加减运算，自动处理null值
    5. 金额验证：检查金额是否为正数、零或负数
2. JacksonUtil: JSON工具类
3. RestClientUtils: Http客户端工具类
4. ThreadPoolUtil: 线程池工具类
    1. 统一线程池：提供统一的线程池访问接口
    2. TraceId传递：自动传递TraceId到异步任务
    3. 任务提交：支持Runnable和Callable任务
    4. 状态监控：获取线程池状态信息
    5. 自定义线程工厂：提供自定义线程工厂
5. UserContextUtil:用户上下文工具类
    1. 双模式兼容：兼容Cookie登录和Token登录
    2. 自动回退：优先使用ContextUtil，失败时回退到Sa-Token
    3. 请求上下文：HTTP请求、响应、会话操作
    4. 参数获取：获取请求参数和Session数据
    5. 企业ID获取：智能获取partnerId

